# 🚀 WordPress Release Automation - Quick Reference

## 📍 Location
```bash
cd /Users/<USER>/Sites/wp-release-automation
```

## ⚡ Quick Commands

### Single Plugin Releases
```bash
./prepare-release essential-addons-dev/better-payment 1.3.3
./prepare-release nhrrob-dev/nhrrob-movies 2.1.0
```

### Multi-Plugin Releases
```bash
./prepare-release better-payment-suite 1.3.3
./prepare-release nhrrob-suite 2.0.0
./prepare-release essential-addons-suite 6.4.0
```

### Testing (Dry Run)
```bash
./global-release-manager essential-addons-dev/better-payment 1.3.3 --dry-run
```

## 🎯 Available Targets

### Essential Addons Dev
- `essential-addons-dev/better-payment`
- `essential-addons-dev/better-payment-pro`
- `essential-addons-dev/essential-addons-elementor`
- `essential-addons-dev/essential-addons-for-elementor-lite`

### NHRROB Dev
- `nhrrob-dev/nhrrob-movies`
- `nhrrob-dev/nhrrob-core-contributions`
- `nhrrob-dev/nhrrob-options-table-manager`
- `nhrrob-dev/nhrrob-frequent-update-manager`
- `nhrrob-dev/nhrrob-hide-admin-notice`
- `nhrrob-dev/nhrrob-trello-skin-for-fluent-boards`

### Release Groups
- `better-payment-suite`
- `essential-addons-suite`
- `nhrrob-suite`
- `all-essential-addons`
- `all-nhrrob`

## 🤖 AI Commands

Tell your AI assistant:
- "Prepare release for essential-addons-dev/better-payment 1.3.4"
- "Prepare release for nhrrob-dev/nhrrob-movies 2.1.0"
- "Prepare release for better-payment-suite 1.3.3"

## 🔧 Advanced Options

```bash
./global-release-manager <target> <version> [options]
```

Options:
- `--auto-changelog` (default: true)
- `--auto-merge`
- `--create-tag`
- `--dry-run`

## 🔗 Global Access Setup

```bash
./setup-aliases.sh
# Restart terminal, then use from anywhere:
prepare-release essential-addons-dev/better-payment 1.3.3
```

## 📋 Workflow

1. Navigate: `cd /Users/<USER>/Sites/wp-release-automation`
2. Release: `./prepare-release <target> <version>`
3. Review PR on GitHub
4. Merge & Deploy

## 🆘 Help

```bash
./prepare-release --help
./global-release-manager --help
```
