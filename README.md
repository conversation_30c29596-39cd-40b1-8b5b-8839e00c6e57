# 🌍 Universal WordPress Plugin Release Automation

**Location:** `/Users/<USER>/Sites/wp-release-automation/`

A universal release automation system that manages WordPress plugin releases across multiple development projects in your Sites folder.

## 🚀 Quick Start Guide

### Basic Usage
```bash
cd /Users/<USER>/Sites/wp-release-automation
./prepare-release <project>/<plugin> <version>
```

### Examples
```bash
# Single plugin releases
./prepare-release essential-addons-dev/better-payment 1.3.3
./prepare-release nhrrob-dev/nhrrob-movies 2.1.0

# Multi-plugin releases (Release Groups)
./prepare-release better-payment-suite 1.3.3      # Both Better Payment plugins
./prepare-release nhrrob-suite 2.0.0              # All NHRROB plugins
./prepare-release essential-addons-suite 6.4.0    # Both Essential Addons plugins
```

## 📁 System Overview

```
/Users/<USER>/Sites/
├── wp-release-automation/              # 🎯 THIS DIRECTORY
│   ├── prepare-release                 # Simple release script (recommended)
│   ├── global-release-manager         # Advanced release manager
│   ├── global-release-config.json     # Configuration for all projects
│   ├── setup-aliases.sh               # Optional global aliases setup
│   └── README.md                      # This documentation
├── essential-addons-dev/              # Your Essential Addons project
└── nhrrob-dev/                        # Your NHRROB project
```

## 🎯 What This System Does

### ✅ **Automated Tasks:**
1. **Auto-generates changelog** from recent git commits
2. **Updates version numbers** in plugin header and constants
3. **Updates stable tag** in readme.txt
4. **Commits and pushes** changes to dev branch
5. **Creates pull request** to master/main branch

### ✅ **Multi-Project Support:**
- **essential-addons-dev** project (4 plugins)
- **nhrrob-dev** project (6 plugins)
- **Release groups** for batch releases

### ✅ **Safety Features:**
- Version validation (semantic versioning)
- Project path validation
- Git repository validation
- Branch checking and switching
- Confirmation prompts
- Dry run mode for testing

## 🛠️ Available Scripts

### 1. `prepare-release` (Recommended)
**Simple one-command release**
```bash
./prepare-release <target> <version>
```

### 2. `global-release-manager` (Advanced)
**Full-featured with options**
```bash
./global-release-manager <target> <version> [options]
```

**Options:**
- `--auto-changelog` - Auto-generate changelog (default: true)
- `--auto-merge` - Auto-merge PR after creation
- `--create-tag` - Create git tag after merge
- `--dry-run` - Show what would be done without making changes

## 📦 Configured Plugins

### Essential Addons Dev Project
- `essential-addons-dev/better-payment`
- `essential-addons-dev/better-payment-pro`
- `essential-addons-dev/essential-addons-elementor`
- `essential-addons-dev/essential-addons-for-elementor-lite`

### NHRROB Dev Project
- `nhrrob-dev/nhrrob-movies`
- `nhrrob-dev/nhrrob-core-contributions`
- `nhrrob-dev/nhrrob-options-table-manager`
- `nhrrob-dev/nhrrob-frequent-update-manager`
- `nhrrob-dev/nhrrob-hide-admin-notice`
- `nhrrob-dev/nhrrob-trello-skin-for-fluent-boards`

### Release Groups
- `better-payment-suite` - Both Better Payment plugins
- `essential-addons-suite` - Both Essential Addons plugins
- `nhrrob-suite` - All NHRROB plugins
- `all-essential-addons` - All Essential Addons project plugins
- `all-nhrrob` - All NHRROB project plugins

## 🤖 AI Assistant Integration

Tell your AI assistant:
> **"Prepare release for essential-addons-dev/better-payment 1.3.4"**
> **"Prepare release for nhrrob-dev/nhrrob-movies 2.1.0"**
> **"Prepare release for better-payment-suite 1.3.3"**

The assistant will automatically run the commands for you!

## 🔄 Typical Workflow

1. **Navigate to system:**
   ```bash
   cd /Users/<USER>/Sites/wp-release-automation
   ```

2. **Prepare release:**
   ```bash
   ./prepare-release essential-addons-dev/better-payment 1.3.3
   ```

3. **Review the pull request** on GitHub

4. **Merge the pull request** when ready

5. **Deploy to WordPress.org** (if applicable)

## 🔗 Optional Global Access

Run once for global access from anywhere:
```bash
./setup-aliases.sh
```

Then restart terminal and use:
```bash
prepare-release essential-addons-dev/better-payment 1.3.3  # From anywhere!
```

## 🚨 Troubleshooting

### Common Issues
- **Permission denied:** `chmod +x prepare-release global-release-manager`
- **GitHub CLI not authenticated:** `gh auth login`
- **Plugin not found:** Check `global-release-config.json` configuration

### Getting Help
```bash
./prepare-release --help
./global-release-manager --help
```

## ⚙️ Configuration

Edit `global-release-config.json` to:
- Add new projects
- Add new plugins
- Create new release groups
- Modify repository settings

## 🎯 Benefits

- **Centralized** - One location manages all projects
- **Universal** - Works with any WordPress project in Sites folder
- **Valet/Herd Compatible** - Perfect for local development
- **Time-saving** - Automates entire release process
- **Safe** - Built-in validations and confirmations
- **Scalable** - Easy to add new projects and plugins

---

**Perfect for managing WordPress plugin releases across multiple development projects! 🌍🚀**
