#!/bin/bash

# Setup script to add aliases for easy access to release automation
# Run this once to set up global access to the release system

echo "🔧 Setting up WordPress Release Automation aliases..."

# Detect shell
if [[ "$SHELL" == *"zsh"* ]]; then
    SHELL_RC="$HOME/.zshrc"
elif [[ "$SHELL" == *"bash"* ]]; then
    SHELL_RC="$HOME/.bash_profile"
else
    SHELL_RC="$HOME/.profile"
fi

echo "📝 Adding aliases to $SHELL_RC"

# Add aliases to shell configuration
cat >> "$SHELL_RC" << 'EOF'

# WordPress Release Automation Aliases
alias prepare-release='cd /Users/<USER>/Sites/wp-release-automation && ./prepare-release'
alias release-manager='cd /Users/<USER>/Sites/wp-release-automation && ./global-release-manager'
alias wp-release='cd /Users/<USER>/Sites/wp-release-automation'

EOF

echo "✅ Aliases added to $SHELL_RC"
echo ""
echo "🎯 Available commands after restarting terminal:"
echo "  prepare-release <project>/<plugin> <version>"
echo "  release-manager <project>/<plugin> <version> [options]"
echo "  wp-release  # Navigate to release automation directory"
echo ""
echo "Examples:"
echo "  prepare-release essential-addons-dev/better-payment 1.3.3"
echo "  prepare-release nhrrob-dev/nhrrob-movies 2.1.0"
echo "  prepare-release better-payment-suite 1.3.3"
echo ""
echo "🔄 Restart your terminal or run: source $SHELL_RC"
