/*!***********************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./node_modules/postcss-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/css/admin.scss ***!
  \***********************************************************************************************************************************************************************************/
.better-payment_page_better-payment-transactions .swal2-popup {
  font-size: 1em;
}

.bp-refund-buttons-wrap .button {
  margin-bottom: 5px;
}

.better-payment .bp-view-receipt .bp-modal-header {
  background: #352FCB;
  padding: 50px 50px 0;
}

.better-payment .bp-view-receipt .bp-modal-footer {
  background: #352FCB;
  padding: 15px;
  text-align: center;
}

.better-payment .bp-view-receipt .bp-modal-header-inner {
  padding: 30px;
  background: #fff;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

.better-payment .bp-view-receipt .bp-modal-content-inner {
  background: #fff;
}

.better-payment .bp-view-receipt .bp-modal-content {
  padding: 50px;
  background: #fff;
}

.better-payment .bp-view-receipt .bp-modal-content-inner .column-content {
  background: #F7F9FB;
  padding: 20px 30px;
}

.better-payment .bp-view-receipt .bp-modal-header-inner h4 {
  color: #352FCB;
  font-size: 25px;
  margin-bottom: 5px;
}

.better-payment .bp-view-receipt .bp-modal-header-inner h4 span {
  color: #A09CFF;
}

.better-payment .bp-view-receipt .bp-modal-header-inner p {
  color: #2A3256;
  font-size: 15px;
}

.better-payment .bp-view-receipt .bp-modal-header-inner p.bp-modal-header-subtitle {
  color: #575495;
  font-size: 18px;
}

.better-payment .bp-view-receipt .bp-modal-header .paid_via_selected span {
  border-color: #6e58f7 !important;
}

.better-payment .bp-view-receipt .bp-modal-header .payment__getway {
  padding: 0;
  margin: 0 0 10px;
  border-bottom: none;
}

.better-payment .bp-view-receipt .bp-modal-header .pay__by {
  padding: 0;
  margin: 0;
}

.better-payment .bp-view-receipt .bp-modal-header-inner p {
  margin-bottom: 10px;
}

.better-payment .bp-view-receipt .bp-modal-content .gray-box {
  color: #656B86;
}

.better-payment-print-receipt-btn {
  padding-top: 8px;
}

.better-payment .is-hidden {
  display: none !important;
}

/* Page: Analytics Starts */
.better-payment .select2.analytic-reports-select2 {
  font-size: 2rem !important;
}

.better-payment .button.analytics-select-custom-button {
  background: #fff;
  border: 1px solid #CFD7E7 !important;
  color: #9095A2;
  font-weight: normal !important;
  width: 100%;
}

.better-payment .button.analytics-select-custom-button:hover {
  border: 1px solid #735EF8 !important;
  color: #735EF8;
}

.better-payment .button.analytics-select-custom-button:hover svg path {
  fill: #735EF8;
}

.better-payment .button.analytics-select-custom-button::before,
.better-payment .button.analytics-select-custom-button::after {
  background: unset;
}

.better-payment .analytics-select-custom-button-wrap .checkbox input {
  border: 1px solid #C0C3D3;
  border-radius: 3px;
  min-width: 15px;
  height: 15px;
}

.better-payment .analytics-select-custom-button-wrap {
  position: relative;
}

.better-payment .analytics-select-custom-button-dropdown .checkbox input:checked::before {
  height: 20px;
  width: 20px;
}

.better-payment .analytics-select-custom-button-dropdown {
  position: absolute;
  right: 0;
  left: -30%;
}

/* Page: Analytics Ends */
/* Page: Settings => Licensing Starts */
.better-payment button.button__active.--verification-required {
  background: #fc0 !important;
  border-color: #fc0 !important;
  color: #fff !important;
}
.better-payment .bp-verification-input-container input {
  height: 55px;
  padding: 10px 20px;
  background-color: #fff;
  border-color: #dbdbdb;
  border-radius: 0.375em;
  color: #363636;
  width: 100%;
}
.better-payment .bp-verification-input-container button {
  padding: 12px 20px;
  border: 0;
}
.better-payment .bp-verification-msg {
  margin-top: 20px;
}
.better-payment .bp-verification-msg a {
  color: #5E2EFF;
}
.better-payment .bp-verification-msg span.bp-customer-email {
  color: #3c434a;
  font-weight: 600;
}
.better-payment .bp-verification-msg .short-description {
  padding: 10px;
  background: #f2f2f2;
  margin-bottom: 10px;
  border-radius: 5px;
}
.better-payment .bp-verification-msg .bp-verification-input-container {
  margin-top: 20px;
  padding-bottom: 20px;
  background: #f9f9fb;
  border-radius: 5px;
  padding: 30px;
}
.better-payment .bp-verification-input-container > div.bp-license-form-block {
  position: relative;
}
.better-payment .bp-verification-msg .bp-verification-input-container button {
  width: 150px;
  position: absolute !important;
  right: 6px;
  top: 6px;
}
.better-payment .bp-verification-msg .bp-verification-input-container p {
  margin-top: 20px;
  color: #3c434a;
}
.better-payment .bp-license-error-msg {
  background-color: #fceff1;
  font-weight: 400;
  color: #2c3438;
  padding: 10px;
  border-radius: 5px;
  text-align: center;
  margin-top: 20px;
}
.better-payment .bp-license-error-msg.notice-message {
  background: #fdf9e8;
}
.better-payment .go__premium .bp-verification-msg p,
.better-payment .go__premium .bp-license-error-msg {
  max-width: 100%;
}

/* Page: Settings => Licensing Ends */
@media screen and (min-width: 360px) and (max-width: 768px) {
  .better-payment .analytics-select-custom-button-dropdown {
    left: 0;
    z-index: 9999;
  }
}
@media screen and (min-width: 769px) {
  .better-payment .bp-modal .modal-card,
  .better-payment .bp-modal .modal-content {
    width: 740px;
  }
}
@media screen and (min-width: 992px) {
  .better-payment .bp-modal .modal-card,
  .better-payment .bp-modal .modal-content {
    width: 950px;
  }
  .better-payment .bp-modal .is-small .modal-card,
  .better-payment .bp-modal .is-small .modal-content {
    width: 550px;
  }
}
@media screen and (min-width: 769px) and (max-width: 991px) {
  .better-payment .analytics-page-content-header-boxes {
    display: block !important;
  }
  .better-payment .analytics-page-content-header-boxes > .column {
    display: block;
    flex: none;
    width: 50%;
    float: left;
  }
  .better-payment .analytics-report-chart-filter {
    display: block !important;
  }
  .better-payment .analytics-report-chart-filter .column {
    display: block;
    width: 100%;
  }
  .better-payment .analytics-select-custom-button-dropdown {
    left: 0;
  }
}
@media screen and (min-width: 992px) and (max-width: 1199px) {
  .better-payment .analytics-page-content-header-boxes {
    display: block !important;
  }
  .better-payment .analytics-page-content-header-boxes > .column {
    display: block;
    flex: none;
    width: 50%;
    float: left;
  }
  .better-payment .analytics-report-chart-filter {
    display: block !important;
  }
  .better-payment .analytics-report-chart-filter .column {
    display: block;
    width: 100%;
  }
  .better-payment .analytics-select-custom-button-dropdown {
    left: 0;
  }
}
