/*!************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].use[1]!./node_modules/postcss-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/css/common.scss ***!
  \************************************************************************************************************************************************************************************/
.better-payment a {
  color: inherit;
  transition: color 0.3s ease, background 0.3s ease, border 0.3s ease, box-shadow 0.3s ease;
  text-decoration: none;
  display: inline-block;
}
.better-payment label {
  margin-bottom: 0;
}
.better-payment h1,
.better-payment h2,
.better-payment h3,
.better-payment h4,
.better-payment h5,
.better-payment h6,
.better-payment p {
  margin: 0;
  padding: 0;
}
.better-payment h1,
.better-payment h2,
.better-payment h3,
.better-payment h4,
.better-payment h5,
.better-payment h6 {
  font-weight: 700;
  line-height: 1.3;
}
.better-payment img,
.better-payment video {
  max-width: 100%;
}
.better-payment .card-field {
  position: relative;
}
.better-payment .card-field .icons {
  position: absolute;
  right: 20px;
  bottom: 17px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.better-payment .better-payment-container {
  max-width: 1170px;
  margin: 0 auto;
  padding: 0 15px;
}
.better-payment .payment-button {
  height: unset;
}
.better-payment .bp-flex {
  display: flex;
}
.better-payment .bp-flex-1 {
  flex: 1;
}
.better-payment .bp-flex-3 {
  flex: 3;
}
.better-payment .bp-w-100 {
  width: 100%;
}
.better-payment .split-payment-text-wrap input,
.better-payment .split-payment-text-wrap select {
  border-radius: 8px;
  min-width: 75px;
  width: unset;
  height: 36px;
  background: transparent;
  padding: 0 10px;
  outline: none;
  font-family: "IBM Plex Sans", sans-serif;
}
.better-payment .layout-4-general {
  background: #fff;
  border-radius: 24px;
  box-shadow: 0px 2px 3px 0px rgba(32, 31, 80, 0.15);
  font-family: "IBM Plex Sans", sans-serif;
  padding: 50px;
  display: flex;
  gap: 32px;
  align-items: flex-start;
}
.better-payment .layout-4-general h1, .better-payment .layout-4-general h2, .better-payment .layout-4-general h3, .better-payment .layout-4-general h4, .better-payment .layout-4-general h5, .better-payment .layout-4-general h6 {
  font-family: "IBM Plex Sans", sans-serif;
}
.better-payment .layout-4-general .reversed {
  flex-direction: row-reverse;
}
.better-payment .layout-4-general .split-payment .payment-type-one-time {
  flex: 0 0 65% !important;
}
.better-payment .layout-4-general .payment-button {
  border-radius: 8px;
  background: #6E58F7;
  color: #FFF;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  width: 100%;
  padding: 15.5px 20px;
  border: 0 !important;
  outline: none !important;
  box-shadow: none;
  cursor: pointer;
}
.better-payment .layout-4-general .split-payment-text-wrap input,
.better-payment .layout-4-general .split-payment-text-wrap select {
  border: 1px solid #d3dbee;
}
.better-payment .layout-4-general .col-wrap {
  display: flex;
  gap: 16px;
}
.better-payment .layout-4-general .col-wrap .span-50 {
  flex: 1 1 calc(50% - 8px);
}
.better-payment .layout-4-general .check-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}
.better-payment .layout-4-general .check-item input {
  display: none;
}
.better-payment .layout-4-general .check-item .checkbox {
  border-radius: 4px;
  border: 2px solid #D3DBEE;
  background: #FFF;
  display: block;
  height: 20px;
  width: 20px;
  cursor: pointer;
  position: relative;
}
.better-payment .layout-4-general .check-item .checkbox::before {
  border-radius: 2px;
  background: #6E58F7;
  height: 12px;
  width: 12px;
  opacity: 0;
  transition: all 0.3s ease;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  content: "";
}
.better-payment .layout-4-general .check-item input:checked + .checkbox::before {
  opacity: 1;
}
.better-payment .layout-4-general .transaction-details-wrap {
  border-radius: 16px;
  background: #F4F4F8;
  padding: 32px;
  min-width: 340px;
  flex-basis: 340px;
}
.better-payment .layout-4-general .transaction-details-wrap .transaction-details-header {
  margin-bottom: 40px;
}
.better-payment .layout-4-general .transaction-details-wrap .transaction-details-header h3 {
  color: #2B2748;
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 10px;
}
.better-payment .layout-4-general .transaction-details-wrap .transaction-details-header p,
.better-payment .layout-4-general .split-payment-note-wrap span,
.better-payment .layout-4-general .transaction-details-wrap .transaction-details-footer li.split-payment-note-wrap span {
  color: #8088A6;
  font-size: 16px;
  font-weight: 500;
  word-break: break-word;
}
.better-payment .layout-4-general .transaction-details-wrap .transaction-details-footer ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.better-payment .layout-4-general .transaction-details-wrap .transaction-details-footer ul li {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.better-payment .layout-4-general .transaction-details-wrap .transaction-details-footer ul li:not(:last-child) {
  margin-bottom: 8px;
}
.better-payment .layout-4-general .transaction-details-wrap .transaction-details-footer ul li span {
  color: #48506D;
  font-size: 18px;
  font-weight: 500;
}
.better-payment .layout-4-general .transaction-details-wrap .transaction-details-footer ul li.discount .amount {
  color: #6E58F7;
}
.better-payment .layout-4-general .transaction-details-wrap .transaction-details-footer ul li .total-amount-text span {
  color: #6E58F7;
}
.better-payment .layout-4-general .transaction-details-wrap .transaction-details-footer ul li .total-amount-text span {
  color: #6E58F7;
}
.better-payment .layout-4-general .transaction-details-wrap .transaction-details-footer ul li.total {
  padding-top: 15px;
  margin-top: 20px;
}
.better-payment .layout-4-general .transaction-details-wrap .transaction-details-footer ul li.total .amount {
  color: #2B2748;
  font-size: 20px;
  font-weight: 600;
}
.better-payment .layout-4-general .general-form-wrap {
  border-radius: 16px;
  border: 2px solid #D3DBEE;
  background: #FFF;
  padding: 30px;
  flex: 1;
  width: 100%;
}
.better-payment .layout-4-general .general-form-wrap .payment-details-title {
  color: #2B2748;
  font-size: 32px;
  font-weight: 500;
  margin-bottom: 20px;
}
.better-payment .layout-4-general .general-form-wrap .payment-method-items {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}
.better-payment .layout-4-general .general-form-wrap .payment-method-items .payment-method-item {
  flex: 1;
}
.better-payment .layout-4-general .general-form-wrap .payment-method-items .payment-method-item input {
  display: none;
}
.better-payment .layout-4-general .general-form-wrap .payment-method-items .payment-method-item .payment-method-image-wrap {
  border-radius: 8px;
  background: #F4F4F8;
  padding: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 7px;
}
.better-payment .layout-4-general .general-form-wrap .payment-method-items .payment-method-item input:checked + .payment-method-image-wrap {
  border: 1px solid #6E58F7;
}
.better-payment .layout-4-general .general-form-wrap .payment-method-items .payment-method-item .payment-method-image-wrap span {
  display: inline-block;
}
.better-payment .layout-4-general .general-form-wrap .payment-method-items .payment-method-item .payment-method-image-wrap .payment-method-image {
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.better-payment .layout-4-general .general-form-wrap .payment-method-items .payment-method-item .payment-method-image-wrap .payment-method-image:not(.card-icon) {
  width: 100%;
}
.better-payment .layout-4-general .general-form-wrap .payment-method-items .payment-method-item .payment-method-image-wrap .payment-method-image img {
  max-height: 100%;
}
.better-payment .layout-4-general .general-form-wrap .payment-method-items .payment-method-item .payment-method-image-wrap .donation-form-header {
  color: #2B2748;
  font-size: 16px;
  font-weight: 600;
  display: block;
}
.better-payment .layout-4-general .general-form-wrap .form-group .label {
  color: #2B2748;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  display: inline-block;
}
.better-payment .layout-4-general .general-form-wrap .form-group .label .required,
.better-payment .layout-4-general .general-form-wrap .payment-amount-wrap .label .required {
  color: #e04f5f;
}
.better-payment .layout-4-general .general-form-wrap .form-group .form-control {
  border-radius: 8px;
  border: 2px solid #D3DBEE;
  width: 100%;
  height: 56px;
  padding: 0 20px;
  outline: none;
  font-size: 18px;
  font-weight: 500;
  color: #2B2748;
}
.better-payment .layout-4-general .general-form-wrap .form-group .form-control:focus {
  border: 2px solid #6E58F7;
}
.better-payment .layout-4-general .general-form-wrap .form-group .form-control::-webkit-input-placeholder {
  color: #8088A6;
}
.better-payment .layout-4-general .general-form-wrap .form-group .form-control::-moz-placeholder {
  color: #8088A6;
}
.better-payment .layout-4-general .general-form-wrap .form-group .form-control:-ms-input-placeholder {
  color: #8088A6;
}
.better-payment .layout-4-general .general-form-wrap .form-group .form-control:-moz-placeholder {
  color: #8088A6;
}
.better-payment .layout-4-general .general-form-wrap .payment-amount-wrap {
  margin-bottom: 20px;
}
.better-payment .layout-4-general .general-form-wrap .payment-amount-wrap .label {
  color: #2B2748;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 15px;
  display: inline-block;
}
.better-payment .layout-4-general .general-form-wrap .payment-amount-wrap .payment-amounts {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 20px;
}
.better-payment .layout-4-general .general-form-wrap .payment-amount-wrap .payment-amounts .payment-amount {
  flex: 0 0 calc(20% - 20px);
}
.better-payment .layout-4-general .general-form-wrap .payment-amount-wrap .payment-amounts .payment-amount input {
  display: none;
}
.better-payment .layout-4-general .general-form-wrap .payment-amount .text {
  border-radius: 8px;
  background: #F4F4F8;
  padding: 17px 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  display: block;
  text-align: center;
  border: 1px solid transparent;
  color: #2B2748;
  font-size: 18px;
  font-weight: 500;
}
.better-payment .layout-4-general .general-form-wrap .payment-amount input:checked + .text {
  border: 1px solid #6E58F7;
  background: #FFF;
  color: #2B2748;
}
.better-payment .layout-4-general .general-form-wrap .payment-amount-wrap .custom-amount input {
  border-radius: 8px;
  border: 1px solid #F4F4F8;
  background: #FFF;
  height: 56px;
  width: 100%;
  padding: 0 20px;
  color: #2B2748;
  font-size: 18px;
  font-weight: 500;
  outline: none;
}
.better-payment .layout-5-donation {
  border-radius: 16px;
  background: #FFF;
  padding: 48px;
  display: flex;
  gap: 48px;
  font-family: "IBM Plex Sans", sans-serif;
}
.better-payment .layout-5-donation h1, .better-payment .layout-5-donation h2, .better-payment .layout-5-donation h3, .better-payment .layout-5-donation h4, .better-payment .layout-5-donation h5, .better-payment .layout-5-donation h6 {
  font-family: "IBM Plex Sans", sans-serif;
}
.better-payment .layout-5-donation .reversed {
  flex-direction: row-reverse;
}
.better-payment .layout-5-donation .donation-image-wrap {
  flex: 0 0 calc(45% - 48px);
  border-radius: 16px;
  overflow: hidden;
  background-image: url("../img/donation.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}
.better-payment .layout-5-donation .col-wrap {
  display: flex;
  gap: 16px;
}
.better-payment .layout-5-donation .col-wrap .span-50 {
  flex: 1 1 calc(50% - 8px);
}
.better-payment .layout-5-donation .donation-image-wrap img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
.better-payment .layout-5-donation .donation-form-wrap {
  flex: 0 0 55%;
}
.better-payment .layout-5-donation .donation-form-wrap .donation-form-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 30px;
}
.better-payment .layout-5-donation .donation-form-wrap .donation-form-header .icon {
  width: unset;
}
.better-payment .layout-5-donation .donation-form-wrap .donation-form-header h3 {
  color: #2B2748;
  font-size: 32px;
  font-weight: 500;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-method-items-wrap {
  margin-bottom: 30px;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-method-items-wrap .label {
  color: #48506D;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 15px;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-method-items-wrap .payment-method-items {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 25px;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item {
  display: flex;
  align-items: center;
  gap: 10px;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item input {
  display: none;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .indicator {
  border-radius: 12px;
  border: 1px solid #92CBAC;
  background: #E9F8F1;
  height: 20px;
  width: 20px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .indicator::before {
  border-radius: 10px;
  background: #13AE5C;
  height: 10px;
  width: 10px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  content: "";
  opacity: 0;
  transition: all 0.3s ease;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .text {
  color: #2B2748;
  font-size: 20px;
  font-weight: 500;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item input:checked + .indicator {
  border: 1px solid #13AE5C;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item input:checked + .indicator::before {
  opacity: 1;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap {
  margin-bottom: 20px;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap.bp-flex {
  flex-wrap: wrap;
  gap: 10px;
  justify-content: start;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .payment-type-item-wrap {
  min-width: 175px;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .label {
  color: #48506D;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 15px;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .payment-type-items {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item > div {
  border-radius: 8px;
  background: #EEF6F2;
  padding: 17.5px 10px;
  justify-content: center;
  display: flex;
  align-items: center;
  gap: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 5px;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item input {
  display: none;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item input:checked + div {
  background: #13AE5C;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item .indicator {
  height: 20px;
  width: 20px;
  margin-left: -20px;
  opacity: 0;
  transition: all 0.3s ease;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item input:checked + div .indicator {
  margin-left: 0;
  opacity: 1;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item .text {
  color: #4C7B62;
  font-size: 18px;
  font-weight: 500;
  transition: all 0.3s ease;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item input:checked + div .text {
  color: #FFFFFF;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-amount-wrap {
  margin-bottom: 20px;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-amount-wrap .label {
  color: #2B2748;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 15px;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-amount-wrap .payment-amounts {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 20px;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-amount-wrap .payment-amounts .payment-amount {
  flex: 0 0 calc(20% - 20px);
}
.better-payment .layout-5-donation .donation-form-wrap .payment-amount-wrap .payment-amounts .payment-amount input {
  display: none;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-amount .text {
  border-radius: 8px;
  background: #EEF6F2;
  padding: 17px 0;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  display: block;
  text-align: center;
  border: 1px solid transparent;
  color: #4C7B62;
  font-size: 18px;
  font-weight: 500;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-amount-wrap .payment-amounts .payment-amount input:checked + .text {
  border: 1px solid #13AE5C;
  background: #FFF;
  color: #335B46;
}
.better-payment .layout-5-donation .donation-form-wrap .payment-amount-wrap .custom-amount input,
.better-payment .layout-5-donation .donation-form-wrap .field-primary_payment_amount .form-group input {
  border-radius: 8px;
  border: 1px solid #4C7B62;
  background: #FFF;
  height: 56px;
  width: 100%;
  padding: 0 20px;
  color: #335B46;
  font-size: 18px;
  font-weight: 500;
  outline: none;
}
.better-payment .layout-5-donation .donation-form-wrap .form-group .label {
  color: #2B2748;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
  display: inline-block;
}
.better-payment .layout-5-donation .donation-form-wrap .form-group .label .required,
.better-payment .layout-5-donation .donation-form-wrap .payment-amount-wrap .label .required {
  color: #e04f5f;
}
.better-payment .layout-5-donation .donation-form-wrap .form-group .form-control {
  border-radius: 8px;
  border: none;
  background: #EEF6F2;
  width: 100%;
  height: 56px;
  padding: 0 20px;
  outline: none;
  font-size: 18px;
  font-weight: 500;
  color: #4C7B62;
}
.better-payment .layout-5-donation .donation-form-wrap .form-group .form-control::-webkit-input-placeholder {
  color: #4C7B62;
}
.better-payment .layout-5-donation .donation-form-wrap .form-group .form-control::-moz-placeholder {
  color: #4C7B62;
}
.better-payment .layout-5-donation .donation-form-wrap .form-group .form-control:-ms-input-placeholder {
  color: #4C7B62;
}
.better-payment .layout-5-donation .donation-form-wrap .form-group .form-control:-moz-placeholder {
  color: #4C7B62;
}
.better-payment .layout-5-donation .donation-button {
  border-radius: 8px;
  background: #13AE5C;
  color: #FFF;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  width: 100%;
  padding: 15.5px 20px;
  border: 0 !important;
  outline: none !important;
  box-shadow: none;
  cursor: pointer;
}
.better-payment .layout-5-donation .payment-label-wrap {
  margin-bottom: 8px;
}
.better-payment .layout-6-woo {
  border-radius: 8px;
  background: #FFF;
  box-shadow: 0px 2px 3px 0px rgba(32, 31, 80, 0.15);
  font-family: "IBM Plex Sans", sans-serif;
  display: flex;
}
.better-payment .layout-6-woo h1, .better-payment .layout-6-woo h2, .better-payment .layout-6-woo h3, .better-payment .layout-6-woo h4, .better-payment .layout-6-woo h5, .better-payment .layout-6-woo h6 {
  font-family: "IBM Plex Sans", sans-serif;
}
.better-payment .layout-6-woo .reversed {
  flex-direction: row-reverse;
}
.better-payment .layout-6-woo .split-payment .payment-type-one-time {
  flex: 0 0 65% !important;
}
.better-payment .layout-6-woo .order-details-wrap {
  border-radius: 8px 0px 0px 8px;
  background: linear-gradient(196deg, #AF6CFB 0%, #7C5ADD 74.17%);
  flex: 0 0 50%;
  min-width: 50%;
}
.better-payment .layout-6-woo .order-details {
  padding: 46px;
}
.better-payment .layout-6-woo .order-details .title {
  color: #FFF;
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 30px;
}
.better-payment .layout-6-woo .order-details .order-list {
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}
.better-payment .layout-6-woo .order-details .order-list .order-item {
  display: flex;
  align-items: center;
  gap: 15px;
  justify-content: space-between;
  margin-bottom: 30px;
}
.better-payment .layout-6-woo .order-details .order-list .order-item .thumb {
  width: 60px;
  height: 60px;
  min-width: 60px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #fff;
}
.better-payment .layout-6-woo .order-details .order-list .order-item .thumb img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
.better-payment .layout-6-woo .order-details .order-list .order-item .name {
  flex: 1;
}
.better-payment .layout-6-woo .order-details .order-list .order-item .name p {
  color: #FFF;
  font-size: 18px;
  font-weight: 500;
}
.better-payment .layout-6-woo .order-details .order-list .order-item .quantity {
  min-width: 60px;
}
.better-payment .layout-6-woo .order-details .order-list .order-item .quantity input {
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  width: 75px;
  height: 36px;
  background: transparent;
  padding: 0 10px;
  color: #fff;
  outline: none;
  font-family: "IBM Plex Sans", sans-serif;
}
.better-payment .layout-6-woo .order-details .order-list .order-item .quantity .arrow {
  position: absolute;
  right: 10px;
  color: rgba(255, 255, 255, 0.5);
  width: 12px;
  transition: all 0.3s ease;
}
.better-payment .layout-6-woo .order-details .order-list .order-item .quantity .arrow:hover {
  color: rgb(255, 255, 255);
}
.better-payment .layout-6-woo .order-details .order-list .order-item .quantity .arrow.arrow-up {
  top: 7px;
}
.better-payment .layout-6-woo .order-details .order-list .order-item .quantity .arrow.arrow-down {
  top: 16px;
}
.better-payment .layout-6-woo .order-details .order-list .order-item .price {
  min-width: 100px;
  text-align: right;
}
.better-payment .layout-6-woo .order-details .order-list .order-item .price p {
  color: #FFF;
  font-size: 20px;
  font-weight: 500;
}
.better-payment .layout-6-woo .order-details .order-summary {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  flex-direction: column;
  padding-top: 15px;
}
.better-payment .layout-6-woo .order-details .order-summary .summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 350px;
  width: 100%;
  gap: 15px;
}
.better-payment .layout-6-woo .order-details .order-summary .summary-item:not(:last-child) {
  margin-bottom: 10px;
}
.better-payment .layout-6-woo .order-details-wrap .total-amount-text,
.better-payment .layout-6-woo .order-details-wrap .total-amount-number {
  color: #FFF;
  font-size: 16px;
  font-weight: 500;
}
.better-payment .layout-6-woo .order-details .order-summary .summary-item.total {
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  padding-top: 10px;
  margin-top: 10px;
}
.better-payment .layout-6-woo .order-details .order-summary .summary-item.total p {
  font-size: 18px;
}
.better-payment .layout-6-woo .woo-form-wrap {
  flex: 0 0 50%;
  min-width: 50%;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap {
  padding: 48px;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .title {
  color: #2B2748;
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 20px;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .payment-method-items {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item {
  flex: 1 1 calc(50% - 8px);
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item input {
  display: none;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option {
  border-radius: 8px;
  background: #F4F4F8;
  padding: 18px 10px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items input:checked + .payment-option {
  border: 1px solid #6E58F7;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option p {
  font-size: 16px;
  font-weight: 600;
  color: #2B2748;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option span {
  display: inline-block;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option .icon.paypal-icon, .better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option .icon.stripe-icon {
  width: 66px;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option .icon.paystack-icon {
  width: 100px;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option .icon image {
  height: 20px;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option .title {
  color: #2B2748;
  font-size: 16px;
  font-weight: 600;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .form-group {
  position: relative;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .form-group .icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .form-control {
  border-radius: 4px;
  border: 1px solid #D3DBEE;
  background: #FFF;
  height: 56px;
  padding: 0 20px;
  width: 100%;
  outline: none;
  color: #2B2748;
  font-size: 14px;
  font-weight: 500;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap select.form-control {
  appearance: none;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .form-control:focus {
  border: 1px solid #2B2748;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .form-group label {
  position: absolute;
  top: -9px;
  left: 18px;
  background: #fff;
  padding: 0 3px;
  color: #5F667D;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  pointer-events: none;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .form-control:focus + label {
  top: -9px;
}
.better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap .form-control:valid + label {
  top: -9px;
}
.better-payment .layout-6-woo .col-wrap {
  display: flex;
  gap: 16px;
}
.better-payment .layout-6-woo .col-wrap .span-50 {
  flex: 1 1 calc(50% - 8px);
}
.better-payment .layout-6-woo .check-item {
  display: flex;
  align-items: center;
  gap: 15px;
  pointer-events: all;
}
.better-payment .layout-6-woo .check-item input {
  display: none;
}
.better-payment .layout-6-woo .check-item .checkbox {
  border-radius: 4px;
  border: 2px solid #D3DBEE;
  background: #FFF;
  display: block;
  height: 20px;
  width: 20px;
  cursor: pointer;
  position: relative;
}
.better-payment .layout-6-woo .check-item .checkbox::before {
  border-radius: 2px;
  background: #6E58F7;
  height: 12px;
  width: 12px;
  opacity: 0;
  transition: all 0.3s ease;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  content: "";
}
.better-payment .layout-6-woo .check-item input:checked + .checkbox::before {
  opacity: 1;
}
.better-payment .layout-6-woo .woo-form-wrap .button {
  border-radius: 8px;
  color: #FFF;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  width: 100%;
  padding: 15.5px 20px;
  box-shadow: none;
  cursor: pointer;
  background: linear-gradient(228deg, #AF6CFB 27.95%, #7C5ADD 82.65%);
}
.better-payment .layout-6-woo .payment-method-items-wrap .payment-amount-wrapper {
  margin-bottom: 20px;
}
.better-payment .layout-6-woo .payment-method-items-wrap .payment-amount-wrapper .label {
  color: #2B2748;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  display: inline-block;
}
.better-payment .layout-6-woo .payment-method-items-wrap .payment-amount-wrapper .payment-amounts {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
  margin-bottom: 20px;
}
.better-payment .layout-6-woo .payment-method-items-wrap .payment-amount-wrapper .payment-amounts .payment-amount {
  flex: 0 0 calc(20% - 20px);
}
.better-payment .layout-6-woo .payment-method-items-wrap .payment-amount-wrapper .payment-amounts .payment-amount input {
  display: none;
}
.better-payment .layout-6-woo .payment-method-items-wrap .payment-amount .text {
  border-radius: 8px;
  background: #F4F4F8;
  padding: 17px 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  display: block;
  text-align: center;
  border: 1px solid transparent;
  color: #2B2748;
  font-size: 18px;
  font-weight: 500;
}
.better-payment .layout-6-woo .payment-method-items-wrap .payment-amount-wrapper .payment-amounts .payment-amount input:checked + .text {
  border: 1px solid #6E58F7;
  background: #FFF;
  color: #2B2748;
}
.better-payment .layout-6-woo .payment-method-items-wrap .payment-amount-wrapper .custom-amount input {
  border-radius: 8px;
  border: 1px solid #F4F4F8;
  background: #FFF;
  height: 56px;
  width: 100%;
  padding: 0 20px;
  color: #2B2748;
  font-size: 18px;
  font-weight: 500;
  outline: none;
}
.better-payment .method-specific-options.hidden {
  display: none;
}
.better-payment #split-payment-installment-wrap {
  display: none;
}

@media all and (max-width: 439px) {
  .better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .payment-type-items {
    gap: 16px;
  }
  .better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item {
    flex: 0 0 100% !important;
  }
}
@media all and (max-width: 575px) {
  .better-payment .layout-6-woo .order-details .order-list .order-item {
    gap: 10px;
  }
  .better-payment .layout-6-woo .order-details .order-list .order-item .price {
    min-width: 72px;
  }
  .better-payment .layout-6-woo .order-details .order-list .order-item .price p {
    font-size: 16px;
  }
  .better-payment .layout-6-woo .col-wrap {
    flex-direction: column;
    gap: 0;
  }
  .better-payment .layout-6-woo .col-wrap .span-50 {
    flex: 1 1 100%;
  }
}
@media all and (max-width: 767px) {
  .better-payment .card-field .icons {
    display: none;
  }
  .better-payment .layout-4-general .col-wrap {
    display: flex;
    flex-direction: column;
    gap: 0;
  }
  .better-payment .layout-4-general .col-wrap .span-50 {
    flex: 1 1 100%;
  }
  .better-payment .layout-4-general .general-form-wrap .payment-method-items {
    gap: 20px;
  }
  .better-payment .layout-4-general .general-form-wrap .payment-method-items .payment-method-item .payment-method-image-wrap {
    padding: 15px 12px;
  }
  .better-payment .layout-4-general .general-form-wrap .payment-amount-wrap .payment-amounts {
    gap: 15px;
  }
  .better-payment .layout-5-donation .col-wrap {
    flex-direction: column;
    gap: 0;
  }
  .better-payment .layout-5-donation .col-wrap .span-50 {
    flex: 1 1 100%;
  }
  .better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .payment-type-items {
    gap: 16px;
  }
  .better-payment .layout-5-donation .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item {
    flex: 0 0 calc(50% - 8px);
  }
  .better-payment .layout-5-donation .donation-form-wrap .payment-amount-wrap .payment-amounts {
    gap: 15px;
  }
  .better-payment .layout-5-donation .donation-form-wrap .payment-amount-wrap .payment-amounts .payment-amount {
    flex: 0 0 calc(30% - 20px);
  }
  .better-payment .layout-6-woo .payment-method-items-wrap .payment-amount-wrapper .payment-amounts {
    gap: 15px;
  }
  .better-payment .layout-6-woo .order-details .order-list .order-item .quantity input {
    width: 55px;
  }
}
@media all and (max-width: 991px) {
  .better-payment .layout-4-general {
    flex-direction: column;
    padding: 30px;
    gap: 30px;
  }
  .better-payment .layout-4-general .transaction-details-wrap {
    padding: 32px 32px 60px;
    min-width: auto;
    width: 100%;
    flex-basis: auto;
  }
  .better-payment .layout-5-donation {
    padding: 30px;
    display: block;
  }
  .better-payment .layout-5-donation .donation-image-wrap {
    display: none;
  }
  .better-payment .layout-6-woo {
    display: block;
  }
  .better-payment .layout-6-woo .order-details {
    padding: 25px;
  }
  .better-payment .layout-6-woo .woo-form-wrap .payment-method-items-wrap {
    padding: 25px;
  }
}
@media all and (max-width: 1199px) {
  .better-payment .layout-4-general .general-form-wrap .payment-method-items .payment-method-item {
    flex: 0 0 calc(50% - 12px);
  }
}
@media all and (max-width: 767px) {
  .better-payment .layout-4-general .general-form-wrap .payment-method-items .payment-method-item {
    flex: 0 0 100%;
  }
  .better-payment .layout-4-general .general-form-wrap .better-payment-field-advanced-layout.field-display-inline {
    width: 100% !important;
    margin: 0;
  }
  .better-payment .layout-4-general .general-form-wrap .payment-amount-wrap .payment-amounts .payment-amount {
    flex: 0 0 calc(50% - 8px);
  }
  .better-payment .layout-4-general .payment-button {
    font-size: 16px;
  }
}
