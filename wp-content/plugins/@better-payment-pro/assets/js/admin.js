/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./src/js/admin.js":
/*!*************************!*\
  !*** ./src/js/admin.js ***!
  \*************************/
/***/ (() => {

eval("(function ($) {\n  \"use strict\";\n\n  $(document).on(\"click\", \".bp-refund-buttons-wrap a\", function (e) {\n    e.preventDefault();\n    let $this = $(this);\n    let orderId = $this.attr(\"data-orderid\");\n    let refundAmount = $this.attr(\"data-amount\");\n    let refundType = $this.attr(\"data-refundtype\");\n    Swal.fire({\n      title: \"Are you sure?\",\n      text: \"You won't be able to revert this!\",\n      icon: \"warning\",\n      showCancelButton: true,\n      confirmButtonColor: \"#3085d6\",\n      cancelButtonColor: \"#d33\",\n      confirmButtonText: \"Yes, refund it!\",\n      focusCancel: 1\n    }).then(result => {\n      if (result.value) {\n        $.post(betterPaymentPro.ajaxurl, {\n          action: \"better_payment_pro_transaction_refund\",\n          orderId: orderId,\n          refundAmount: refundAmount,\n          refundType: refundType,\n          _wpnonce: betterPaymentPro.nonce\n        }).done(function (response) {\n          toastr.success(\"Successfully refunded!\");\n          setTimeout(function () {\n            location.reload();\n          }, 1500);\n        }).fail(function () {\n          toastr.error(\"Something went wrong!\");\n        });\n      } else if (result.dismiss == \"cancel\") {} else if (result.dismiss == \"esc\") {}\n    });\n  });\n  $(document).on(\"click\", \".bp-send-receipt-submit\", function (e) {\n    e.preventDefault();\n    let $this = $(this);\n    let email = $(\".better-payment .bp-send-receipt .email-recipient-field\").val();\n    let orderId = $this.attr(\"data-orderid\");\n    let isemailValidated = admin_better_email_validation(email);\n    if (email == \"\" || !isemailValidated) {\n      toastr.error(\"Email address is not valid!\");\n      return false;\n    }\n    $.post(betterPaymentPro.ajaxurl, {\n      action: \"better_payment_pro_transaction_send_receipt\",\n      email: email,\n      orderId: orderId,\n      _wpnonce: betterPaymentPro.nonce\n    }).done(function (response) {\n      toastr.success(\"Email sent successfully!\");\n      setTimeout(function () {\n        location.reload();\n      }, 1500);\n    }).fail(function () {\n      toastr.error(\"Something went wrong!\");\n    });\n  });\n  $(document).on(\"click\", \".better-payment-print-receipt-btn\", function (e) {\n    e.preventDefault();\n    let $this = $(this);\n    let printContent = $(\".better-payment-print-receipt-wrap\").html();\n    let combined = document.createElement(\"div\");\n    combined.innerHTML = printContent;\n    let wheight = $(window).height();\n    let winPrint = window.open(\"\", \"\", \"left=50%,top=10%,width=\" + 700 + \",height=\" + wheight + \",toolbar=0,scrollbars=0,status=0\");\n    winPrint.document.write(combined.outerHTML);\n    winPrint.document.close();\n    winPrint.focus();\n    winPrint.print();\n    winPrint.close();\n  });\n  $(document).on(\"click\", '.better-payment .bp-license-form-block button[type=\"submit\"][name=\"license_activate\"]', function (e) {\n    e.preventDefault();\n    $(\".bp-license-error-msg\").hide().text(\"\");\n    $(\".bp-verification-msg\").hide();\n    let button = $(this);\n    button.text(\"Activating...\");\n    $.ajax({\n      url: wpdeveloperLicenseManagerConfig.api_url,\n      type: \"POST\",\n      data: {\n        action: `${wpdeveloperLicenseManagerConfig.action}/license/activate`,\n        _nonce: wpdeveloperLicenseManagerConfig.nonce,\n        license_key: $(`#${wpdeveloperLicenseManagerConfig.action}-license-key`).val()\n      },\n      success: function (response) {\n        if (response.success) {\n          $(`#${wpdeveloperLicenseManagerConfig.action}-license-key`).attr(\"disabled\", \"disabled\");\n          if (response.data.license !== \"required_otp\") {\n            $(\".bp-activate__license__block\").hide().siblings(\".--deactivation-form\").show();\n            $(\".--deactivation-form input\").val(response.data.license_key);\n            $(`#${wpdeveloperLicenseManagerConfig.action}-license-key`).val(\"\").removeAttr(\"disabled\").siblings(\"button\").removeAttr(\"disabled\").text(\"Activate\");\n            return;\n          }\n          button.text(\"Verification Required\").attr(\"disabled\", \"disabled\").addClass(\"--verification-required\");\n          $(\".bp-customer-email\").text(response.data.customer_email);\n          $(\".bp-verification-msg\").show();\n        } else {\n          $(\".bp-license-error-msg\").text(response.data.message).show();\n          button.text(\"Activate\");\n        }\n      },\n      error: function (response) {\n        console.log(response);\n        button.text(\"Activate\");\n      }\n    });\n  }).on(\"click\", '.bp-verification-msg button[type=\"submit\"]', function (e) {\n    e.preventDefault();\n    $(\".bp-license-error-msg\").hide().text(\"\");\n    let button = $(this);\n    button.text(\"Verifying...\");\n    $.ajax({\n      url: wpdeveloperLicenseManagerConfig.api_url,\n      type: \"POST\",\n      data: {\n        action: `${wpdeveloperLicenseManagerConfig.action}/license/submit-otp`,\n        _nonce: wpdeveloperLicenseManagerConfig.nonce,\n        license: $(`#${wpdeveloperLicenseManagerConfig.action}-license-key`).val(),\n        otp: $(`#${wpdeveloperLicenseManagerConfig.action}-license-otp`).val()\n      },\n      success: function (response) {\n        if (response.success) {\n          window.location.reload();\n        } else {\n          $(\".bp-license-error-msg\").text(\"Whoops! Your License Verification Code has expired. Please try again.\").show();\n        }\n        button.text(\"Verify\");\n      },\n      error: function (response) {\n        console.log(response);\n        button.text(\"Verify\");\n      }\n    });\n  }).on(\"click\", '.bp-license-form-block button[type=\"submit\"][name=\"license_deactivate\"]', function (e) {\n    e.preventDefault();\n    let button = $(this);\n    button.text(\"Deactivating...\");\n    $.ajax({\n      url: wpdeveloperLicenseManagerConfig.api_url,\n      type: \"POST\",\n      data: {\n        action: `${wpdeveloperLicenseManagerConfig.action}/license/deactivate`,\n        _nonce: wpdeveloperLicenseManagerConfig.nonce\n      },\n      success: function (response) {\n        if (response.success) {\n          window.location.reload();\n        } else {\n          $(\".bp-license-error-msg\").text(response.data.message).show();\n        }\n        button.text(\"Deactivate\");\n      },\n      error: function (response) {\n        console.log(response);\n        button.text(\"Deactivate\");\n      }\n    });\n  }).on(\"click\", \".bp-otp-resend\", function (e) {\n    e.preventDefault();\n    $.ajax({\n      url: wpdeveloperLicenseManagerConfig.api_url,\n      type: \"POST\",\n      data: {\n        action: `${wpdeveloperLicenseManagerConfig.action}/license/resend-otp`,\n        _nonce: wpdeveloperLicenseManagerConfig.nonce,\n        license: $(`#${wpdeveloperLicenseManagerConfig.action}-license-key`).val()\n      },\n      success: function (response) {\n        if (response.success) {\n          $(\".bp-license-error-msg\").text(\"License Verification Code has been sent to your email address. Please check your email to find the code.\").addClass(\"notice-message\").show();\n          setTimeout(function () {\n            $(\".bp-license-error-msg\").removeClass(\"notice-message\").text(\"\").hide();\n          }, 3000);\n        } else {\n          $(\".bp-license-error-msg\").text(response.data.message).show();\n        }\n      },\n      error: function (response) {\n        console.log(response);\n      }\n    });\n  });\n  function toasterOptions() {\n    toastr.options = {\n      timeOut: \"2000\",\n      toastClass: \"font-size-md\",\n      positionClass: \"toast-top-center\",\n      showMethod: \"slideDown\",\n      hideMethod: \"slideUp\"\n    };\n  }\n  function admin_better_email_validation(email) {\n    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\n  }\n  toasterOptions();\n})(jQuery);\n\n//# sourceURL=webpack://better-payment-pro/./src/js/admin.js?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = {};
/******/ 	__webpack_modules__["./src/js/admin.js"]();
/******/ 	
/******/ })()
;