/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./src/js/analytics.js":
/*!*****************************!*\
  !*** ./src/js/analytics.js ***!
  \*****************************/
/***/ (() => {

eval("// Page: Analytics Starts\n(function ($) {\n  \"use strict\";\n\n  $(document).ready(function () {\n    $(document).on(\"change\", \".better-payment .analytics_time_period-custom-range\", function (e) {\n      let selectedValues = $(this).is(\":checked\");\n      if (selectedValues === true) {\n        $(\".better-payment .modal.analytics-custom-time-period\").addClass(\"is-active\");\n      }\n    });\n\n    // Chart JS: Starts\n    setTimeout(function () {\n      let analyticsChartConfigData = {};\n      if (typeof betterPaymentProAnalytics.analytics != \"undefined\") {\n        analyticsChartConfigData = getAnalyticsChartConfigData(betterPaymentProAnalytics.analytics);\n      }\n      const labels = typeof analyticsChartConfigData.labels != \"undefined\" ? analyticsChartConfigData.labels : [];\n      const datasets = typeof analyticsChartConfigData.datasets != \"undefined\" ? analyticsChartConfigData.datasets : [];\n      const data = {\n        labels: labels,\n        datasets: datasets\n      };\n      const config = {\n        type: \"line\",\n        data: data,\n        options: {\n          maintainAspectRatio: !1,\n          scaleShowHorizontalLines: !0,\n          scaleShowVerticalLines: !1,\n          bezierCurveTension: 0.3,\n          responsive: true,\n          spanGaps: false,\n          tooltips: {\n            mode: \"nearest\",\n            position: \"nearest\",\n            intersect: false\n          },\n          hover: {\n            position: \"nearest\",\n            intersect: false\n          },\n          scales: {\n            y: {\n              ticks: {\n                // Include a dollar sign in the ticks\n                callback: function (value, index, values) {\n                  return \"$\" + value;\n                }\n              }\n            }\n          }\n        }\n      };\n      const myChart = new Chart(document.getElementById(\"myChart\"), config);\n    }, 1000); // Run after 1000ms\n\n    $(document).on(\"click\", \".better-payment .bp-analytics-filter-submit\", function (e) {\n      e.preventDefault();\n      $(\".analytics-chart-wrap\").css(\"opacity\", 0.5);\n      let $this = $(this);\n      let form = $(\"#better-payment-analytics-form\");\n      $.post(betterPaymentPro.ajaxurl, {\n        action: \"better_payment_pro_analytics_filter\",\n        data: form.serialize(),\n        _wpnonce: betterPaymentPro.nonce\n      }).done(function (response) {\n        if (typeof response.data !== \"undefined\" && typeof response.data.analytics !== \"undefined\") {\n          // $(\".analytics-chart-wrap\").html(\n          //   response.data.analytics_filter_content\n          // );\n          let analyticsData = response.data.analytics;\n          let analyticsChartConfigDataAjax = getAnalyticsChartConfigData(analyticsData);\n          myChart.data = {\n            labels: typeof analyticsChartConfigDataAjax.labels != \"undefined\" ? analyticsChartConfigDataAjax.labels : [],\n            datasets: typeof analyticsChartConfigDataAjax.datasets != \"undefined\" ? analyticsChartConfigDataAjax.datasets : []\n          };\n          myChart.update();\n          $(\".analytics-chart-wrap\").css(\"opacity\", 1);\n          $(\".analytics-total-transaction\").attr(\"title\", \"Filtered Total Transactions: \" + analyticsData.all_transactions_total);\n          $(\".analytics-completed-transaction\").attr(\"title\", \"Filtered Completed Transactions: \" + analyticsData.completed_transactions_total);\n          $(\".analytics-incomplete-transaction\").attr(\"title\", \"Filtered Incomplete Transactions: \" + analyticsData.incomplete_transactions_total);\n          $(\".analytics-refund-transaction\").attr(\"title\", \"Filtered Refund Transactions: \" + analyticsData.refund_transactions_total);\n        }\n      }).fail(function () {\n        $(\".analytics-chart-wrap\").css(\"opacity\", 1);\n        toastr.error(\"Something went wrong!\");\n      });\n    });\n    // Chart JS: Ends\n\n    $(document).on(\"click\", \".analytics-select-custom-button\", function (e) {\n      e.preventDefault();\n      let $this = $(this);\n      let dataTarget = $this.attr(\"data-target\");\n      $(dataTarget).toggleClass(\"is-hidden\");\n      if (dataTarget === \".analytics_transaction_types-dropdown\") {\n        if (!$(\".analytics_time_period-dropdown\").hasClass(\"is-hidden\")) {\n          $(\".analytics_time_period-dropdown\").addClass(\"is-hidden\");\n        }\n      } else {\n        if (!$(\".analytics_transaction_types-dropdown\").hasClass(\"is-hidden\")) {\n          $(\".analytics_transaction_types-dropdown\").addClass(\"is-hidden\");\n        }\n      }\n    });\n    $(document).on(\"click\", \"body\", function (e) {\n      let filter_button_container = $(\".analytics-select-custom-button-wrap\");\n      if (!filter_button_container.is(e.target) && filter_button_container.has(e.target).length === 0) {\n        if (!$(\".analytics_transaction_types-dropdown\").hasClass(\"is-hidden\")) {\n          $(\".analytics_transaction_types-dropdown\").addClass(\"is-hidden\");\n        }\n        if (!$(\".analytics_time_period-dropdown\").hasClass(\"is-hidden\")) {\n          $(\".analytics_time_period-dropdown\").addClass(\"is-hidden\");\n        }\n      }\n    });\n    function getAnalyticsChartConfigData(analyticsData) {\n      let datasets = [];\n      let labels = [];\n      let allTransactionDataset = {\n        label: \"Total Transaction\",\n        data: analyticsData.all_transactions,\n        borderColor: \"#735EF8\",\n        backgroundColor: \"#735EF8\"\n      };\n      let completedTransactionDataset = {\n        label: \"Completed Transaction\",\n        data: analyticsData.completed_transactions,\n        borderColor: \"#0ECA86\",\n        backgroundColor: \"#0ECA86\"\n      };\n      let incompleteTransactionDataset = {\n        label: \"Incomplete Transaction\",\n        data: analyticsData.incomplete_transactions,\n        borderColor: \"#FFDA15\",\n        backgroundColor: \"#FFDA15\"\n      };\n      let refundedTransactionDataset = {\n        label: \"Refunded Transaction\",\n        data: analyticsData.refund_transactions,\n        borderColor: \"#FF0202\",\n        backgroundColor: \"#FF0202\"\n      };\n      if (typeof analyticsData.all_transactions_show != \"undefined\" && analyticsData.all_transactions_show == 1) {\n        datasets.push(allTransactionDataset);\n      }\n      if (typeof analyticsData.completed_transactions_show != \"undefined\" && analyticsData.completed_transactions_show == 1) {\n        datasets.push(completedTransactionDataset);\n      }\n      if (typeof analyticsData.incomplete_transactions_show != \"undefined\" && analyticsData.incomplete_transactions_show == 1) {\n        datasets.push(incompleteTransactionDataset);\n      }\n      if (typeof analyticsData.refund_transactions_show != \"undefined\" && analyticsData.refund_transactions_show == 1) {\n        datasets.push(refundedTransactionDataset);\n      }\n      if (typeof analyticsData.show_all_four_stats != \"undefined\" && analyticsData.show_all_four_stats == 1) {\n        datasets = [allTransactionDataset, completedTransactionDataset, incompleteTransactionDataset, refundedTransactionDataset];\n      }\n      if (typeof analyticsData.payment_date_period_index != \"undefined\") {\n        labels = analyticsData.payment_date_period_index;\n      }\n      return {\n        datasets: datasets,\n        labels: labels\n      };\n    }\n    $(\".analytics-select-custom-button-dropdown input[name='analytics_transaction_types[]']\").change(function () {\n      if (this.checked) {\n        if (this.value !== \"all\") {\n          $(\".analytics_transaction_types-dropdown .analytics_transaction_types-all\").prop(\"checked\", false);\n          $(\".analytics_transaction_types-button-text\").html(\"Transactions\");\n        } else if (this.value === \"all\") {\n          $(\".analytics_transaction_types-dropdown input[name='analytics_transaction_types[]']\").prop(\"checked\", false);\n          $(\".analytics_transaction_types-dropdown .analytics_transaction_types-all\").prop(\"checked\", true);\n          $(\".analytics_transaction_types-button-text\").html(\"All Transactions\");\n        }\n      } else {\n        //If four checkboxes are unchecked then check All Transactions checkbox\n        if (!$(\".analytics_transaction_types-dropdown input[name='analytics_transaction_types[]']:checked\").length) {\n          $(\".analytics_transaction_types-dropdown .analytics_transaction_types-all\").prop(\"checked\", true);\n          $(\".analytics_transaction_types-button-text\").html(\"All Transactions\");\n        } else {\n          $(\".analytics_transaction_types-button-text\").html(\"Transactions\");\n        }\n      }\n    });\n    $(\".analytics-select-custom-button-dropdown input[name='analytics_time_period[]']\").change(function () {\n      if (this.checked) {\n        //Uncheck other checkboxes but this one\n        $(\".analytics_time_period-dropdown input[name='analytics_time_period[]']\").not(this).prop(\"checked\", false);\n        updateDropdownText(this.value);\n      } else {\n        //If four checkboxes are unchecked then check Last Week checkbox\n        if (!$(\".analytics_time_period-dropdown input[name='analytics_time_period[]']:checked\").length) {\n          $(\".analytics_time_period-dropdown .analytics_time_period-week\").prop(\"checked\", true);\n          updateDropdownText(\"week\");\n        }\n      }\n    });\n    function capitalizeFirstLetter(string) {\n      return string.charAt(0).toUpperCase() + string.slice(1);\n    }\n    function updateDropdownText(text, inputName = \"analytics_time_period\") {\n      if (inputName === \"analytics_time_period\") {\n        if (text !== \"custom\") {\n          $(\".analytics_time_period-button-text\").html(\"Last \" + capitalizeFirstLetter(text));\n        } else {\n          $(\".analytics_time_period-button-text\").html(\"Custom\");\n        }\n      }\n\n      // if(inputName === 'analytics_transaction_types'){\n      //     if(text !== 'all'){\n      //         //Fetch all checked checkboxes and update the dropdown text\n      //         let checkedCheckboxes = $(\".analytics_transaction_types-dropdown input[name='analytics_transaction_types[]']:checked\");\n      //         let dropdownText = '';\n      //         for(let i = 0; i < checkedCheckboxes.length; i++){\n      //             //Remove -transaction from the text\n      //             let optionText = checkedCheckboxes[i].value.replace('-transaction', '');\n\n      //             if(i === checkedCheckboxes.length - 1){\n      //                 dropdownText += capitalizeFirstLetter(optionText) + ' Transactions';\n      //             } else {\n      //                 dropdownText += capitalizeFirstLetter(optionText) + ', ';\n      //             }\n      //         }\n      //         $(\".analytics_transaction_types-button-text\").html(dropdownText);\n      //     }else {\n      //         $(\".analytics_transaction_types-button-text\").html(\"All Transactions\");\n      //     }\n      // }\n    }\n  });\n})(jQuery);\n// Page: Analytics Ends\n\n//# sourceURL=webpack://better-payment-pro/./src/js/analytics.js?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = {};
/******/ 	__webpack_modules__["./src/js/analytics.js"]();
/******/ 	
/******/ })()
;