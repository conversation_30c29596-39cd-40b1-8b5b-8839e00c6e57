/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./src/js/common.js":
/*!**************************!*\
  !*** ./src/js/common.js ***!
  \**************************/
/***/ (() => {

eval(";\n(function ($) {\n  $(document).ready(function () {\n    // $('.better-payment .select2').select2({\n    //     placeholder: 'Select an option',\n    //     allowClear: true,\n    //     extraClass: ''\n    // });\n\n    // $('.better-payment .better-payment-analytics .select2').select2({\n    //     placeholder: 'Select an option',\n    //     allowClear: true,\n    //     extraClass: 'analytic-reports-select2'\n    // });\n\n    $(document).on('change', '.better-payment .order-details-wrap .quantity input', function (e) {\n      let quantity = $(this).val();\n      let price = parseFloat($(this).data('price'));\n      let total_price = quantity * price;\n      let element = $(this).closest('.order-item').find('.order-item-price-amount');\n      $(element).html(total_price.toFixed(2));\n      let overall_total = 0;\n      $('.better-payment .order-item').each(function () {\n        let item_total = parseFloat($(this).find('.order-item-price-amount').text());\n        overall_total += item_total;\n      });\n\n      // $('.summary-item-subtotal-amount').html( total_price );\n      // $('.better-payment .payment-amount-wrap input').val( total_price );\n      $('.better-payment .summary-item-subtotal-amount').html(overall_total.toFixed(2));\n      $('.better-payment .payment-amount-wrap input').val(overall_total);\n    });\n    $(document).on('change', '.better-payment .bp-custom-payment-amount-quantity', function (e) {\n      let quantity = $(this).val();\n      let price = $('.bp-custom-payment-amount').val();\n      let total_price = quantity * price;\n      $('.bp-transaction-details-amount-text').html(total_price);\n    });\n    $(document).on('change', '.better-payment .split-payment-text-wrap select', function (e) {\n      let selectedOption = $(this).find('option:selected');\n      let noteWrap = $('.better-payment .split-payment-installment-note-wrap');\n      let noteAmount = $('.better-payment .split-payment-installment-note-amount');\n      let unitAmount = selectedOption.data('unit-amount') ?? 0;\n      let interval = selectedOption.data('interval') ?? '';\n      let currencyLeft = noteAmount.data('currency-left') ?? '';\n      let currencyRight = noteAmount.data('currency-right') ?? '';\n      let separator = interval ? '/' : '';\n      if (unitAmount) {\n        noteAmount.text(`${currencyLeft}${unitAmount}${currencyRight} ${separator} ${interval}`);\n        noteWrap.removeClass('is-hidden');\n      } else {\n        noteWrap.addClass('is-hidden');\n      }\n    });\n\n    // BP User Dashboard\n    $(document).on(\"click\", \".bp-user-dashboard-subscription-cancel\", function (e) {\n      e.preventDefault();\n      bpUserDashboardSubscriptionCancel(this);\n    });\n    function bpUserDashboardSubscriptionCancel(button) {\n      let subscriptionCancelButton = $(button),\n        nonce = betterPaymentPro.nonce;\n      let subscription_id = subscriptionCancelButton.attr('data-subscriptionid');\n      if (!subscription_id) {\n        return false;\n      }\n      $.ajax({\n        type: \"POST\",\n        url: betterPaymentPro.ajaxurl,\n        data: {\n          action: \"better_payment_user_dashboard_subscription_cancel\",\n          nonce: nonce,\n          subscription_id: subscription_id\n        },\n        beforeSend: function () {\n          subscriptionCancelButton.addClass(\"is-loading\");\n        },\n        success: function (res) {\n          subscriptionCancelButton.removeClass(\"is-loading\");\n          if (res.success) {\n            let status_button = $(button).closest('.better-payment-user-dashboard-table-body').find('.bp-user-dashboard-subscription-status button');\n            status_button.html('Inactive').addClass('inactive').removeClass('active');\n            toastr.success(res.data.message ?? 'Successfully unsubscribed!');\n          } else {\n            toastr.error(res.data.message ?? 'Something went wrong!');\n          }\n        }\n      });\n    }\n    $('.better-payment input[name=\"payment_type\"]').on('change', function () {\n      var selectedValue = $(this).val();\n      var $dropdown = $('#split_payment_installment');\n      if ($dropdown.length) {\n        $dropdown.val(selectedValue);\n      }\n    });\n    $(document).on('click', '.better-payment .payment-type-items', function (e) {\n      const $this = $(this);\n      const priceId = $this.data('price_id');\n      const amount = $this.data('amount');\n      if (!priceId || !amount) {\n        console.error('Invalid price ID or amount');\n        return;\n      }\n\n      // Update the amount input\n      $('.better-payment .field-primary_payment_amount input[name=\"primary_payment_amount\"]').val(amount);\n\n      // Update the hidden price ID input\n      $('.better-payment input[name=\"better_payment_recurring_price_id\"]').val(priceId);\n    });\n  });\n})(jQuery);\n\n//# sourceURL=webpack://better-payment-pro/./src/js/common.js?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = {};
/******/ 	__webpack_modules__["./src/js/common.js"]();
/******/ 	
/******/ })()
;