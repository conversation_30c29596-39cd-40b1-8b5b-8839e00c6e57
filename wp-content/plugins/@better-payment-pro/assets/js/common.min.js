(()=>{var t;(t=jQuery)(document).ready((function(){t(document).on("change",".better-payment .order-details-wrap .quantity input",(function(e){let a=t(this).val()*parseFloat(t(this).data("price")),n=t(this).closest(".order-item").find(".order-item-price-amount");t(n).html(a.toFixed(2));let i=0;t(".better-payment .order-item").each((function(){let e=parseFloat(t(this).find(".order-item-price-amount").text());i+=e})),t(".better-payment .summary-item-subtotal-amount").html(i.toFixed(2)),t(".better-payment .payment-amount-wrap input").val(i)})),t(document).on("change",".better-payment .bp-custom-payment-amount-quantity",(function(e){let a=t(this).val()*t(".bp-custom-payment-amount").val();t(".bp-transaction-details-amount-text").html(a)})),t(document).on("change",".better-payment .split-payment-text-wrap select",(function(e){let a=t(this).find("option:selected"),n=t(".better-payment .split-payment-installment-note-wrap"),i=t(".better-payment .split-payment-installment-note-amount"),r=a.data("unit-amount")??0,s=a.data("interval")??"",o=i.data("currency-left")??"",m=i.data("currency-right")??"",c=s?"/":"";r?(i.text(`${o}${r}${m} ${c} ${s}`),n.removeClass("is-hidden")):n.addClass("is-hidden")})),t(document).on("click",".bp-user-dashboard-subscription-cancel",(function(e){e.preventDefault(),function(e){let a=t(e),n=betterPaymentPro.nonce,i=a.attr("data-subscriptionid");if(!i)return!1;t.ajax({type:"POST",url:betterPaymentPro.ajaxurl,data:{action:"better_payment_user_dashboard_subscription_cancel",nonce:n,subscription_id:i},beforeSend:function(){a.addClass("is-loading")},success:function(n){a.removeClass("is-loading"),n.success?(t(e).closest(".better-payment-user-dashboard-table-body").find(".bp-user-dashboard-subscription-status button").html("Inactive").addClass("inactive").removeClass("active"),toastr.success(n.data.message??"Successfully unsubscribed!")):toastr.error(n.data.message??"Something went wrong!")}})}(this)})),t('.better-payment input[name="payment_type"]').on("change",(function(){var e=t(this).val(),a=t("#split_payment_installment");a.length&&a.val(e)})),t(document).on("click",".better-payment .payment-type-items",(function(e){const a=t(this),n=a.data("price_id"),i=a.data("amount");n&&i?(t('.better-payment .field-primary_payment_amount input[name="primary_payment_amount"]').val(i),t('.better-payment input[name="better_payment_recurring_price_id"]').val(n)):console.error("Invalid price ID or amount")}))}))})();