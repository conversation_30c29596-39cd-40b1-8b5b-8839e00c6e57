<?php

namespace Better_Payment\Pro;

use Better_Payment\Pro\Admin\Elementor\UserDashboard;
use Better_Payment\Pro\Admin\Transaction;
use Better_Payment\Pro\Admin\Elementor\Widget;
use Better_Payment\Pro\Admin\Menu;
use Better_Payment\Pro\Admin\Setting;

/**
 * The admin class
 * 
 * @since 0.0.1
 */
class Admin extends Controller{

    /**
     * Initialize the class
     * 
     * @since 0.0.1
     */
    public function __construct() {

    }

    /**
     * Dispatch and bind actions
     *
     * @return void
     * @since 0.0.1
     */
    public static function dispatch_actions( ) {
        $transactionObj = new Transaction();
        $bpElementorWidgetObj = new Widget();
        $userDashboardObj = new UserDashboard();
        $menuObj = new Menu();
        $settingObj = new Setting();
        
        add_filter('better_payment/pro_enabled', '__return_true');

        add_filter('better_payment/admin/get_submenu_page_list', [$menuObj, 'get_submenu_page_list'], 10, 2);
        add_filter('better_payment/admin/get_page_menu_slug_list', [$menuObj, 'get_page_menu_slug_list'], 10);

        add_action( 'better_payment/admin/license_tab_content', [ $settingObj, 'render_licenses_page' ] );
		
        add_action('better_payment/admin/transaction_refund_content', [$transactionObj, 'transaction_refund_content']);
        add_action('better_payment/admin/transaction_receipt_content', [$transactionObj, 'transaction_receipt_content']);
        add_action('better_payment/admin/transaction_subscription_content', [$transactionObj, 'transaction_subscription_content']);        
        add_action('better_payment/stripe_payment/success', [$bpElementorWidgetObj, 'stripe_payment_success']);        
        
        add_filter('better_payment/elementor/editor/get_style_depends', [$bpElementorWidgetObj, 'get_style_depends']);
        add_filter('better_payment/elementor/editor/get_script_depends', [$bpElementorWidgetObj, 'get_script_depends']);
        
        add_filter('better_payment/elementor/user_dashboard/editor/get_style_depends', [$userDashboardObj, 'get_style_depends']);
        add_filter('better_payment/elementor/user_dashboard/editor/get_script_depends', [$userDashboardObj, 'get_script_depends']);
        
        add_action('better_payment/elementor/editor/layouts_form_settings_transaction_details_section', [$bpElementorWidgetObj, 'transaction_details_section']);
        add_action('better_payment/elementor/editor/layouts_form_settings_amount_list_section', [$bpElementorWidgetObj, 'amount_list_section']);
        add_action('better_payment/elementor/editor/manage_response_webhook', [$bpElementorWidgetObj, 'manage_response_webhook'], 10, 2);
        add_filter('better_payment/elementor/widget/layouts', [$bpElementorWidgetObj, 'get_pro_layouts']);

        add_action('better_payment/widget/user-dashboard/subscriptions_tab', [$userDashboardObj, 'subscriptions_tab'], 10, 2);
        add_action('better_payment/widget/user-dashboard/dashboard_tab_analytics_reports', [$userDashboardObj, 'dashboard_tab_analytics_reports'], 10, 2);
        add_action('better_payment/widget/user-dashboard/dashboard_tab_recurring_subscriptions', [$userDashboardObj, 'dashboard_tab_recurring_subscriptions'], 10, 2);
        add_action('better_payment/widget/user-dashboard/dashboard_tab_split_subscriptions', [$userDashboardObj, 'dashboard_tab_split_subscriptions'], 10, 2);
        
        add_action('better_payment/elementor/user_dashboard/layout_settings_header_after', [$userDashboardObj, 'layout_settings_header_after_controls']);
        add_action('better_payment/elementor/user_dashboard/content_settings_no_items_after', [$userDashboardObj, 'content_settings_no_items_after_controls']);
        add_filter('better_payment/elementor/user_dashboard/bp_settings', [$userDashboardObj, 'bp_settings'], 10, 2);
    }
}
