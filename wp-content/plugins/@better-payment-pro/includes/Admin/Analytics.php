<?php

namespace Better_Payment\Pro\Admin;

use Better_Payment\Lite\Admin\DB;
use Better_Payment\Lite\Classes\Handler;
use Better_Payment\Pro\Contracts\Pageable;
use Better_Payment\Pro\Controller;
use Better_Payment\Pro\Models\AnalyticsModel;

use function Better_Payment\Lite\Classes\better_payment_dd;

/**
 * The Analytics handler class
 * 
 * @since 0.0.1
 */
class Analytics extends Controller implements Pageable{

    /**
     * Initialize the class
     * 
     * @since 0.0.1
     */
    public function __construct( ) {
        
    }

    /**
     * Render the analytics page
     *
     * @return void
     * @since 0.0.1
     */
    public function render_page() {
        $data = array();
        $analytics_model = new AnalyticsModel();
        wp_enqueue_script('better-payment-pro-chartjs-script');
        wp_enqueue_script('better-payment-pro-analytics-script');

        $from_cache = !empty($_GET['cache_clear']) && floatval(sanitize_text_field($_GET['cache_clear'])) == 1 ? 0 : 1;

        $data['all_transactions_amount'] = $analytics_model->all_transactions_amount($from_cache);
        $data['completed_transactions_amount'] = $analytics_model->completed_transactions_amount($from_cache);
        $data['incomplete_transactions_amount'] = $analytics_model->incomplete_transactions_amount($from_cache);
        $data['refund_transactions_amount'] = $analytics_model->refund_transactions_amount($from_cache);
        $data['unpaid_transactions_amount'] = $analytics_model->unpaid_transactions_amount($from_cache);

        $data['transaction_amount_currency'] = Handler::get_currency_symbols('USD');
        $data['analytics_time_period_today'] = date($this->fetch_date_format());
        $data['analytics_time_period_week'] = date($this->fetch_date_format(), strtotime('-1 week'));
        $data['analytics_time_period_month'] = date($this->fetch_date_format(), strtotime('-1 month'));
        $data['analytics_time_period_year'] = date($this->fetch_date_format(), strtotime('-1 year'));

        $data['analytics'] = $this->get_analytics_data_for_chart(
            ['all'], 
            ['week'], 
            $data['analytics_time_period_today'], 
            $data['analytics_time_period_week'], 
            $data['analytics_time_period_month'], 
            $data['analytics_time_period_year'], 
            '',
            '',
        );

        wp_localize_script( 'better-payment-pro-analytics-script', 'betterPaymentProAnalytics', [
            'analytics' => $data['analytics'],
        ] );

        $this->view('page-analytics', $data);
    }

    /**
     * Handle analytics filter content : AJAX request
     *
     * @return void
     * @since 1.0.0
     */
    public function analytics_filter_content() {
        if ( ! wp_verify_nonce( $_REQUEST['_wpnonce'], 'better-payment-pro-admin-nonce' ) ) {
            wp_send_json_error( [
                'message' => __( 'Nonce verification failed!', 'better-payment-pro' )
            ] );
        }

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( [
                'message' => __( 'No permission!', 'better-payment-pro' )
            ] );
        }

        global $better_payment_pro_config;

        $formFields = array();

        if(isset($_POST['data'])){
            parse_str($_POST['data'], $formFields); 
        }

        $formFieldsSanitized = map_deep( $formFields, 'sanitize_text_field' );

        $analytics_transaction_types = isset($formFieldsSanitized['analytics_transaction_types']) ? map_deep($formFieldsSanitized['analytics_transaction_types'], 'sanitize_text_field') : array(); 
        $analytics_time_period = isset($formFieldsSanitized['analytics_time_period']) ? map_deep($formFieldsSanitized['analytics_time_period'], 'sanitize_text_field') : array(); 
        
        $analytics_time_period_today = !empty($formFieldsSanitized['analytics_time_period_today']) ? sanitize_text_field($formFieldsSanitized['analytics_time_period_today']) : ''; 
        $analytics_time_period_week = !empty($formFieldsSanitized['analytics_time_period_week']) ? sanitize_text_field($formFieldsSanitized['analytics_time_period_week']) : ''; 
        $analytics_time_period_month = !empty($formFieldsSanitized['analytics_time_period_month']) ? sanitize_text_field($formFieldsSanitized['analytics_time_period_month']) : ''; 
        $analytics_time_period_year = !empty($formFieldsSanitized['analytics_time_period_year']) ? sanitize_text_field($formFieldsSanitized['analytics_time_period_year']) : ''; 
        $analytics_time_period_start_date_custom = !empty($formFieldsSanitized['analytics_time_period_start_date_custom']) ? sanitize_text_field($formFieldsSanitized['analytics_time_period_start_date_custom']) : ''; 
        $analytics_time_period_end_date_custom = !empty($formFieldsSanitized['analytics_time_period_end_date_custom']) ? sanitize_text_field($formFieldsSanitized['analytics_time_period_end_date_custom']) : ''; 
        
        wp_enqueue_script('better-payment-pro-chartjs-script');
        wp_enqueue_script('better-payment-pro-analytics-script');

        $analytics = $this->get_analytics_data_for_chart(
            $analytics_transaction_types,
            $analytics_time_period, 
            $analytics_time_period_today, 
            $analytics_time_period_week, 
            $analytics_time_period_month, 
            $analytics_time_period_year, 
            $analytics_time_period_start_date_custom, 
            $analytics_time_period_end_date_custom, 
        );
        // ob_start();
        // include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/template-analytics-content.php";
        // $analytics_filter_content = ob_get_contents();
        // ob_end_clean();

        wp_send_json_success([
            'message' => __( 'Filter applied successfully!', 'better-payment-pro' ),
            // 'analytics_filter_content' => $analytics_filter_content,
            'analytics' => $analytics,
        ]);
    }

    public function fetch_date_format($format = 'Y-m-d H:i:s'){
        return $format;
    }

    public function get_formatted_transactions_for_chart($transactions){
        $transactions_formatted = array();

        if(count($transactions)){
            
            foreach($transactions as $transaction){
                $payment_date = isset($transaction->payment_date) ? $transaction->payment_date : '';
                $amount = isset($transaction->amount) ? $transaction->amount : 0;
                
                if($payment_date){
                    $date_index = date('d M, y', strtotime($payment_date));
                
                    if(isset($transactions_formatted[ $date_index ])){
                        $transactions_formatted[ $date_index ] = $transactions_formatted[ $date_index ] + $amount;
                    } else {
                        $transactions_formatted[ $date_index ] = $amount;
                    }
                }
            }

        }

        return $transactions_formatted;
    }

    public function get_analytics_data_for_chart(
        $analytics_transaction_types,
        $analytics_time_period, 
        $analytics_time_period_today, 
        $analytics_time_period_week, 
        $analytics_time_period_month, 
        $analytics_time_period_year, 
        $analytics_time_period_start_date_custom, 
        $analytics_time_period_end_date_custom,
        $type = 'admin'
        ){
        $analytics_model = new AnalyticsModel();
        $from_cache = 0;

        $payment_date_from = '';
        $payment_date_to = '';

        if( is_array($analytics_time_period) && in_array('week', $analytics_time_period) ){
            $payment_date_from = $analytics_time_period_week;
            $payment_date_to = $analytics_time_period_today;
        } 
        
        if( is_array($analytics_time_period) && in_array('month', $analytics_time_period) ){
            $payment_date_from = $analytics_time_period_month;
            $payment_date_to = $analytics_time_period_today;
        } 
        
        if( is_array($analytics_time_period) && in_array('year', $analytics_time_period) ){
            $payment_date_from = $analytics_time_period_year;
            $payment_date_to = $analytics_time_period_today;
        } 

        if( is_array($analytics_time_period) && in_array('custom', $analytics_time_period) ){
            $payment_date_from = $analytics_time_period_start_date_custom;
            $payment_date_to = $analytics_time_period_end_date_custom;
        }

        $payment_date_from = date('Y-m-d H:i:s', strtotime($payment_date_from));
        $payment_date_to = date('Y-m-d H:i:s', strtotime($payment_date_to));
        

        $current_user = wp_get_current_user();
        $user_email = 'admin' == $type ? '' : $current_user->user_email;

        $all_transactions = $analytics_model->all_transactions($from_cache, $payment_date_from, '', $user_email);
        $completed_transactions = $analytics_model->completed_transactions($from_cache, $payment_date_from, '', $user_email);
        $incomplete_transactions = $analytics_model->incomplete_transactions($from_cache, $payment_date_from, '', $user_email);
        $refund_transactions = $analytics_model->refund_transactions($from_cache, $payment_date_from, '', $user_email);

        $all_transactions_formatted = $this->get_formatted_transactions_for_chart($all_transactions);
        $completed_transactions_formatted = $this->get_formatted_transactions_for_chart($completed_transactions);
        $incomplete_transactions_formatted = $this->get_formatted_transactions_for_chart($incomplete_transactions);
        $refund_transactions_formatted = $this->get_formatted_transactions_for_chart($refund_transactions);
        
        $payment_date_period_index = array();
        for($date = $payment_date_from; $date <= $payment_date_to; $date = date('Y-m-d', strtotime($date . ' +1 day')) ){
            $payment_date_period_index[] = date('d M, y', strtotime($date));
        }
        
        $show_all_four_stats = is_array($analytics_transaction_types) && in_array('all', $analytics_transaction_types) ? 1 : 0;
        $all_transactions_show = is_array($analytics_transaction_types) && in_array('total-transaction', $analytics_transaction_types) ? 1 : 0;
        $completed_transactions_show = is_array($analytics_transaction_types) && in_array('completed-transaction', $analytics_transaction_types) ? 1 : 0;
        $incomplete_transactions_show = is_array($analytics_transaction_types) && in_array('incomplete-transaction', $analytics_transaction_types) ? 1 : 0;
        $refund_transactions_show = is_array($analytics_transaction_types) && in_array('refund-transaction', $analytics_transaction_types) ? 1 : 0;

        $analytics = [
            'all_transactions' => $all_transactions_formatted,
            'completed_transactions' => $completed_transactions_formatted,
            'incomplete_transactions' => $incomplete_transactions_formatted,
            'refund_transactions' => $refund_transactions_formatted,
            'show_all_four_stats' => $show_all_four_stats,
            'all_transactions_show' => $all_transactions_show,
            'completed_transactions_show' => $completed_transactions_show,
            'incomplete_transactions_show' => $incomplete_transactions_show,
            'refund_transactions_show' => $refund_transactions_show,
            'payment_date_period_index' => $payment_date_period_index,
            'all_transactions_total' => number_format(array_sum($all_transactions_formatted), 2),
            'completed_transactions_total' => number_format(array_sum($completed_transactions_formatted), 2),
            'incomplete_transactions_total' => number_format(array_sum($incomplete_transactions_formatted), 2),
            'refund_transactions_total' => number_format(array_sum($refund_transactions_formatted), 2),
            'all_transactions_count' => count($all_transactions),
            'completed_transactions_count' => count($completed_transactions),
            'incomplete_transactions_count' => count($incomplete_transactions),
            'refund_transactions_count' => count($refund_transactions),
        ];

        return $analytics;
    }

}