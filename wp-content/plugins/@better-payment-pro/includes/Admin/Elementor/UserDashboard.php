<?php

namespace Better_Payment\Pro\Admin\Elementor;

use Better_Payment\Lite\Admin\DB;
use Better_Payment\Pro\Admin\Analytics;
use Better_Payment\Pro\Config;
use Better_Payment\Pro\Controller;
use Elementor\Controls_Manager;

use function Better_Payment\Lite\Classes\better_payment_dd;

/**
 * Widget handler class
 * 
 * @since 0.0.1
 */
class UserDashboard extends Controller {

    /**
     * Initialize the class
     * 
     * @since 0.0.1
     */
    function __construct() {

    }

    /**
     * Widget style depends for editor
     *
     * @return void
     * @since 0.0.1
     */
    public function get_style_depends( $styleDepends ){
        $styleDependsPro = [
            'bp-icon-front', 
            'better-payment-pro-style', 
            'better-payment-pro-admin-style', 
            'better-payment-pro-common-style',
        ];

        $styleDepends = array_merge( $styleDepends, $styleDependsPro );
        return $styleDepends;
    }

    /**
     * Widget script depends for editor
     *
     * @return void
     * @since 0.0.1
     */
    public function get_script_depends( $scriptDepends ){
        $scriptDependsPro = [
            'better-payment-pro-admin-script',
            'better-payment-pro-common-script',
            'better-payment-pro-chartjs-script', 
            'better-payment-pro-analytics-script',
        ];

        $analyticsObj = new Analytics();
        $data = [];

        $data['analytics_time_period_today'] = \date($analyticsObj->fetch_date_format());
        $data['analytics_time_period_week'] = \date($analyticsObj->fetch_date_format(), strtotime('-1 week'));
        $data['analytics_time_period_month'] = \date($analyticsObj->fetch_date_format(), strtotime('-1 month'));
        $data['analytics_time_period_year'] = \date($analyticsObj->fetch_date_format(), strtotime('-1 year'));

        $data['analytics'] = $analyticsObj->get_analytics_data_for_chart(
            ['all'],
            ['year'],
            $data['analytics_time_period_today'],
            $data['analytics_time_period_week'],
            $data['analytics_time_period_month'],
            $data['analytics_time_period_year'],
            '',
            '',
            'user'
        );
        
        wp_localize_script('better-payment-pro-analytics-script', 'betterPaymentProAnalytics', [
            'analytics' => $data['analytics'],
        ]);

        $scriptDepends = array_merge( $scriptDepends, $scriptDependsPro );
        return $scriptDepends;
    }
    
    public function subscriptions_tab( $settings, $bp_settings ){
        ob_start();
		include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/elementor/user-dashboard/template-subscriptions-tab.php";
        $subscriptions_tab_content = ob_get_contents();
        ob_end_clean();

        echo $subscriptions_tab_content;
    }
    
    public function dashboard_tab_analytics_reports( $settings, $bp_settings ){
        ob_start();
		include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/elementor/user-dashboard/template-dashboard-tab-analytics-reports.php";
        $dashboard_tab_content = ob_get_contents();
        ob_end_clean();

        echo $dashboard_tab_content;
    }
    
    public function dashboard_tab_recurring_subscriptions( $settings, $bp_settings ){
        ob_start();
		include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/elementor/user-dashboard/template-dashboard-tab-recurring-subscriptions.php";
        $dashboard_tab_content = ob_get_contents();
        ob_end_clean();

        echo $dashboard_tab_content;
    }
    
    public function dashboard_tab_split_subscriptions( $settings, $bp_settings ){
        ob_start();
		include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/elementor/user-dashboard/template-dashboard-tab-split-subscriptions.php";
        $dashboard_tab_content = ob_get_contents();
        ob_end_clean();

        echo $dashboard_tab_content;
    }

    public function get_user_subscriptions( $email = '' ){
        $current_user = wp_get_current_user();

        if( empty($email) ) {
            $email = $current_user->user_email;
        }

        $bp_admin_completed_transactions = DB::get_transactions_by_email( $email );

        $subscriptions = [];
        if ( is_array( $bp_admin_completed_transactions ) && count( $bp_admin_completed_transactions )) {

            $subscriptions = array_filter($bp_admin_completed_transactions, function($better_payment_transaction_obj) {
                $form_fields_info = maybe_unserialize($better_payment_transaction_obj->form_fields_info);

                return !empty($form_fields_info['subscription_id']) 
                    && !empty($form_fields_info['subscription_customer_id']) 
                    && !empty($form_fields_info['subscription_plan_id']);
            });
        }

        return $subscriptions;
    }
    
    public function get_product_details_by_plan_id( $plan_id ){
        $better_payment_pro_config = new Config();
        
        $stripeSecretKey = $better_payment_pro_config->stripe_secret_key_test;
        
        if ( ! empty( $better_payment_pro_config->stripe_live_mode ) && 'yes' === $better_payment_pro_config->stripe_live_mode ) {
            $stripeSecretKey = $better_payment_pro_config->stripe_secret_key_live;
        }
        if ( empty( $stripeSecretKey ) || empty( $plan_id ) ) {
            return false;
        }

        $stripe = new \Stripe\StripeClient( $stripeSecretKey );
        
        try {
            $price = $stripe->prices->retrieve($plan_id);
            $product = $stripe->products->retrieve($price->product);

        } catch (\Stripe\Exception\ApiErrorException $e) {
            // Handle the exception if something goes wrong
            $product = false;
        }

        return $product;
    }

    public function cencel_subscription(){
        if ( ! wp_verify_nonce( sanitize_text_field( $_REQUEST['nonce'] ), 'better-payment-pro-admin-nonce' ) ) {
            wp_send_json_error( [
                'message' => __( 'Nonce verification failed!', 'better-payment-pro' )
            ] );
        }

        $subscription_id = sanitize_text_field( $_REQUEST['subscription_id'] );

        $subscriptions = $this->get_user_subscriptions();

        if ( empty( $subscriptions ) ) {
            wp_send_json_error( [
                'message' => __( 'No subscription found!', 'better-payment-pro' )
            ] );
        }

        // Validation
        $subscriptionExists = false;
        $subscriptionNeeded = false;

        foreach ($subscriptions as $subscription) {
            $form_fields_info = maybe_unserialize($subscription->form_fields_info);
            $is_payment_split_payment = ! empty( $form_fields_info['is_payment_split_payment'] ) ? intval( $form_fields_info['is_payment_split_payment'] ) : 0;
            
            if ( $is_payment_split_payment ) {
                $subscriptionNeeded  = true;
            }

            if (!empty($form_fields_info['subscription_id']) && $form_fields_info['subscription_id'] === $subscription_id) {
                $subscriptionExists = true;
                break;
            }
        }

        if ( $subscriptionNeeded ) {
            wp_send_json_error( [
                'message' => __( 'Subscription can not be deleted!', 'better-payment-pro' )
            ] );
        }
        
        if ( ! $subscriptionExists ) {
            wp_send_json_error( [
                'message' => __( 'Subscription id not found!', 'better-payment-pro' )
            ] );
        }

        $better_payment_pro_config = new Config();
        
        $stripeSecretKey = $better_payment_pro_config->stripe_secret_key_test;
        
        if ( ! empty( $better_payment_pro_config->stripe_live_mode ) && 'yes' === $better_payment_pro_config->stripe_live_mode ) {
            $stripeSecretKey = $better_payment_pro_config->stripe_secret_key_live;
        }
        if ( empty( $stripeSecretKey ) || empty( $subscription_id ) ) {
            wp_send_json_error( [
                'message' => __( 'Something went wrong!', 'better-payment-pro' ),
            ] );
        }

        $stripe = new \Stripe\StripeClient( $stripeSecretKey );
        
        try {
            $subscription = $stripe->subscriptions->cancel($subscription_id);

        } catch (\Stripe\Exception\ApiErrorException $e) {
            // Handle the exception if something goes wrong
            $subscription = false;
        }
        
        // Update DB
        if ( $subscription && is_object( $subscription )) {
            global $wpdb;
            $table   = "{$wpdb->prefix}better_payment";

            $results = $wpdb->get_row(
                $wpdb->prepare( "SELECT id,obj_id,transaction_id,form_fields_info,referer FROM $table WHERE transaction_id=%s limit 1", sanitize_text_field( $subscription_id ) )
            );

            $form_fields_info = maybe_unserialize($results->form_fields_info);
            
            $current_subscription_status = ! empty( $form_fields_info['subscription_status'] ) ? $form_fields_info['subscription_status'] : '';
            
            $form_fields_info['subscription_status']        = ! empty( $subscription->status ) ? sanitize_text_field( $subscription->status ) : sanitize_text_field( $current_subscription_status );
            $form_fields_info['subscription_canceled_at']   = ! empty( $subscription->canceled_at ) ? sanitize_text_field( $subscription->canceled_at ) : '';
            
            $updated = $wpdb->update(
                $table,
                array(
                    'form_fields_info'  => maybe_serialize( $form_fields_info ),
                ),
                array( 'ID' => $results->id )
            );

        }

        wp_send_json_success( [
            'message' => __( 'Successfully unsubscribed!', 'better-payment-pro' ),
            'subscription_id' => $subscription_id,
        ] );
    }

    public function layout_settings_header_after_controls( $widgetObj ) {
        $widgetObj->add_control(
			'better_payment_user_dashboard_layout_subscriptions_list_label',
			[
				'label' => esc_html__( 'Subscriptions List', 'better-payment-pro' ),
				'type'  => Controls_Manager::HEADING,
                'separator' => 'before',
			]
		);

        $widgetObj->add_control(
			'better_payment_user_dashboard_subscriptions_list_subscription_id_show',
			[
				'label'        => __( 'Subscription ID', 'better-payment-pro' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Show', 'better-payment-pro' ),
				'label_off'    => __( 'Hide', 'better-payment-pro' ),
				'return_value' => 'yes',
				'default'      => 'yes',
			]
		);

        $widgetObj->add_control(
			'better_payment_user_dashboard_subscriptions_list_plan_id_show',
			[
				'label'        => __( 'Product Name', 'better-payment-pro' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Show', 'better-payment-pro' ),
				'label_off'    => __( 'Hide', 'better-payment-pro' ),
				'return_value' => 'yes',
				'default'      => 'yes',
			]
		);

        $widgetObj->add_control(
			'better_payment_user_dashboard_subscriptions_list_status_show',
			[
				'label'        => __( 'Status', 'better-payment-pro' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Show', 'better-payment-pro' ),
				'label_off'    => __( 'Hide', 'better-payment-pro' ),
				'return_value' => 'yes',
				'default'      => 'yes',
			]
		);

        $widgetObj->add_control(
			'better_payment_user_dashboard_subscriptions_list_amount_show',
			[
				'label'        => __( 'Amount', 'better-payment-pro' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Show', 'better-payment-pro' ),
				'label_off'    => __( 'Hide', 'better-payment-pro' ),
				'return_value' => 'yes',
				'default'      => 'yes',
			]
		);

        $widgetObj->add_control(
			'better_payment_user_dashboard_subscriptions_list_created_date_show',
			[
				'label'        => __( 'Payment Date', 'better-payment-pro' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Show', 'better-payment-pro' ),
				'label_off'    => __( 'Hide', 'better-payment-pro' ),
				'return_value' => 'yes',
				'default'      => 'yes',
			]
		);

        $widgetObj->add_control(
			'better_payment_user_dashboard_subscriptions_list_current_period_show',
			[
				'label'        => __( 'Renewal Date', 'better-payment-pro' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Show', 'better-payment-pro' ),
				'label_off'    => __( 'Hide', 'better-payment-pro' ),
				'return_value' => 'yes',
				'default'      => 'yes',
			]
		);

        $widgetObj->add_control(
			'better_payment_user_dashboard_subscriptions_list_action_show',
			[
				'label'        => __( 'Action', 'better-payment-pro' ),
				'type'         => Controls_Manager::SWITCHER,
				'label_on'     => __( 'Show', 'better-payment-pro' ),
				'label_off'    => __( 'Hide', 'better-payment-pro' ),
				'return_value' => 'yes',
				'default'      => 'yes',
			]
		);
    }
    
    public function content_settings_no_items_after_controls( $widgetObj ) {
        $widgetObj->add_control(
			'better_payment_user_dashboard_content_subscriptions_list_label',
			[
				'label' => esc_html__( 'Subscriptions List', 'better-payment-pro' ),
				'type'  => Controls_Manager::HEADING,
                'separator' => 'before',
			]
		);

        $widgetObj->add_control( 
            'better_payment_user_dashboard_subscriptions_list_subscription_id_label', 
            [
                'label'       => esc_html__( 'Subscription ID', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => false,
                'default'     => esc_html__( 'Subscription ID', 'better-payment-pro' ),
                'ai' => [
                    'active' => false,
                ],
	    	] 
        );

        $widgetObj->add_control( 
            'better_payment_user_dashboard_subscriptions_list_plan_id_label', 
            [
                'label'       => esc_html__( 'Product Name', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => false,
                'default'     => esc_html__( 'Product Name', 'better-payment-pro' ),
                'ai' => [
                    'active' => false,
                ],
	    	] 
        );

        $widgetObj->add_control( 
            'better_payment_user_dashboard_subscriptions_list_status_label', 
            [
                'label'       => esc_html__( 'Status', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => false,
                'default'     => esc_html__( 'Status', 'better-payment-pro' ),
                'ai' => [
                    'active' => false,
                ],
	    	] 
        );
        
        $widgetObj->add_control( 
            'better_payment_user_dashboard_subscriptions_list_amount_label', 
            [
                'label'       => esc_html__( 'Amount', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => false,
                'default'     => esc_html__( 'Amount', 'better-payment-pro' ),
                'ai' => [
                    'active' => false,
                ],
	    	] 
        );
        
        $widgetObj->add_control( 
            'better_payment_user_dashboard_subscriptions_list_created_date_label', 
            [
                'label'       => esc_html__( 'Payment Date', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => false,
                'default'     => esc_html__( 'Payment Date', 'better-payment-pro' ),
                'ai' => [
                    'active' => false,
                ],
	    	] 
        );
        
        $widgetObj->add_control( 
            'better_payment_user_dashboard_subscriptions_list_current_period_label', 
            [
                'label'       => esc_html__( 'Renewal Date', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => false,
                'default'     => esc_html__( 'Renewal Date', 'better-payment-pro' ),
                'ai' => [
                    'active' => false,
                ],
	    	] 
        );
        
        $widgetObj->add_control( 
            'better_payment_user_dashboard_subscriptions_list_action_label', 
            [
                'label'       => esc_html__( 'Action', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => false,
                'default'     => esc_html__( 'Action', 'better-payment-pro' ),
                'ai' => [
                    'active' => false,
                ],
	    	]
        );

        $widgetObj->add_control( 
            'better_payment_user_dashboard_subscriptions_list_status_active_label', 
            [
                'label'       => esc_html__( 'Status » Active', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => false,
                'default'     => esc_html__( 'Active', 'better-payment-pro' ),
                'ai' => [
                    'active' => false,
                ],
	    	]
        );
        
        $widgetObj->add_control( 
            'better_payment_user_dashboard_subscriptions_list_status_inactive_label', 
            [
                'label'       => esc_html__( 'Status » Inactive', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => false,
                'default'     => esc_html__( 'Inactive', 'better-payment-pro' ),
                'ai' => [
                    'active' => false,
                ],
	    	]
        );

        $widgetObj->add_control(
            'better_payment_user_dashboard_subscriptions_list_action_cancel_label', 
            [
                'label'       => esc_html__( 'Action » Cancel', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'label_block' => false,
                'default'     => esc_html__( 'Cancel', 'better-payment-pro' ),
                'ai' => [
                    'active' => false,
                ],
	    	]
        );
    }

    public function bp_settings( $bp_settings, $settings ) {
        $bp_settings_pro = [];

        $bp_settings_pro['subscription_table_subscription_id_show'] = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_subscription_id_show']) && 'yes' === $settings['better_payment_user_dashboard_subscriptions_list_subscription_id_show'];
        $bp_settings_pro['subscription_table_plan_id_show'] = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_plan_id_show']) && 'yes' === $settings['better_payment_user_dashboard_subscriptions_list_plan_id_show'];
        $bp_settings_pro['subscription_table_status_show'] = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_status_show']) && 'yes' === $settings['better_payment_user_dashboard_subscriptions_list_status_show'];
        $bp_settings_pro['subscription_table_amount_show'] = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_amount_show']) && 'yes' === $settings['better_payment_user_dashboard_subscriptions_list_amount_show'];
        $bp_settings_pro['subscription_table_created_date_show'] = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_created_date_show']) && 'yes' === $settings['better_payment_user_dashboard_subscriptions_list_created_date_show'];
        $bp_settings_pro['subscription_table_current_period_show'] = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_current_period_show']) && 'yes' === $settings['better_payment_user_dashboard_subscriptions_list_current_period_show'];
        $bp_settings_pro['subscription_table_action_show'] = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_action_show']) && 'yes' === $settings['better_payment_user_dashboard_subscriptions_list_action_show'];
        
        $bp_settings_pro['subscription_table_subscription_id_label']  = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_subscription_id_label']) ? $settings['better_payment_user_dashboard_subscriptions_list_subscription_id_label'] : 'Subscription ID';
        $bp_settings_pro['subscription_table_plan_id_label']  = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_plan_id_label']) ? $settings['better_payment_user_dashboard_subscriptions_list_plan_id_label'] : 'Product Name';
        $bp_settings_pro['subscription_table_status_label']  = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_status_label']) ? $settings['better_payment_user_dashboard_subscriptions_list_status_label'] : 'Status';
        $bp_settings_pro['subscription_table_amount_label']  = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_amount_label']) ? $settings['better_payment_user_dashboard_subscriptions_list_amount_label'] : 'Amount';
        $bp_settings_pro['subscription_table_created_date_label']  = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_created_date_label']) ? $settings['better_payment_user_dashboard_subscriptions_list_created_date_label'] : 'Payment Date';
        $bp_settings_pro['subscription_table_current_period_label']  = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_current_period_label']) ? $settings['better_payment_user_dashboard_subscriptions_list_current_period_label'] : 'Current Period';
        $bp_settings_pro['subscription_table_action_label']  = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_action_label']) ? $settings['better_payment_user_dashboard_subscriptions_list_action_label'] : 'Action';
        $bp_settings_pro['subscription_table_status_active_label']  = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_status_active_label']) ? $settings['better_payment_user_dashboard_subscriptions_list_status_active_label'] : 'Active';
        $bp_settings_pro['subscription_table_status_inactive_label']  = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_status_inactive_label']) ? $settings['better_payment_user_dashboard_subscriptions_list_status_inactive_label'] : 'Inactive';
        $bp_settings_pro['subscription_table_action_cancel_label']  = ! empty( $settings['better_payment_user_dashboard_subscriptions_list_action_cancel_label']) ? $settings['better_payment_user_dashboard_subscriptions_list_action_cancel_label'] : 'Cancel';

        $bp_settings = array_merge($bp_settings_pro, $bp_settings);

        return $bp_settings;
    }
}