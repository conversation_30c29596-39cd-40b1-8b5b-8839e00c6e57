<?php

namespace Better_Payment\Pro\Admin\Elementor;

use Better_Payment\Pro\Config;
use Better_Payment\Pro\Controller;
use Elementor\Controls_Manager;

use function Better_Payment\Lite\Classes\better_payment_dd;

/**
 * Widget handler class
 * 
 * @since 0.0.1
 */
class Widget extends Controller{

    /**
     * Initialize the class
     * 
     * @since 0.0.1
     */
    function __construct() {

    }

    /**
     * Widget style depends for editor
     *
     * @return void
     * @since 0.0.1
     */
    public function get_style_depends( $styleDepends ){
        $styleDependsPro = [
            'bp-icon-front', 
            'better-payment-pro-style', 
            'better-payment-pro-admin-style', 
            'better-payment-pro-common-style',
        ];

        $styleDepends = array_merge( $styleDepends, $styleDependsPro );
        return $styleDepends;
    }

    /**
     * Widget script depends for editor
     *
     * @return void
     * @since 0.0.1
     */
    public function get_script_depends( $scriptDepends ){
        $scriptDependsPro = [
            'better-payment-pro-admin-script',
            'better-payment-pro-common-script',
        ];

        $scriptDepends = array_merge( $scriptDepends, $scriptDependsPro );
        return $scriptDepends;
    }

    public function get_pro_layouts( $layouts ){
        $layouts_pro = [
            'layout-4-pro' => 'Layout 4 | General',
            'layout-5-pro' => 'Layout 5 | Donation',
            'layout-6-pro' => 'Layout 6 | Woo',
        ];
        $layouts = array_merge( $layouts, $layouts_pro );

        return $layouts;
    }
    
    public function transaction_details_section( $widgetObj ){
        $widgetObj->add_control(
            'layout_sidebar_total_amount_text_layout_4_pro',
            [
                'label'       => esc_html__( 'Amount Text', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'default'     => __( 'Total amount', 'better-payment-pro' ),
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_form_layout' => 'layout-4-pro',
                ],
            ]
        );

        $widgetObj->add_control(
            'layout_sidebar_total_amount_text_layout_6_pro',
            [
                'label'       => esc_html__( 'Amount Text', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'default'     => __( 'Order details', 'better-payment-pro' ),
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_form_layout' => 'layout-6-pro',
                ],
            ]
        );

        $widgetObj->add_control(
            'better_payment_form_transaction_details_split_payment_text',
            [
                'label'       => esc_html__( 'Split Payment', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'default'     => __( 'Split Payment', 'better-payment-pro' ),
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_form_payment_type' => 'split-payment',
                ],
            ]
        );

        $widgetObj->add_control(
            'better_payment_form_transaction_details_installment_text',
            [
                'label'       => esc_html__( 'Installment', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'default'     => __( 'Installment', 'better-payment-pro' ),
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_form_payment_type' => 'split-payment',
                ],
            ]
        );
    }
    
    public function amount_list_section( $widgetObj ){
        $widgetObj->add_control(
            'better_payment_show_amount_quantity',
            [
                'label'        => __( 'Show Quantity', 'better-payment-pro' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Yes', 'better-payment-pro' ),
                'label_off'    => __( 'No', 'better-payment-pro' ),
                'return_value' => 'yes',
                'condition' => [
                    'better_payment_form_layout' => ['layout-4-pro', 'layout-5-pro'],
                    'better_payment_show_amount_list_layout_4_5_6' => 'yes',
                ],
            ]
        );

        $widgetObj->add_control(
            'better_payment_show_amount_quantity_text',
            [
                'label'       => esc_html__( 'Quantity Text', 'better-payment-pro' ),
                'type'        => Controls_Manager::TEXT,
                'default'     => __( 'Quantity', 'better-payment-pro' ),
                'label_block' => true,
                'ai' => [
                    'active' => false,
                ],
                'condition' => [
                    'better_payment_form_layout' => ['layout-4-pro', 'layout-5-pro'],
                    'better_payment_show_amount_list_layout_4_5_6' => 'yes',
                ],
            ]
        );
    }
    
    public function manage_response_webhook( $widgetObj, $settings ){
        $status = false;

        if ( ! empty( $_REQUEST[ 'webhook-stripe' ] ) ) {
            $status = $this->manage_response_webhook_stripe( $settings );
        }

        return $status;
    }

    protected function manage_response_webhook_stripe( $settings = [] ) {
        $status = false;

        $stripeSecretKey = ! empty( $settings[ 'better_payment_stripe_secret_key' ] ) ? sanitize_text_field( $settings[ 'better_payment_stripe_secret_key' ] ) : '';
        $endpointSecret = ! empty( $settings[ 'better_payment_form_recurring_webhook_stripe_secret' ] ) ? sanitize_text_field( $settings[ 'better_payment_form_recurring_webhook_stripe_secret' ] ) : '';
        
        if ( empty( $stripeSecretKey ) || empty( $endpointSecret ) ) {
            return $status;
        }

        \Stripe\Stripe::setApiKey( $stripeSecretKey );

        $payload = @file_get_contents('php://input');
        $event = null;

        if ( $endpointSecret ) {
            $sig_header = ! empty( $_SERVER['HTTP_STRIPE_SIGNATURE'] ) ? sanitize_text_field( $_SERVER['HTTP_STRIPE_SIGNATURE'] ) : '';
            try {
                $event = \Stripe\Webhook::constructEvent(
                $payload, $sig_header, $endpointSecret
                );
            } catch(\Stripe\Exception\SignatureVerificationException $e) {
                // Invalid signature
                echo '⚠️  Webhook error while validating signature.';
                http_response_code(400);
                return $status;
            }
        }

        global $wpdb;
        $table   = "{$wpdb->prefix}better_payment";

        $paymentIntent = ! empty( $event->data->object ) ? $event->data->object : ''; // contains a \Stripe\PaymentIntent
        $transaction_id = ! empty( $paymentIntent->subscription ) ? $paymentIntent->subscription : '';

        $results = $wpdb->get_row(
            $wpdb->prepare( "SELECT id,obj_id,transaction_id,form_fields_info,referer FROM $table WHERE transaction_id=%s limit 1", sanitize_text_field( $transaction_id ) )
        );

        $form_fields_info = maybe_unserialize($results->form_fields_info);

        switch ($event->type) {
            case 'invoice.paid':
                $form_fields_info['subscription_current_period_start'] = ! empty( $paymentIntent->lines->data[0]->period->start ) ? intval( $paymentIntent->lines->data[0]->period->start ) : intval( $form_fields_info['subscription_current_period_start'] );
                $form_fields_info['subscription_current_period_end'] = ! empty( $paymentIntent->lines->data[0]->period->end ) ? intval( $paymentIntent->lines->data[0]->period->end ) : intval( $form_fields_info['subscription_current_period_end'] );
                $form_fields_info['subscription_status'] = ! empty( $paymentIntent->status ) ? sanitize_text_field( $paymentIntent->status ) : sanitize_text_field( $form_fields_info['subscription_status'] );
                $form_fields_info['subscription_payment_intent_id'] = ! empty( $paymentIntent->id ) ? sanitize_text_field( $paymentIntent->id ) : '';
                
                $updated = $wpdb->update(
                    $table,
                    array(
                        'status'            => sanitize_text_field( $form_fields_info['subscription_status'] ),
                        'form_fields_info'  => maybe_serialize( $form_fields_info ),
                    ),
                    array( 'ID' => $results->id )
                );

                $status = true;
                break;
            case 'invoice.payment_failed':
                $form_fields_info['subscription_status'] = ! empty( $paymentIntent->status ) ? sanitize_text_field( $paymentIntent->status ) : sanitize_text_field( $form_fields_info['subscription_status'] );
                $form_fields_info['subscription_payment_intent_id'] = ! empty( $paymentIntent->id ) ? sanitize_text_field( $paymentIntent->id ) : '';
                
                $updated = $wpdb->update(
                    $table,
                    array(
                        'status'            => sanitize_text_field( $form_fields_info['subscription_status'] ),
                        'form_fields_info'  => maybe_serialize( $form_fields_info ),
                    ),
                    array( 'ID' => $results->id )
                );

                $status = true;
                break;
            default:
                // Unexpected event type
                error_log('Received unknown event type');
        }

        http_response_code(200);
        return $status; // exit
    }

    public function stripe_payment_success( $action_data ) {
        $is_payment_split_payment = ! empty( $action_data['is_payment_split_payment'] ) && 1 === intval( $action_data['is_payment_split_payment'] ); 
        $recurring_unit_amount_paid = 0;
        $split_payment_installment_price_id = '';

        if ( $is_payment_split_payment ) {
            $form_fields_info = ! empty( $action_data['form_fields_info'] ) ? $action_data['form_fields_info'] : []; 
            $split_payment_iterations = ! empty( $form_fields_info['split_payment_installment_iteration'] ) ? intval( $form_fields_info['split_payment_installment_iteration'] ) : 1; // initial iteration done at first payment
            $split_payment_installment_price_id = ! empty( $form_fields_info['split_payment_installment_price_id'] ) ? sanitize_text_field( $form_fields_info['split_payment_installment_price_id'] ) : '';
            
            $better_payment_pro_config = new Config();
        
            $stripeSecretKey = $better_payment_pro_config->stripe_secret_key_test;
            
            if ( ! empty( $better_payment_pro_config->stripe_live_mode ) && 'yes' === $better_payment_pro_config->stripe_live_mode ) {
                $stripeSecretKey = $better_payment_pro_config->stripe_secret_key_live;
            }

            if ( empty( $stripeSecretKey ) ) {
                wp_send_json_error( [
                    'message' => __( 'Something went wrong!', 'better-payment-pro' ),
                ] );
            }

            $stripe = new \Stripe\StripeClient( $stripeSecretKey );
            
            try {
                //
                $checkout_session = $stripe->checkout->sessions->retrieve( $action_data['checkout_session_id'] );
                $subscription_id = ! empty( $checkout_session->subscription ) ? $checkout_session->subscription : '';

                $recurring_interval = 'month';
                
                if ( ! empty( $split_payment_installment_price_id ) ) {
                    $recurring_price_details = $stripe->prices->retrieve( $split_payment_installment_price_id);
                    $recurring_interval = ! empty( $recurring_price_details->recurring->interval ) ? sanitize_text_field( $recurring_price_details->recurring->interval ) : $recurring_interval;
                    $recurring_unit_amount_paid = ! empty( $recurring_price_details->unit_amount ) ? floatval( ( $recurring_price_details->unit_amount ) / 100 ) : $recurring_unit_amount_paid;
                }
                
                $cancel_time = strtotime("+$split_payment_iterations {$recurring_interval}s");
                
                if ( ! empty( $split_payment_iterations ) ) {
                    $subscription_updated = $stripe->subscriptions->update(
                        $subscription_id,
                        [ 
                            'cancel_at' => $cancel_time,
                        ]
                    );
                }

                global $wpdb;
                $table   = "{$wpdb->prefix}better_payment";

                $results = $wpdb->get_row(
                    $wpdb->prepare( "SELECT id,obj_id,transaction_id,form_fields_info,referer FROM $table WHERE obj_id=%s limit 1", sanitize_text_field( $action_data['checkout_session_id'] ) )
                );

                if ( ! empty( $results->id ) ) {
                    $form_fields_info = maybe_unserialize($results->form_fields_info); // from db
                    $form_fields_info['split_payment_installment_amount'] = sanitize_text_field( $recurring_unit_amount_paid );
                    $form_fields_info['split_payment_cancels_at'] = $cancel_time;

                    $updated = $wpdb->update(
                        $table,
                        array(
                            'amount'            => sanitize_text_field( $recurring_unit_amount_paid ),
                            'form_fields_info'  => maybe_serialize( $form_fields_info ),
                        ),
                        array( 'ID' => $results->id )
                    );
                }

            } catch (\Stripe\Exception\ApiErrorException $e) {
                // Handle the exception if something goes wrong
                echo $e->getMessage();
            }

        }
    }
}