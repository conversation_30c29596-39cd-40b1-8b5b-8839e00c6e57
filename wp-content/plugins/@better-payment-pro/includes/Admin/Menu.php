<?php

namespace Better_Payment\Pro\Admin;

use Better_Payment\Pro\Controller;

/**
 * The Menu handler class
 * 
 * @since 0.0.1
 */
class Menu extends Controller{

    /**
     * Initialize the class
     * 
     * @since 0.0.1
     */
    function __construct( ) {
        
    }

    /**
     * Menu subpages
     *
     * @return array
     * @since 0.0.1 
     */
    public function get_page_menu_slug_list($menu_slug_list) {
        
        if(!is_array($menu_slug_list)){
            return $menu_slug_list;
        }

        $menu_slug_list = array_merge(
            $menu_slug_list, 
            array(
                'better-payment-analytics',
            )
        );
        
        return $menu_slug_list;
    }

    /**
     * Submenu configuration
     *
     * @return array
     * @since 0.0.1
     */
    public function get_submenu_page_list($submenu_page_list, $page_slug_prefix) {
        
        if(!is_array($submenu_page_list)){
            return $submenu_page_list;
        }
        
        $analyticsObj = new Analytics();
        
        $submenu_page_list = array_merge(
            $submenu_page_list, 
            array(
                $page_slug_prefix . '-analytics'   => array(
                    'title'      => __('Analytics', 'better-payment-pro'),
                    'capability' => 'manage_options',
                    'callback'   => [$analyticsObj, 'render_page'],
                ),
            )
        );
        
        return $submenu_page_list;
    }

}