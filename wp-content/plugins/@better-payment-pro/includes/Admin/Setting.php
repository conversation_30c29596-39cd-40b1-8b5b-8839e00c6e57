<?php

namespace Better_Payment\Pro\Admin;

use Better_Payment\Pro\Controller;
use Better_Payment\Pro\Traits\Helper;

/**
 * The Transaction handler class
 * 
 * @since 0.0.1
 */
class Setting extends Controller {

    use Helper;

    protected $args = [];

    /**
     * Initialize the class
     * 
     * @since 0.0.1
     */
    function __construct( ) {
        $this->args = $this->get_licensing_arguments();
    }

    /**
	 * Renders the settings page for entering license information.
	 * 
	 * @since 0.0.1
	 */
	public function render_licenses_page() {
        $hidden_license_key = $this->get_hidden_license_key();
		$status      = $this->get_license_status();
		
        wp_enqueue_style('better-payment-pro-admin-style');
        wp_enqueue_script('better-payment-pro-admin-script');
		?>
		<div class="better-payment-license-wrapper">
			<form method="post" id="better-payment-license-form">
				<?php 
	            settings_fields('better-payment-settings');
				
				ob_start();
				if ($status == false || $status !== 'valid') {
					include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . '/partials/license-activate.php';
				}

				if ($status !== false && $status == 'valid') {
					include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . '/partials/license-deactivate.php';
				}
				
				$content = ob_get_clean();
				printf($content);
				?>

			</form>
		</div>
	<?php
	}

	/**
     * Gets the current license status
     * @return bool|string   The product license key, or false if not set
     */
    public function get_license_status()
    {
        $status = get_option($this->args['item_slug'] . '_license_status', false);
        return ($status === false || $status === "") ? false : trim($status);
    }

	/**
     * Gets the currently set license key in a hidden way
     * @return string   The product license key
     */
    private function get_hidden_license_key()
    {
        $input_string = $this->get_license_key();
        $length = mb_strlen($input_string) - 10; // 5 - 5;
        return substr_replace($input_string, mb_substr(preg_replace('/\S/', '*', $input_string), 5, $length), 5, $length);
    }

	/**
     * Gets the currently set license key
     * @return bool|string   The product license key, or false if not set
     */
    private function get_license_key()
    {
        $license = get_option($this->args['item_slug'] . '_license');
        if (! $license) {
            return false;
        }
        return trim($license);
    }
}