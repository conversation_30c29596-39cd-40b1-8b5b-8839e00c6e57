<?php

namespace Better_Payment\Pro\Admin;

use Better_Payment\Pro\Controller;
use Better_Payment\Pro\Traits\Helper;

/**
 * The Transaction handler class
 * 
 * @since 0.0.1
 */
class Transaction extends Controller{

    private $table_name = 'better_payment';
    /**
     * Initialize the class
     * 
     * @since 0.0.1
     */
    function __construct( ) {
        
    }

    /**
     * Transaction refund content
     *
     * @return string
     * @since 0.0.1 
     */
    public function transaction_refund_content($transaction_obj) {
        wp_enqueue_style( 'better-payment-pro-admin-style' );
        wp_enqueue_script( 'better-payment-pro-admin-script' );
        wp_enqueue_style( 'better-payment-pro-common-style' );
        wp_enqueue_script( 'better-payment-pro-common-script' );
        
        $better_payment_transaction_obj = $transaction_obj;
        $form_fields_info   = [];
        $is_imported        = 0;
        if ( isset( $better_payment_transaction_obj->form_fields_info ) ) {
            $form_fields_info = maybe_unserialize( $better_payment_transaction_obj->form_fields_info );
            $is_imported = isset( $form_fields_info['is_imported'] ) ? intval( $form_fields_info['is_imported'] ) : $is_imported;
        }
        
        ob_start();
        include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/template-transaction-refund.php";
        $transaction_refund_content = ob_get_contents();
        ob_end_clean();

        echo $transaction_refund_content;
    }

    /**
     * Transaction receipt content
     *
     * @return string
     * @since 0.0.1 
     */
    public function transaction_receipt_content($transaction_obj) {
        wp_enqueue_style( 'better-payment-pro-admin-style' );
        wp_enqueue_script( 'better-payment-pro-admin-script' );
        wp_enqueue_style( 'better-payment-pro-common-style' );
        wp_enqueue_script( 'better-payment-pro-common-script' );
        
        $better_payment_transaction_obj = $transaction_obj;
        ob_start();
		include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/template-transaction-receipt.php";
        $transaction_receipt_content = ob_get_contents();
        ob_end_clean();

        echo $transaction_receipt_content;
    }
    
    /**
     * Transaction subscription content
     *
     * @return string
     * @since 0.3.0
     */
    public function transaction_subscription_content( $transaction_obj ) {
        $better_payment_transaction_obj = $transaction_obj;
        ob_start();
		include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/template-transaction-subscription.php";
        $transaction_receipt_content = ob_get_contents();
        ob_end_clean();

        echo $transaction_receipt_content;
    }

    /**
     * Fetch a single transaction from the DB
     *
     * @param  string $order_id
     *
     * @return object
     * @since 0.0.1
     */
    function get_transaction_by_order_id( $order_id ) {
        global $wpdb;
        
        $transaction = $wpdb->get_row(
            $wpdb->prepare( "SELECT * FROM {$wpdb->prefix}{$this->table_name} WHERE order_id = %s", $order_id )
        );

        return $transaction;
    }

    /**
     * Update transaction status in the DB
     *
     * @param  string $order_id
     *
     * @return object
     * @since 0.0.1
     */
    function update_transaction_status( $order_id, $status = '', $refund_info = '' ) {
        global $wpdb;
        
        $is_refunded = $wpdb->update( $wpdb->prefix . $this->table_name, array( 
            'status' => sanitize_text_field($status),
            'refund_info' => maybe_serialize($refund_info)
         ), array( 'order_id' => sanitize_text_field($order_id) ) );

        return $is_refunded;
    }
}