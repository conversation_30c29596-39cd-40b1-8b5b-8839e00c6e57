<div class="better-payment">
    <div class="better-payment-wrapper">
        <div class="better-payment-container">
            <?php

            use Better_Payment\Lite\Classes\Helper;

            $better_payment_helper_obj = new Helper();

            include BETTER_PAYMENT_ADMIN_VIEWS_PATH . "/partials/layout-vars.php";
            include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/partials/layout-vars.php";

            $render_attribute_default_text = $widgetObj->render_attribute_default_text( $settings );
            ?>

            <form name="better-payment-form-<?php echo esc_attr($widgetObj->get_id()); ?>" data-better-payment="<?php echo esc_attr($layout_setting_meta); ?>" class="better-payment-form" id="better-payment-form-<?php echo esc_attr($widgetObj->get_id()); ?>" action="<?php echo esc_url($layout_action); ?>" method="post">
                <input type="hidden" name="better_payment_page_id" value="<?php echo esc_attr(get_the_ID()); ?>">
                <input type="hidden" name="better_payment_widget_id" value="<?php echo esc_attr($widgetObj->get_id()); ?>">
                
                <?php if ( $is_payment_recurring || $is_payment_split_payment ) : ?>
                <input type="hidden" name="better_payment_recurring_mode" value="subscription">
                <input type="hidden" name="better_payment_recurring_price_id" value="<?php echo esc_attr( $recurring_price_id ); ?>">
                <?php endif; ?>
                
                <div class="layout-4-general reversed1">
                    <div class="transaction-details-wrap">
                        <div class="transaction-details-header">
                            <h3 class="transaction-details-header-title"><?php esc_html_e($layout_form_transaction_details_heading, 'better-payment-pro'); ?></h3>
                            <p class="transaction-details-header-paragraph"><?php esc_html_e($layout_form_transaction_details_sub_heading, 'better-payment-pro'); ?></p>
                        </div>

                        <div class="transaction-details-footer">
                            <ul>
                                <li class="total-amount-text-wrap">
                                    <span class="total-amount-text"><?php esc_html_e($total_amount_text, 'better-payment-pro'); ?></span>
                                    
                                    <?php if ( $is_payment_recurring || $is_payment_split_payment ) : ?>
                                    <span class="total-amount-number"><?php printf('%s<span class="total-amount-number-inner bp-transaction-details-amount-text">%s</span>%s', esc_html($layout_form_currency_left), '' !== $product_price ? esc_html($product_price) : $render_attribute_default_text, esc_html($layout_form_currency_right)); ?></span>
                                    <?php else : ?>
                                    <span class="total-amount-number"><?php printf('%s<span class="total-amount-number-inner bp-transaction-details-amount-text">%s</span>%s', esc_html($layout_form_currency_left), '' !== $product_price ? floatval($product_price) : $render_attribute_default_text, esc_html($layout_form_currency_right)); ?></span>
                                    <?php endif; ?>
                                    
                                </li>

                                <?php if ( $is_payment_split_payment ) : ?>
                                <li class="split-payment-text-wrap">
                                    <span class="split-payment-text"><?php esc_html_e( $split_payment_text, 'better-payment-pro'); ?></span>
                                    <select class="form-control" name="split_payment_installment" id="split_payment_installment">
                                        <option class="split-payment-default-value-option" value="">-</option>
                                        
                                        <?php if ( is_array( $split_payment_installments ) && count( $split_payment_installments ) ) : ?>
                                            <?php foreach( $split_payment_installments as $split_payment_installment ) : ?>
                                                <option data-unit-amount="<?php echo esc_html( $split_payment_installment['installment_unit_amount'] ); ?>" data-interval="<?php echo esc_html( $split_payment_installment['installment_interval'] ); ?>" value="<?php echo esc_html( $split_payment_installment['installment_price_id'] ); ?>"><?php echo esc_html( $split_payment_installment['installment_price_name'] ); ?></option>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </select>
                                </li>
                                <li class="split-payment-note-wrap split-payment-installment-note-wrap is-hidden">
                                    <span><?php esc_html_e("$installment_text ", 'better-payment-pro'); ?></span> <span class="split-payment-installment-note-amount" data-currency-left="<?php echo esc_html( $layout_form_currency_left ); ?>" data-currency-right="<?php echo esc_html( $layout_form_currency_right ); ?>" ></span><?php echo esc_html($layout_form_currency_right); ?>
                                </li>
                                <?php endif; ?>

                            </ul>
                        </div>
                    </div>

                    <div class="general-form-wrap">
                        <h3 class="payment-details-title"><?php esc_html_e($form_title_text, 'better-payment-pro'); ?></h3>
                        <div class="mb-4 payment-method-items">
                            <?php
                            $is_paypal_enabled = !empty($settings['better_payment_form_paypal_enable']) && 'yes' === $settings['better_payment_form_paypal_enable'];
                            $is_stripe_enabled = !empty($settings['better_payment_form_stripe_enable']) && 'yes' === $settings['better_payment_form_stripe_enable'];
                            $is_paystack_enabled = !empty($settings['better_payment_form_paystack_enable']) && 'yes' === $settings['better_payment_form_paystack_enable'];
                            ?>

                            <?php if (true === $is_paypal_enabled) : ?>
                                <label class="payment-method-item payment-method-paypal">
                                    <input type="radio" name="payment_method" class="layout-payment-method-paypal" checked>
                                    <div class="payment-method-image-wrap">
                                        <span class="payment-method-image">
                                            <img src="<?php echo esc_url(BETTER_PAYMENT_ASSETS . '/img/paypal.png'); ?>" alt="paypal-image">
                                        </span>
                                    </div>
                                </label>
                            <?php endif; ?>

                            <?php if (true === $is_stripe_enabled) : ?>
                                <label class="payment-method-item payment-method-stripe">
                                    <input type="radio" name="payment_method" class="layout-payment-method-stripe" <?php echo !$is_paypal_enabled ? 'checked' : '' ?>>
                                    <div class="payment-method-image-wrap">
                                        <span class="payment-method-image">
                                            <img src="<?php echo esc_url(BETTER_PAYMENT_PRO_ASSETS . '/img/stripe.png'); ?>" alt="stripe-image">
                                        </span>
                                    </div>
                                </label>
                            <?php endif; ?>

                            <?php if (true === $is_paystack_enabled) : ?>
                                <label class="payment-method-item payment-method-paystack">
                                    <input type="radio" name="payment_method" class="layout-payment-method-paystack" <?php echo (!$is_paypal_enabled) && (!$is_stripe_enabled) ? 'checked' : '' ?>>
                                    <div class="payment-method-image-wrap">
                                        <span class="payment-method-image">
                                            <img src="<?php echo esc_url(BETTER_PAYMENT_PRO_ASSETS . '/img/paystack.png'); ?>" alt="paystack-image">
                                        </span>
                                    </div>
                                </label>
                            <?php endif; ?>
                        </div>

                        <?php 
                        if ( ! empty( $settings['better_payment_form_fields_layout_4_5_6'] ) ) :
                            foreach (  $settings['better_payment_form_fields_layout_4_5_6'] as $item ) :
                                if ( isset( $item["better_payment_field_name_show"] ) && 'yes' !== $item["better_payment_field_name_show"] ) {
                                    $item_primary_field_type = !empty($item["better_payment_primary_field_type"]) ? $item["better_payment_primary_field_type"] : "";
                                    $is_payment_amount_field = 'primary_payment_amount' === $item_primary_field_type ? 1 : 0;
                                    $is_payment_amount_field_hidden = $is_payment_amount_field ? 1 : 0;

                                    continue;
                                }

                                include BETTER_PAYMENT_ADMIN_VIEWS_PATH . "/partials/layout-repeater-vars.php";
                                include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/partials/layout-repeater-vars.php";

                                if ( $is_payment_amount_field ) : 
                                ?>
                                    <div class="mb-4 bp-payment-amount-wrap payment-amount-wrap <?php echo esc_attr($layout_put_amount_field_hide_show); ?>">
                                        <label class="label">
                                            <?php esc_html_e( $render_attribute_label, 'better-payment-pro' ); ?> 
                                            <?php if ($render_attribute_required) : ?> 
                                                <span class="required">*</span> 
                                            <?php endif; ?> 
                                        </label>

                                        <?php
                                        if ( ! empty( $settings['better_payment_show_amount_list_layout_4_5_6'] ) && 'yes' === $settings['better_payment_show_amount_list_layout_4_5_6'] ) :
                                        ?>
                                            <div class="payment-amounts">
                                                <?php $this->render_amount_element($settings, ['version' => 'v2']); ?>
                                            </div>
                                        <?php 
                                        endif;
                                        ?>    
                                    </div>
                                <?php 
                                endif;
                                ?>
                                <div class="better-payment-field-advanced-layout field-<?php echo esc_attr($render_attribute_name); ?> elementor-repeater-item-<?php echo esc_attr($item['_id']); ?> <?php echo esc_attr( $payment_amount_field_class ); ?> <?php echo esc_attr( $field_display_inline_class ); ?>">
                                    <div class="form-group mb-4">
                                        <?php if ( ! $is_payment_amount_field ) : ?>
                                        <label class="label">
                                            <?php esc_html_e( $render_attribute_label, 'better-payment-pro' ); ?> 
                                            <?php if ($render_attribute_required) : ?> 
                                                <span class="required">*</span> 
                                            <?php endif; ?> 
                                        </label>
                                        <?php endif; ?> 

                                        <input 
                                            type="<?php echo esc_attr( $render_attribute_type ); ?>" 
                                            class="form-control <?php echo esc_attr( $render_attribute_class ); ?>" 
                                            placeholder="<?php echo esc_attr( $render_attribute_placeholder_pro ); ?>" 
                                            name="<?php echo esc_attr( $render_attribute_name ); ?>" 
                                            
                                            <?php if ($render_attribute_required) : ?> 
                                                required="<?php echo esc_attr($render_attribute_required); ?>" 
                                            <?php endif; ?> 
                                            
                                            <?php if ($is_payment_amount_field) : ?>
                                                step="any"
                                                min="<?php echo esc_attr( $render_attribute_min ); ?>" 
                                                max="<?php echo esc_attr( $render_attribute_max ); ?>" 
                                                value="<?php echo esc_attr( $render_attribute_default ); ?>" 
                                                <?php echo esc_attr( $render_attribute_default_fixed ); ?> 
                                            <?php endif; ?> 
                                            >
                                    </div>
                                </div>
                                
                                <?php if ( $is_payment_amount_field ) : ?>
                                <div class="payment-amounts-quantity">
                                    <?php if ( ! empty( $settings['better_payment_show_amount_quantity'] ) && 'yes' === $settings['better_payment_show_amount_quantity'] ) : ?>
                                        <label class="label"><?php esc_html_e( $amount_quantity_text, 'better-payment-pro' ); ?></label>

                                        <div class="better-payment-field-advanced-layout field-payment_amount_quantity">
                                            <div class="form-group mb-4">
                                                <input type="number" class="form-control bp-custom-payment-amount-quantity" placeholder="Quantity" name="payment_amount_quantity" step="any" min="1" max="" value="1">
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div></div> <!-- To fix css issue  -->
                                <?php endif; ?>
                                
                                <?php 
                            endforeach;
                        endif;
                        ?>

                        <?php if ( 0 === $payment_amount_field_exists ) : ?>
                            <div class="mb-4 bp-payment-amount-wrap payment-amount-wrap <?php echo esc_attr($layout_put_amount_field_hide_show); ?> <?php echo esc_attr( $payment_amount_field_class ); ?>">
                                <label class="label">
                                    <?php esc_html_e( 'Amount', 'better-payment-pro' ); ?> 
                                    <?php if ($render_attribute_required) : ?> 
                                        <span class="required">*</span> 
                                    <?php endif; ?> 
                                </label>

                                <?php
                                if ( ! empty( $settings['better_payment_show_amount_list_layout_4_5_6'] ) && 'yes' === $settings['better_payment_show_amount_list_layout_4_5_6'] ) {
                                    ?>
                                    <div class="payment-amounts">
                                        <?php $this->render_amount_element($settings, ['version' => 'v2']); ?>
                                    </div>
                                    <?php 
                                }
                                ?>    
                            </div>
                        <?php endif; ?>

                        <?php
                        if ($is_paypal_enabled) {
                            echo Better_Payment\Lite\Classes\Handler::paypal_button(esc_attr($widgetObj->get_id()), $settings, ['extra_classes' => 'payment-button']);
                        }

                        if ($is_stripe_enabled) {
                            echo Better_Payment\Lite\Classes\Handler::stripe_button(esc_attr($widgetObj->get_id()), $settings, ['extra_classes' => 'payment-button']);
                        }

                        if ($is_paystack_enabled) {
                            echo Better_Payment\Lite\Classes\Handler::paystack_button(esc_attr($widgetObj->get_id()), $settings, ['extra_classes' => 'payment-button']);
                        }
                        ?>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>