<div class="better-payment">
    <div class="better-payment-wrapper">
        <div class="better-payment-container">
            <?php

            use Better_Payment\Lite\Classes\Helper;

            $better_payment_helper_obj = new Helper();

            include BETTER_PAYMENT_ADMIN_VIEWS_PATH . "/partials/layout-vars.php";
            include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/partials/layout-vars.php";

            $render_attribute_default_text = $this->render_attribute_default_text( $settings );
            ?>
            
            <form name="better-payment-form-<?php echo esc_attr($widgetObj->get_id()); ?>" data-better-payment="<?php echo esc_attr($layout_setting_meta); ?>" class="better-payment-form" id="better-payment-form-<?php echo esc_attr($widgetObj->get_id()); ?>" action="<?php echo esc_url($layout_action); ?>" method="post">
                <input type="hidden" name="better_payment_page_id" value="<?php echo esc_attr(get_the_ID()); ?>">
                <input type="hidden" name="better_payment_widget_id" value="<?php echo esc_attr($widgetObj->get_id()); ?>">
                
                <?php if ( $is_payment_recurring || $is_payment_split_payment ) : ?>
                <input type="hidden" name="better_payment_recurring_mode" value="subscription">
                <input type="hidden" name="better_payment_recurring_price_id" value="<?php echo esc_attr( $recurring_price_id ); ?>">
                <?php endif; ?>             

                <div class="layout-5-donation reversed1">
                    <div class="donation-form-wrap">
                        <div class="donation-form-header">
                            <div class="icon">
                                <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M18.6698 23.005L23.0098 23.0058C27.9804 23.0058 32.0098 27.0352 32.0098 32.0058H18.0078L18.0098 34.0058L34.0098 34.0042V32.0058C34.0098 29.841 33.3734 27.7996 32.2376 26.0042L38.0098 26.0058C41.9944 26.0058 45.4346 28.3364 47.042 31.7086C42.3124 37.95 34.6536 42.0058 26.0098 42.0058C20.4872 42.0058 15.8089 40.8246 12.0091 38.7564L12.0118 20.148C14.5029 20.5046 16.7824 21.5172 18.6698 23.005ZM10.0098 38.0058C10.0098 39.1104 9.11435 40.0058 8.00977 40.0058H4.00977C2.90521 40.0058 2.00977 39.1104 2.00977 38.0058V20.0058C2.00977 18.9013 2.90521 18.0059 4.00977 18.0059H8.00977C9.11435 18.0059 10.0098 18.9013 10.0098 20.0058V38.0058ZM36.0098 10.0059C39.3234 10.0059 42.0098 12.6922 42.0098 16.0059C42.0098 19.3196 39.3234 22.0058 36.0098 22.0058C32.696 22.0058 30.0098 19.3196 30.0098 16.0059C30.0098 12.6922 32.696 10.0059 36.0098 10.0059ZM22.0098 4.00586C25.3234 4.00586 28.0098 6.69216 28.0098 10.0059C28.0098 13.3196 25.3234 16.0059 22.0098 16.0059C18.6961 16.0059 16.0098 13.3196 16.0098 10.0059C16.0098 6.69216 18.6961 4.00586 22.0098 4.00586Z" fill="#13AE5C" />
                                </svg>
                            </div>
                            <h3 class="payment-details-title"><?php esc_html_e($form_title_text, 'better-payment-pro'); ?></h3>
                        </div>

                        <div class="donation-form-body">
                            <div class="payment-method-items-wrap">
                                <p class="label"><?php esc_html_e( $form_sub_title_text, 'better-payment-pro' ); ?></p>
                                <div class="payment-method-items">
                                    <?php
                                    $is_paypal_enabled = !empty($settings['better_payment_form_paypal_enable']) && 'yes' === $settings['better_payment_form_paypal_enable'];
                                    $is_stripe_enabled = !empty($settings['better_payment_form_stripe_enable']) && 'yes' === $settings['better_payment_form_stripe_enable'];
                                    $is_paystack_enabled = !empty($settings['better_payment_form_paystack_enable']) && 'yes' === $settings['better_payment_form_paystack_enable'];
                                    ?>

                                    <?php if (true === $is_paypal_enabled) : ?>
                                        <label class="payment-method-item payment-method-paypal">
                                            <input type="radio" name="payment_method" class="layout-payment-method-paypal" checked>
                                            <span class="indicator"></span>
                                            <span class="text"><?php echo esc_html('PayPal', 'better-payment-pro'); ?></span>
                                        </label>
                                    <?php endif; ?>

                                    <?php if (true === $is_stripe_enabled) : ?>
                                        <label class="payment-method-item payment-method-stripe">
                                            <input type="radio" name="payment_method" class="layout-payment-method-stripe" <?php echo !$is_paypal_enabled ? 'checked' : '' ?>>
                                            <span class="indicator"></span>
                                            <span class="text"><?php echo esc_html('Stripe', 'better-payment-pro'); ?></span>
                                        </label>
                                    <?php endif; ?>

                                    <?php if (true === $is_paystack_enabled) : ?>
                                        <label class="payment-method-item payment-method-paystack">
                                            <input type="radio" name="payment_method" class="layout-payment-method-paystack" <?php echo (!$is_paypal_enabled) && (!$is_stripe_enabled) ? 'checked' : '' ?>>
                                            <span class="indicator"></span>
                                            <span class="text"><?php echo esc_html('Paystack', 'better-payment-pro'); ?></span>
                                        </label>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="payment-label-wrap">
                            <p class="label"><?php esc_html_e('Payment type', 'better-payment-pro'); ?></p>
                            </div>
                            <div class="payment-type-items-wrap bp-flex">
                                <div class="payment-type-item-wrap">
                                    <div class="payment-type-items <?php echo $is_payment_split_payment ? esc_attr(' split-payment') : ''; ?> <?php echo $is_payment_recurring ? ' recurring' : ''; ?>" data-price_id="<?php echo esc_attr( $recurring_price_id ); ?>" data-amount="<?php echo esc_attr( $recurring_unit_amount ); ?>">
                                        <label class="payment-type-item payment-type-one-time bp-w-100">
                                            <input type="radio" name="payment_type" checked value="">
                                            <div>
                                                <span class="indicator">
                                                    <svg viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <g clip-path="url(#clip0_48_121)">
                                                            <path d="M2.25 9C2.25 9.88642 2.42459 10.7642 2.76381 11.5831C3.10303 12.4021 3.60023 13.1462 4.22703 13.773C4.85382 14.3998 5.59794 14.897 6.41689 15.2362C7.23583 15.5754 8.11358 15.75 9 15.75C9.88642 15.75 10.7642 15.5754 11.5831 15.2362C12.4021 14.897 13.1462 14.3998 13.773 13.773C14.3998 13.1462 14.897 12.4021 15.2362 11.5831C15.5754 10.7642 15.75 9.88642 15.75 9C15.75 8.11358 15.5754 7.23583 15.2362 6.41689C14.897 5.59794 14.3998 4.85382 13.773 4.22703C13.1462 3.60023 12.4021 3.10303 11.5831 2.76381C10.7642 2.42459 9.88642 2.25 9 2.25C8.11358 2.25 7.23583 2.42459 6.41689 2.76381C5.59794 3.10303 4.85382 3.60023 4.22703 4.22703C3.60023 4.85382 3.10303 5.59794 2.76381 6.41689C2.42459 7.23583 2.25 8.11358 2.25 9Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                            <path d="M6.75 9L8.25 10.5L11.25 7.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                        </g>
                                                        <defs>
                                                            <clipPath id="clip0_48_121">
                                                                <rect width="18" height="18" fill="white" />
                                                            </clipPath>
                                                        </defs>
                                                    </svg>
                                                </span>
                                                
                                                <?php if ( $is_payment_split_payment ) : ?>
                                                    <span class="text total-amount-pre-text">One Time </span>
                                                <?php endif; ?>

                                                <span class="text"><?php esc_html_e( ucfirst( $payment_type_text ), 'better-payment-pro'); ?></span>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                
                                <?php if ( isset( $recurring_payments ) && is_array( $recurring_payments ) && count( $recurring_payments ) ) : ?>
                                    <?php foreach( $recurring_payments as $recurring_payment ) : ?>
                                        <div class="payment-type-item-wrap">
                                            <div class="payment-type-items <?php echo $is_payment_recurring ? esc_attr(' recurring') : ''; ?>" data-price_id="<?php echo esc_attr( $recurring_payment['recurring_price_id'] ); ?>" data-amount="<?php echo esc_attr( $recurring_payment['recurring_unit_amount'] ); ?>">
                                                <label class="payment-type-item payment-type-one-time bp-w-100">
                                                    <input type="radio" name="payment_type" value="<?php echo esc_attr( $recurring_payment['recurring_price_id'] ); ?>">
                                                    <div>
                                                        <span class="indicator">
                                                            <svg viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <g clip-path="url(#clip0_48_121)">
                                                                    <path d="M2.25 9C2.25 9.88642 2.42459 10.7642 2.76381 11.5831C3.10303 12.4021 3.60023 13.1462 4.22703 13.773C4.85382 14.3998 5.59794 14.897 6.41689 15.2362C7.23583 15.5754 8.11358 15.75 9 15.75C9.88642 15.75 10.7642 15.5754 11.5831 15.2362C12.4021 14.897 13.1462 14.3998 13.773 13.773C14.3998 13.1462 14.897 12.4021 15.2362 11.5831C15.5754 10.7642 15.75 9.88642 15.75 9C15.75 8.11358 15.5754 7.23583 15.2362 6.41689C14.897 5.59794 14.3998 4.85382 13.773 4.22703C13.1462 3.60023 12.4021 3.10303 11.5831 2.76381C10.7642 2.42459 9.88642 2.25 9 2.25C8.11358 2.25 7.23583 2.42459 6.41689 2.76381C5.59794 3.10303 4.85382 3.60023 4.22703 4.22703C3.60023 4.85382 3.10303 5.59794 2.76381 6.41689C2.42459 7.23583 2.25 8.11358 2.25 9Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                                    <path d="M6.75 9L8.25 10.5L11.25 7.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                                </g>
                                                                <defs>
                                                                    <clipPath id="clip0_48_121">
                                                                        <rect width="18" height="18" fill="white" />
                                                                    </clipPath>
                                                                </defs>
                                                            </svg>
                                                        </span>

                                                        <?php if ( $is_payment_recurring ) : ?>
                                                            <span class="text total-amount-pre-text"><?php echo esc_html( $recurring_payment['recurring_price_name'] ); ?></span>
                                                        <?php endif; ?>

                                                        <span class="text"><?php echo ' $' . esc_html( $recurring_payment['recurring_unit_amount'] ); ?></span>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>

                                <?php if ( isset( $split_payment_installments ) && is_array( $split_payment_installments ) && count( $split_payment_installments ) ) : ?>
                                    <?php foreach( $split_payment_installments as $split_payment_installment ) : ?>
                                        <div class="payment-type-item-wrap">
                                            <div class="payment-type-items <?php echo $is_payment_split_payment ? esc_attr(' split-payment') : ''; ?>" data-price_id="<?php echo esc_attr( $split_payment_installment['installment_price_id'] ); ?>" data-amount="<?php echo esc_attr( $split_payment_installment['installment_unit_amount'] ); ?>">
                                                <label class="payment-type-item payment-type-one-time bp-w-100">
                                                    <input type="radio" name="payment_type" value="<?php echo esc_attr( $split_payment_installment['installment_price_id'] ); ?>">
                                                    <div>
                                                        <span class="indicator">
                                                            <svg viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <g clip-path="url(#clip0_48_121)">
                                                                    <path d="M2.25 9C2.25 9.88642 2.42459 10.7642 2.76381 11.5831C3.10303 12.4021 3.60023 13.1462 4.22703 13.773C4.85382 14.3998 5.59794 14.897 6.41689 15.2362C7.23583 15.5754 8.11358 15.75 9 15.75C9.88642 15.75 10.7642 15.5754 11.5831 15.2362C12.4021 14.897 13.1462 14.3998 13.773 13.773C14.3998 13.1462 14.897 12.4021 15.2362 11.5831C15.5754 10.7642 15.75 9.88642 15.75 9C15.75 8.11358 15.5754 7.23583 15.2362 6.41689C14.897 5.59794 14.3998 4.85382 13.773 4.22703C13.1462 3.60023 12.4021 3.10303 11.5831 2.76381C10.7642 2.42459 9.88642 2.25 9 2.25C8.11358 2.25 7.23583 2.42459 6.41689 2.76381C5.59794 3.10303 4.85382 3.60023 4.22703 4.22703C3.60023 4.85382 3.10303 5.59794 2.76381 6.41689C2.42459 7.23583 2.25 8.11358 2.25 9Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                                    <path d="M6.75 9L8.25 10.5L11.25 7.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                                                                </g>
                                                                <defs>
                                                                    <clipPath id="clip0_48_121">
                                                                        <rect width="18" height="18" fill="white" />
                                                                    </clipPath>
                                                                </defs>
                                                            </svg>
                                                        </span>

                                                        <?php if ( $is_payment_split_payment ) : ?>
                                                            <span class="text total-amount-pre-text"><?php echo esc_html( $split_payment_installment['installment_price_name'] ); ?></span>
                                                        <?php endif; ?>

                                                        <span class="text"><?php echo ' $' . esc_html( $split_payment_installment['installment_unit_amount'] ); ?></span>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                
                                <?php if ( $is_payment_split_payment ) : ?>
                                    <div class="payment-type-item-wrap" id="split-payment-installment-wrap">
                                        <label class="payment-type-item payment-type-split-payment">
                                            <div class="label">
                                                <span class="text"><?php esc_html_e( ucfirst( $split_payment_text ), 'better-payment-pro'); ?></span>
                                            </div>
                                            <div class="form-group">
                                                <select class="form-control" name="split_payment_installment" id="split_payment_installment">
                                                    <option class="split-payment-default-value-option" value="">-</option>

                                                    <?php if ( is_array( $split_payment_installments ) && count( $split_payment_installments ) ) : ?>
                                                        <?php foreach( $split_payment_installments as $split_payment_installment ) : ?>
                                                            <option data-unit-amount="<?php echo esc_attr( $split_payment_installment['installment_unit_amount'] ); ?>" data-interval="<?php echo esc_attr( $split_payment_installment['installment_interval'] ); ?>" value="<?php echo esc_attr( $split_payment_installment['installment_price_id'] ); ?>"><?php echo esc_attr( $split_payment_installment['installment_price_name'] ); ?></option>
                                                        <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                        </label>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <?php 
                            if ( ! empty( $settings['better_payment_form_fields_layout_4_5_6_desc'] ) ) :
                                foreach (  $settings['better_payment_form_fields_layout_4_5_6_desc'] as $item ) :
                                    if ( isset( $item["better_payment_field_name_show"] ) && 'yes' !== $item["better_payment_field_name_show"] ) {
                                        $item_primary_field_type = !empty($item["better_payment_primary_field_type"]) ? $item["better_payment_primary_field_type"] : "";
                                        $is_payment_amount_field = 'primary_payment_amount' === $item_primary_field_type ? 1 : 0;
                                        $is_payment_amount_field_hidden = $is_payment_amount_field ? 1 : 0;

                                        continue;
                                    }

                                    include BETTER_PAYMENT_ADMIN_VIEWS_PATH . "/partials/layout-repeater-vars.php";
                                    include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/partials/layout-repeater-vars.php";

                                    if ( $is_payment_amount_field ) : 
                                    ?>
                                        <div class="mb-4 bp-payment-amount-wrap payment-amount-wrap <?php esc_attr_e($layout_put_amount_field_hide_show, 'better-payment-pro'); ?>">
                                            <label class="label">
                                                <?php esc_html_e( $render_attribute_label, 'better-payment-pro' ); ?>
                                                <?php if ($render_attribute_required) : ?>
                                                    <span class="required">*</span>
                                                <?php endif; ?>
                                            </label>

                                            <?php
                                            if ( ! empty( $settings['better_payment_show_amount_list_layout_4_5_6'] ) && 'yes' === $settings['better_payment_show_amount_list_layout_4_5_6'] ) :
                                                ?>
                                                <div class="payment-amounts">
                                                    <?php $this->render_amount_element($settings, ['version' => 'v2']); ?>
                                                </div>
                                            <?php 
                                            endif;
                                            ?>    
                                        </div>
                                    <?php 
                                    endif;
                                    ?>
                                    <div class="better-payment-field-advanced-layout field-<?php echo esc_attr($render_attribute_name); ?> elementor-repeater-item-<?php echo esc_attr($item['_id']); ?> <?php echo esc_attr( $payment_amount_field_class ); ?> <?php echo esc_attr( $field_display_inline_class ); ?>">
                                        <div class="form-group mb-4">
                                            <?php if ( ! $is_payment_amount_field ) : ?>
                                                <label class="label">
                                                    <?php esc_html_e( $render_attribute_label, 'better-payment-pro' ); ?>
                                                    <?php if ($render_attribute_required) : ?>
                                                        <span class="required">*</span>
                                                    <?php endif; ?>
                                                </label>
                                            <?php endif; ?>

                                            <input
                                                type="<?php echo esc_attr( $render_attribute_type ); ?>"
                                                class="form-control <?php echo esc_attr( $render_attribute_class ); ?>"
                                                placeholder="<?php echo esc_attr( $render_attribute_placeholder_pro ); ?>"
                                                name="<?php echo esc_attr( $render_attribute_name ); ?>"

                                                <?php if ($render_attribute_required) : ?>
                                                    required="<?php echo esc_attr($render_attribute_required); ?>"
                                                <?php endif; ?>

                                                <?php if ($is_payment_amount_field) : ?>
                                                    step="any"
                                                    min="<?php echo esc_attr( $render_attribute_min ); ?>"
                                                    max="<?php echo esc_attr( $render_attribute_max ); ?>"
                                                    value="<?php echo esc_attr( $render_attribute_default ); ?>"
                                                    <?php echo esc_attr( $render_attribute_default_fixed ); ?>
                                                <?php endif; ?>
                                            >
                                        </div>
                                    </div>

                                    <?php if ( $is_payment_amount_field ) : ?>
                                    <div class="payment-amounts-quantity">
                                        <?php if ( ! empty( $settings['better_payment_show_amount_quantity'] ) && 'yes' === $settings['better_payment_show_amount_quantity'] ) : ?>
                                            <label class="label"><?php esc_html_e( $amount_quantity_text, 'better-payment-pro' ); ?></label>

                                            <div class="better-payment-field-advanced-layout field-payment_amount_quantity">
                                                <div class="form-group mb-4">
                                                    <input type="number" class="form-control bp-custom-payment-amount-quantity" placeholder="Quantity" name="payment_amount_quantity" step="any" min="1" max="" value="1">
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div></div> <!-- To fix css issue  -->
                                    <?php endif; ?>
                                    
                                    <?php 
                                endforeach;
                            endif;
                            ?>

                            <?php if ( 0 === $payment_amount_field_exists ) : ?>
                                <div class="mb-4 bp-payment-amount-wrap payment-amount-wrap <?php echo esc_attr($layout_put_amount_field_hide_show); ?> <?php echo esc_attr( $payment_amount_field_class ); ?>">
                                    <label class="label">
                                        <?php esc_html_e( 'Amount to pay', 'better-payment-pro' ); ?> 
                                        <?php if ($render_attribute_required) : ?> 
                                            <span class="required">*</span> 
                                        <?php endif; ?> 
                                    </label>

                                    <?php
                                    if ( ! empty( $settings['better_payment_show_amount_list_layout_4_5_6'] ) && 'yes' === $settings['better_payment_show_amount_list_layout_4_5_6'] ) {
                                        ?>
                                        <div class="payment-amounts">
                                            <?php $this->render_amount_element($settings, ['version' => 'v2']); ?>
                                        </div>
                                        <?php 
                                    }
                                    ?>    
                                </div>
                            <?php endif; ?>

                            <?php
                            if ($is_paypal_enabled) {
                                echo Better_Payment\Lite\Classes\Handler::paypal_button(esc_attr($widgetObj->get_id()), $settings, ['extra_classes' => 'donation-button payment-button', 'button_text_default' => 'Proceed to Donate']);
                            }

                            if ($is_stripe_enabled) {
                                echo Better_Payment\Lite\Classes\Handler::stripe_button(esc_attr($widgetObj->get_id()), $settings, ['extra_classes' => 'donation-button payment-button', 'button_text_default' => 'Proceed to Donate']);
                            }

                            if ($is_paystack_enabled) {
                                echo Better_Payment\Lite\Classes\Handler::paystack_button(esc_attr($widgetObj->get_id()), $settings, ['extra_classes' => 'donation-button payment-button', 'button_text_default' => 'Proceed to Donate']);
                            }
                            ?>
                        </div>
                    </div>

                    <div class="donation-image-wrap">
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>