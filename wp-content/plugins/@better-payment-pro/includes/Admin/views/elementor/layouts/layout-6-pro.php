<div class="better-payment">
    <div class="better-payment-wrapper">
        <div class="better-payment-container">
            <?php

            use Better_Payment\Lite\Classes\Helper;

            $better_payment_helper_obj = new Helper();

            include BETTER_PAYMENT_ADMIN_VIEWS_PATH . "/partials/layout-vars.php";
            include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/partials/layout-vars.php";

            $render_attribute_default_text = $this->render_attribute_default_text( $settings );
            ?>
            
            <form name="better-payment-form-<?php echo esc_attr($widgetObj->get_id()); ?>" data-better-payment="<?php echo esc_attr($layout_setting_meta); ?>" class="better-payment-form" id="better-payment-form-<?php echo esc_attr($widgetObj->get_id()); ?>" action="<?php echo esc_url($layout_action); ?>" method="post">
                <input type="hidden" name="better_payment_page_id" value="<?php echo esc_attr(get_the_ID()); ?>">
                <input type="hidden" name="better_payment_widget_id" value="<?php echo esc_attr($widgetObj->get_id()); ?>">

                <div class="layout-6-woo reversed1">
                    <div class="order-details-wrap">
                        <div class="order-details">
                            <h3 class="title"><?php esc_html_e($layout_form_transaction_details_heading, 'better-payment-pro'); ?></h3>
                            <div class="order-list">
                                <?php if( ! empty( $product_details ) ) : ?>
                                <?php foreach( $product_details as $product_detail) : ?>
                                <div class="order-item">
                                    <?php if ( ! empty( $product_detail['product_image_src'] ) ) : ?>
                                    <div class="thumb">
                                        <img src="<?php echo esc_url( $product_detail['product_image_src_array'][0] ); ?>" alt="product-image">
                                    </div>
                                    <?php endif; ?>
                                    <div class="name">
                                        <p><?php echo wp_kses( sprintf("<a class='has-text-white' href='%s'>%s</a>", esc_url( $product_detail['product_permalink'] ), esc_html( $product_detail['product_name'] ) ), $valid_html_tags ); ?></p>
                                    </div>
                                    <div class="quantity">
                                        <span class="arrow arrow-down">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 448 512">
                                                <path d="M201.4 342.6c12.5 12.5 32.8 12.5 45.3 0l160-160c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L224 274.7 86.6 137.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l160 160z" />
                                            </svg>
                                        </span>
                                        <span class="arrow arrow-up">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 448 512">
                                                <path d="M201.4 137.4c12.5-12.5 32.8-12.5 45.3 0l160 160c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L224 205.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l160-160z" />
                                            </svg>
                                        </span>
                                        <input name="payment_amount_quantity" type="number" data-price="<?php echo '' !== $product_detail['product_price'] ? intval($product_detail['product_price']) : 0 ?>" step="any" min="1" value="1">
                                    </div>
                                    <div class="price">
                                        <p><?php printf( '%s<span class="order-item-price-amount">%0.2f</span>%s', wp_kses( $layout_form_currency_left, $valid_html_tags ), '' !== $product_detail['product_price'] ? intval($product_detail['product_price']) : '', wp_kses( $layout_form_currency_right, $valid_html_tags )  ); ?></p>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                            <div class="order-summary">
                                <div class="summary-item total1">
                                    <p class="total-amount-text"><?php esc_html_e($total_amount_text, 'better-payment-pro'); ?></p>
                                    <p class="total-amount-number"><?php printf( '%s<span class="summary-item-subtotal-amount">%.2f</span>%s', wp_kses( $layout_form_currency_left, $valid_html_tags ), '' !== $product_price_total ? ($product_price_total) : '', wp_kses( $layout_form_currency_right, $valid_html_tags )  ); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="woo-form-wrap">
                        <div class="payment-method-items-wrap">
                            <h3 class="title"><?php esc_html_e($form_title_text, 'better-payment-pro'); ?></h3>
                            <div class="mb-5 payment-method-items">
                                <?php
                                $is_paypal_enabled = !empty($settings['better_payment_form_paypal_enable']) && 'yes' === $settings['better_payment_form_paypal_enable'];
                                $is_stripe_enabled = !empty($settings['better_payment_form_stripe_enable']) && 'yes' === $settings['better_payment_form_stripe_enable'];
                                $is_paystack_enabled = !empty($settings['better_payment_form_paystack_enable']) && 'yes' === $settings['better_payment_form_paystack_enable'];
                                ?>

                                <?php if (true === $is_paypal_enabled) : ?>
                                    <label class="payment-method-item payment-method-paypal">
                                        <input type="radio" name="payment_method" class="layout-payment-method-paypal" checked>
                                        <div class="payment-option">
                                            <span class="icon paypal-icon">
                                                <img src="<?php echo esc_url(BETTER_PAYMENT_ASSETS . '/img/paypal.png'); ?>" alt="paypal-image">
                                            </span>
                                        </div>
                                    </label>
                                <?php endif; ?>

                                <?php if (true === $is_stripe_enabled) : ?>
                                    <label class="payment-method-item payment-method-stripe">
                                        <input type="radio" name="payment_method" class="layout-payment-method-stripe" <?php echo !$is_paypal_enabled ? 'checked' : '' ?>>
                                        <div class="payment-option">
                                            <span class="icon stripe-icon">
                                                <img src="<?php echo esc_url(BETTER_PAYMENT_PRO_ASSETS . '/img/stripe.png'); ?>" alt="stripe-image">
                                            </span>
                                        </div>
                                    </label>
                                <?php endif; ?>

                                <?php if (true === $is_paystack_enabled) : ?>
                                    <label class="payment-method-item payment-method-paystack">
                                        <input type="radio" name="payment_method" class="layout-payment-method-paystack" <?php echo (!$is_paypal_enabled) && (!$is_stripe_enabled) ? 'checked' : '' ?>>
                                        <div class="payment-option">
                                            <span class="icon paystack-icon">
                                                <img src="<?php echo esc_url(BETTER_PAYMENT_PRO_ASSETS . '/img/paystack.png'); ?>" alt="paystack-image">
                                            </span>
                                        </div>
                                    </label>
                                <?php endif; ?>

                            </div>

                            <div>
                                <?php 
                                if ( ! empty( $settings['better_payment_form_fields_layout_4_5_6_woo'] ) ) :
                                    foreach (  $settings['better_payment_form_fields_layout_4_5_6_woo'] as $item ) :
                                        if ( isset( $item["better_payment_field_name_show"] ) && 'yes' !== $item["better_payment_field_name_show"] ) {
                                            $item_primary_field_type = !empty($item["better_payment_primary_field_type"]) ? $item["better_payment_primary_field_type"] : "";
                                            $is_payment_amount_field = 'primary_payment_amount' === $item_primary_field_type ? 1 : 0;
                                            $is_payment_amount_field_hidden = $is_payment_amount_field ? 1 : 0;

                                            continue;
                                        }

                                        include BETTER_PAYMENT_ADMIN_VIEWS_PATH . "/partials/layout-repeater-vars.php";
                                        include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/partials/layout-repeater-vars.php";
                                        ?>

                                        <div class="better-payment-field-advanced-layout field-<?php echo esc_attr($render_attribute_name); ?> elementor-repeater-item-<?php echo esc_attr($item['_id']); ?> <?php echo esc_attr( $payment_amount_field_class ); ?> <?php echo esc_attr( $field_display_inline_class ); ?>">
                                            <div class="form-group mb-4">
                                                <?php if ( ! $is_payment_amount_field ) : ?>
                                                <input 
                                                    type="<?php echo esc_attr( $render_attribute_type ); ?>" 
                                                    class="form-control <?php echo esc_attr( $render_attribute_class ); ?>" 
                                                    name="<?php echo esc_attr( $render_attribute_name ); ?>" 

                                                    <?php if ($render_attribute_required) : ?> 
                                                        required="<?php echo esc_attr($render_attribute_required); ?>" 
                                                    <?php endif; ?> 
                                                    >
                                                
                                                <label class="label">
                                                    <?php esc_html_e( $render_attribute_label, 'better-payment-pro' ); ?> 
                                                    <?php if ($render_attribute_required) : ?> 
                                                        <span class="required">*</span> 
                                                    <?php endif; ?> 
                                                </label>
                                                <?php endif; ?> 

                                            </div>
                                        </div> 
                                        <?php 
                                    endforeach;
                                endif;
                                ?>

                                <?php if ( 0 === $payment_amount_field_exists ) : ?>
                                    <div class="mb-4 bp-payment-amount-wrap payment-amount-wrap is-hidden">
                                        <input 
                                            type="number" 
                                            class="form-control" 
                                            name="primary_payment_amount"
                                            data-price="<?php echo ! empty( $product_price_total ) ? intval($product_price_total) : 0; ?>"
                                            value="<?php echo ! empty( $product_price_total ) ? intval($product_price_total) : 0; ?>" 
                                            >   
                                    </div>
                                <?php endif; ?>

                                <?php
                                if ($is_paypal_enabled) {
                                    echo Better_Payment\Lite\Classes\Handler::paypal_button(esc_attr($widgetObj->get_id()), $settings, ['extra_classes' => 'woo-button payment-button']);
                                }

                                if ($is_stripe_enabled) {
                                    echo Better_Payment\Lite\Classes\Handler::stripe_button(esc_attr($widgetObj->get_id()), $settings, ['extra_classes' => 'woo-button payment-button']);
                                }

                                if ($is_paystack_enabled) {
                                    echo Better_Payment\Lite\Classes\Handler::paystack_button(esc_attr($widgetObj->get_id()), $settings, ['extra_classes' => 'woo-button payment-button']);
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>