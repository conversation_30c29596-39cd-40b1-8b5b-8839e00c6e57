<?php

$subscriptions = $this->get_user_subscriptions();

?>

<!-- Need to replace with action -->
<?php if ( $bp_settings['header_show'] ) : ?>
<div class="better-payment-user-dashboard-header bp--db-header bp-dashboard-header flex items-center justify-center">
    <div class="bp-dashboard-hamburger padding-0 bp-visible-xs">
        <svg fill="none" viewBox="0 0 24 24" width="25" xmlns="http://www.w3.org/2000/svg"><path clip-rule="evenodd" d="m3 8c0-.55228.44772-1 1-1h16c.5523 0 1 .44772 1 1s-.4477 1-1 1h-16c-.55228 0-1-.44772-1-1zm0 4c0-.5523.44772-1 1-1h16c.5523 0 1 .4477 1 1s-.4477 1-1 1h-16c-.55228 0-1-.4477-1-1zm0 4c0-.5523.44772-1 1-1h8c.5523 0 1 .4477 1 1s-.4477 1-1 1h-8c-.55228 0-1-.4477-1-1z" fill="rgb(0,0,0)" fill-rule="evenodd"/></svg>
    </div>
    <h2><?php esc_html_e($bp_settings['subscription_label'], 'better-payment-pro'); ?></h2>
    <button class="primary-btn d-none"><a href="<?php echo the_permalink(); ?>">Refresh Stats</a></button>
</div>
<?php endif; ?>

<div class="bp--body-content">
    <div class="bp--table-main-wrapper">
        <div class="bp--table-wrapper subscription better-payment-user-dashboard-table">
            <div class="better-payment-user-dashboard-table-header bp--table-header flex justify-between gap-3">
                <?php if ( $bp_settings['subscription_table_subscription_id_show'] ) : ?>
                <div class="th details min-w-300 max-w-300">
                    <h5><?php esc_html_e($bp_settings['subscription_table_subscription_id_label'], 'better-payment-pro') ?></h5>
                </div>
                <?php endif; ?>

                <div class="th details d-none">
                    <h5>Customer ID</h5>
                </div>

                <?php if ( $bp_settings['subscription_table_plan_id_show'] ) : ?>
                <div class="th details min-w-300 max-w-300">
                    <h5><?php esc_html_e($bp_settings['subscription_table_plan_id_label'], 'better-payment-pro') ?></h5>
                </div>
                <?php endif; ?>

                <?php if ( $bp_settings['subscription_table_status_show'] ) : ?>
                <div class="th details flex justify-center">
                    <h5><?php esc_html_e($bp_settings['subscription_table_status_label'], 'better-payment-pro') ?></h5>
                </div>
                <?php endif; ?>

                <?php if ( $bp_settings['subscription_table_amount_show'] ) : ?>
                <div class="th details">
                    <h5><?php esc_html_e($bp_settings['subscription_table_amount_label'], 'better-payment-pro') ?></h5>
                </div>
                <?php endif; ?>

                <?php if ( $bp_settings['subscription_table_created_date_show'] ) : ?>
                <div class="th details">
                    <h5><?php esc_html_e($bp_settings['subscription_table_created_date_label'], 'better-payment-pro') ?></h5>
                </div>
                <?php endif; ?>

                <?php if ( $bp_settings['subscription_table_current_period_show'] ) : ?>
                <div class="th details">
                    <h5><?php esc_html_e($bp_settings['subscription_table_current_period_label'], 'better-payment-pro') ?></h5>
                </div>
                <?php endif; ?>

                <?php if ( $bp_settings['subscription_table_action_show'] ) : ?>
                <div class="th details flex justify-center">
                    <h5><?php esc_html_e($bp_settings['subscription_table_action_label'], 'better-payment-pro') ?></h5>
                </div>
                <?php endif; ?>
            </div>

            <?php if (count($subscriptions)) : ?>
                <?php 
                $bp_txn_counter = 1;  
                
                foreach ($subscriptions as $subscription) :
                    $form_fields_info           = maybe_unserialize($subscription->form_fields_info);
                    $transaction_id             = ! empty($subscription->transaction_id) ? $subscription->transaction_id : '';
                    $subscription_id            = ! empty($form_fields_info['subscription_id']) ? $form_fields_info['subscription_id'] : '';
                    $customer_id                = ! empty($form_fields_info['subscription_customer_id']) ? $form_fields_info['subscription_customer_id'] : '';
                    $subscription_plan_id       = ! empty($form_fields_info['subscription_plan_id']) ? $form_fields_info['subscription_plan_id'] : '';
                    $subscription_interval      = ! empty($form_fields_info['subscription_interval']) ? $form_fields_info['subscription_interval'] : '';
                    $current_period_start       = ! empty($form_fields_info['subscription_current_period_start']) ? $form_fields_info['subscription_current_period_start'] : '';
                    $current_period_end         = ! empty($form_fields_info['subscription_current_period_end']) ? $form_fields_info['subscription_current_period_end'] : '';
                    $subscription_status        = ! empty($form_fields_info['subscription_status']) ? $form_fields_info['subscription_status'] : '';
                    $subscription_created_date  = ! empty($form_fields_info['subscription_created_date']) ? $form_fields_info['subscription_created_date'] : '';
                    $is_payment_split_payment   = ! empty($form_fields_info['is_payment_split_payment']) ? intval( $form_fields_info['is_payment_split_payment'] ) : 0;
                    
                    $product_details = $this->get_product_details_by_plan_id( $subscription_plan_id );
                    $subscription_plan_name = ! empty( $product_details ) ? $product_details->name : $subscription_plan_id;
                ?>
                    <div class="better-payment-user-dashboard-table-body bp--table-body flex items-center justify-between gap-3">
                        <?php if ( $bp_settings['subscription_table_subscription_id_show'] ) : ?>
                        <div class="td details flex items-center gap-3 min-w-300 max-w-300">
                            <!-- <img src="mainLogo.png" alt="logo"> -->
                            <div class="bp-flexbox-container">
                                <h5 class="bp-shortend-text">
                                    <span id="bp_copy_clipboard_input_<?php echo esc_attr($bp_txn_counter); ?>"><?php echo esc_html($subscription_id); ?></span> 
                                </h5>
                                <p>
                                    <span id="bp_copy_clipboard_<?php echo esc_attr($bp_txn_counter); ?>" class="bp-icon bp-copy-square bp-copy-clipboard" title="<?php esc_html_e('Copy', 'better-payment-pro'); ?>" data-bp_txn_counter="<?php echo esc_attr($bp_txn_counter); ?>" ></span>
                                </p>
                                <!-- <p>Manage Payments with WordPress</p> -->
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="td details d-none">
                            <p><?php echo esc_html($customer_id); ?></p>
                        </div>

                        <?php if ( $bp_settings['subscription_table_plan_id_show'] ) : ?>
                        <div class="td details flex items-center gap-3 min-w-300 max-w-300">
                            <p><?php echo esc_html($subscription_plan_name); ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if ( $bp_settings['subscription_table_status_show'] ) : ?>
                        <div class="td details flex justify-center bp-user-dashboard-subscription-status">
                            <?php $active_inactive_text = $subscription_status === 'active' || $subscription_status === 'complete' ? $bp_settings['subscription_table_status_active_label'] : $bp_settings['subscription_table_status_inactive_label']; ?>
                            <button class="<?php echo esc_html( $subscription_status ); ?> <?php echo esc_html( ($subscription_status === 'active' || $subscription_status === 'complete') ? 'active' : 'inactive'); ?>"><?php echo esc_html($active_inactive_text); ?></button>
                        </div>
                        <?php endif; ?>

                        <?php if ( $bp_settings['subscription_table_amount_show'] ) : ?>
                        <div class="td details">
                            <p class="flex items-center"><?php echo esc_html($subscription->currency) . ' ' . esc_html(floatval($subscription->amount)); ?><span class="price">/<?php echo esc_html($subscription_interval); ?></span></p>
                        </div>
                        <?php endif; ?>

                        <?php if ( $bp_settings['subscription_table_created_date_show'] ) : ?>
                        <div class="td details">
                            <p><?php echo esc_html(date('d M, Y', $subscription_created_date)); ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if ( $bp_settings['subscription_table_current_period_show'] ) : ?>
                        <div class="td details">
                            <p><?php echo esc_html(date('d M, Y', $current_period_end)); ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if ( $bp_settings['subscription_table_action_show'] ) : ?>
                        <div class="td details flex justify-center">
                            <button class="cancel bp-user-dashboard-subscription-cancel <?php echo esc_html( $subscription_status ); ?> <?php echo (( 'active' === $subscription_status || 'complete' === $subscription_status ) && !$is_payment_split_payment ) ? '' : 'is-hidden'; ?>" data-subscriptionid="<?php echo esc_html( $subscription_id ); ?>"><?php esc_html_e($bp_settings['subscription_table_action_cancel_label'], 'better-payment-pro'); ?></button>
                        </div>
                        <?php endif; ?>

                    </div>
                    <?php $bp_txn_counter++; ?>
                <?php endforeach; ?>
            <?php else : ?>
            <div class="flex justify-center m-5">
                <p class="bp-no_subscription-text"><?php esc_html_e($bp_settings['no_items_label'], 'better-payment-pro'); ?></p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>