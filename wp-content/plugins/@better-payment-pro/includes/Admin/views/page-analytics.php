<?php

use Better_Payment\Lite\Classes\Helper;
use Better_Payment\Pro\Models\AnalyticsModel;

$all_transactions_amount = isset($all_transactions_amount) ? number_format($all_transactions_amount, 2) : 0;
$completed_transactions_amount = isset($completed_transactions_amount) ? number_format($completed_transactions_amount, 2) : 0;
$incomplete_transactions_amount = isset($incomplete_transactions_amount) ? number_format($incomplete_transactions_amount, 2) : 0;
$refund_transactions_amount = isset($refund_transactions_amount) ? number_format($refund_transactions_amount, 2) : 0;
$unpaid_transactions_amount = isset($unpaid_transactions_amount) ? number_format($unpaid_transactions_amount, 2) : 0;

$transaction_amount_currency = isset($transaction_amount_currency) ? $transaction_amount_currency : '$';
$analytics_time_period_today = isset($analytics_time_period_today) ? $analytics_time_period_today : date('Y-m-d H:i:s', strtotime('today'));
$analytics_time_period_week = isset($analytics_time_period_week) ? $analytics_time_period_week : $analytics_time_period_today;
$analytics_time_period_month = isset($analytics_time_period_month) ? $analytics_time_period_month : $analytics_time_period_today;
$analytics_time_period_year = isset($analytics_time_period_year) ? $analytics_time_period_year : $analytics_time_period_today;
$analytics_model_obj = new AnalyticsModel();
$better_payment_helper_obj = new Helper();
?>

<div class="better-payment">
    <div class="better-payment-analytics">
        <form method="post" id="better-payment-analytics-form" action="#">

            <div class="analytics-page-content p-5 m-1">
                <div class="analytics-page-content-header">
                    <div class="columns">
                        <div class="column">
                            <div class="logo pb-4">
                                <a href="javascript:void(0)"><img src="<?php echo esc_url(BETTER_PAYMENT_ASSETS . '/img/logo.svg'); ?>" alt="Better Payment logo"></a>
                            </div>
                        </div>
                        <div class="column is-3">
                            <p class="has-text-right">
                                <a title="<?php esc_attr_e('We are caching all data for 1 hour. To see the live data press this button!') ?>" href="<?php echo esc_url_raw( admin_url('admin.php?page=better-payment-analytics&cache_clear=1') ); ?>" class="button analytics-cache-clear mr-1 fix-common"><?php esc_html_e('Refresh Stats', 'better-payment-pro'); ?></a>
                            </p>
                        </div>
                    </div>

                    <div class="columns analytics-page-content-header-boxes">
                        <div class="column">
                            <div class="box">
                                <div class="header-box header-box-1 px-3 pt-3 pb-0">
                                    <div class="columns">
                                        <div class="column is-1">
                                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect width="16" height="16" rx="5" fill="#735EF8" />
                                            </svg>
                                        </div>

                                        <div class="column is-11">
                                            <p class="is-size-6 analytics-total-transaction"><?php esc_html_e('Total Transactions', 'better-payment-pro'); ?></p>
                                            <p class="is-size-3 analytics-total-transaction-amount"><?php printf('%s %s', esc_html( $transaction_amount_currency ), esc_html( $all_transactions_amount ) ) ?> </p>
                                            <p class="is-size-3 analytics-total-transaction-amount-filtered is-hidden"><?php printf('%s %s', esc_html( $transaction_amount_currency ), esc_html( $all_transactions_amount ) ) ?> </p>
                                            <p class="is-hidden" title="<?php esc_attr_e('It also includes unpaid transactions: ', 'better-payment-pro') .  esc_attr_e($transaction_amount_currency . ' ' . esc_attr_e( $unpaid_transactions_amount ) ); ?>"></p>
                                            <p class="is-hidden" title="<?php esc_attr_e(sprintf('Transaction statuses: %s', $better_payment_helper_obj->arrayToString($analytics_model_obj->allowed_statuses(), ', ', ', NULL')), 'better-payment-pro');  ?>"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="column">
                            <div class="box">
                                <div class="header-box header-box-2 px-3 pt-3 pb-0">
                                    <div class="columns">
                                        <div class="column is-1">
                                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect width="16" height="16" rx="5" fill="#0ECA86" />
                                            </svg>
                                        </div>

                                        <div class="column is-11">
                                            <p class="is-size-6 analytics-completed-transaction"><?php esc_html_e('Completed Transactions', 'better-payment-pro'); ?></p>
                                            <p class="is-size-3 analytics-completed-transaction-amount"><?php printf('%s %s', esc_html( $transaction_amount_currency ), esc_html( $completed_transactions_amount ) ) ?> </p>
                                            <p class="is-size-3 analytics-completed-transaction-amount-filtered is-hidden"><?php printf('%s %s', esc_html( $transaction_amount_currency ), esc_html( $completed_transactions_amount ) ) ?> </p>
                                            <p class="is-hidden" title="<?php esc_attr_e(sprintf('Transaction statuses: %s', $better_payment_helper_obj->arrayToString($analytics_model_obj->allowed_statuses('completed'))), 'better-payment-pro');  ?>"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="column">
                            <div class="box">
                                <div class="header-box header-box-3 px-3 pt-3 pb-0">
                                    <div class="columns">
                                        <div class="column is-1">
                                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect width="16" height="16" rx="5" fill="#FFDA15" />
                                            </svg>
                                        </div>

                                        <div class="column is-11">
                                            <p class="is-size-6 analytics-incomplete-transaction"><?php esc_html_e('Incomplete Transactions', 'better-payment-pro'); ?></p>
                                            <p class="is-size-3 analytics-incomplete-transaction-amount"><?php printf('%s %s', esc_html( $transaction_amount_currency ), esc_html( $incomplete_transactions_amount ) ) ?> </p>
                                            <p class="is-size-3 analytics-incomplete-transaction-amount-filtered is-hidden"><?php printf('%s %s', esc_html( $transaction_amount_currency ), esc_html( $incomplete_transactions_amount ) ) ?> </p>
                                            <p class="is-hidden" title="<?php esc_attr_e(sprintf('Transaction statuses: %s', $better_payment_helper_obj->arrayToString($analytics_model_obj->allowed_statuses('incomplete', 'v2'), ', ', ', NULL')), 'better-payment-pro');  ?>"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="column">
                            <div class="box">
                                <div class="header-box header-box-4 px-3 pt-3 pb-0">
                                    <div class="columns">
                                        <div class="column is-1">
                                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect width="16" height="16" rx="5" fill="#FF0202" />
                                            </svg>
                                        </div>

                                        <div class="column is-11">
                                            <p class="is-size-6 analytics-refund-transaction"><?php esc_html_e('Refund Transactions', 'better-payment-pro'); ?></p>
                                            <p class="is-size-3 analytics-refund-transaction-amount"><?php printf('%s %s', esc_html( $transaction_amount_currency ), esc_html( $refund_transactions_amount ) ) ?> </p>
                                            <p class="is-size-3 analytics-refund-transaction-amount-filtered is-hidden"><?php printf('%s %s', esc_html( $transaction_amount_currency ), esc_html( $refund_transactions_amount ) ) ?> </p>
                                            <p class="is-hidden" title="<?php esc_attr_e(sprintf('Transaction statuses: %s', $better_payment_helper_obj->arrayToString($analytics_model_obj->allowed_statuses('refunded'))), 'better-payment-pro');  ?>"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="clearfix">

                        </div>
                    </div>
                </div>

                <div class="analytics-page-content-body py-6">
                    <div class="columns">
                        <div class="column">
                            <div class="box">
                                <div class="content-box p-4 my-2 mx-3">
                                    <div class="columns">
                                        <div class="column is-12">
                                            <div class="columns analytics-report-chart-filter">
                                                <div class="column is-6 is-8-fullhd-custom">
                                                    <p class="is-size-5 has-text-weight-medium"><?php esc_html_e('Analytic Reports', 'better-payment-pro'); ?></p>
                                                </div>

                                                <div class="column">
                                                    <div class="columns pull-right">
                                                        <div class="column">
                                                            <div class="select is-hidden">
                                                                <select name="analytics_transaction_types1[]" multiple="multiple" class="select2 analytic-reports-select2 analytics_transaction_types">
                                                                    <option disabled><?php esc_html_e('Select an option', 'better-payment-pro'); ?></option>
                                                                    <option value="all" selected><?php esc_html_e('All', 'better-payment-pro') ?></option>
                                                                    <option value="total-transaction"><?php esc_html_e('Total Transaction', 'better-payment-pro'); ?></option>
                                                                    <option value="completed-transaction"><?php esc_html_e('Completed Transaction', 'better-payment-pro'); ?></option>
                                                                    <option value="incomplete-transaction"><?php esc_html_e('Incomplete Transaction', 'better-payment-pro'); ?></option>
                                                                    <option value="refund-transaction"><?php esc_html_e('Refund Transaction', 'better-payment-pro'); ?></option>
                                                                </select>
                                                            </div>

                                                            <div class="analytics-select-custom-button-wrap">
                                                                <button class="button fix-common analytics-select-custom-button" data-target=".analytics_transaction_types-dropdown">
                                                                    <span class="mr-2 analytics_transaction_types-button-text"><?php esc_html_e('All Transactions', 'better-payment-pro'); ?></span>
                                                                    <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                        <path d="M7.99896 0L4.63636 3.41372L1.27377 0L0 1.29314L4.63636 6L9.27273 1.29314L7.99896 0Z" fill="#9095A2" />
                                                                    </svg>
                                                                </button>

                                                                <div class="box analytics-select-custom-button-dropdown is-hidden analytics_transaction_types-dropdown" >
                                                                    <p class="mb-3">
                                                                        <label class="checkbox">
                                                                            <input class="analytics_transaction_types-all" type="checkbox" checked name="analytics_transaction_types[]" value="all">
                                                                            <?php esc_html_e('All Transactions', 'better-payment-pro'); ?>
                                                                        </label>
                                                                    </p>
                                                                    <p class="mb-3">
                                                                        <label class="checkbox">
                                                                            <input class="analytics_transaction_types-total-transaction" type="checkbox" name="analytics_transaction_types[]" value="total-transaction">
                                                                            <?php esc_html_e('Total Transactions', 'better-payment-pro'); ?>
                                                                        </label>
                                                                    </p>
                                                                    <p class="mb-3">
                                                                        <label class="checkbox">
                                                                            <input class="analytics_transaction_types-completed-transaction" type="checkbox" name="analytics_transaction_types[]" value="completed-transaction">
                                                                            <?php esc_html_e('Completed Transactions', 'better-payment-pro'); ?>
                                                                        </label>
                                                                    </p>
                                                                    <p class="mb-3">
                                                                        <label class="checkbox">
                                                                            <input class="analytics_transaction_types-incomplete-transaction" type="checkbox" name="analytics_transaction_types[]" value="incomplete-transaction">
                                                                            <?php esc_html_e('Incomplete Transactions', 'better-payment-pro'); ?>
                                                                        </label>
                                                                    </p>
                                                                    <p class="mb-3">
                                                                        <label class="checkbox">
                                                                            <input class="analytics_transaction_types-refund-transaction" type="checkbox" name="analytics_transaction_types[]" value="refund-transaction">
                                                                            <?php esc_html_e('Refund Transactions', 'better-payment-pro'); ?>
                                                                        </label>
                                                                    </p>

                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="column">
                                                            <div class="select is-hidden">
                                                                <select name="analytics_time_period1[]" multiple="multiple" class="select2 analytic-reports-select2 analytics_time_period">
                                                                    <option disabled><?php esc_html_e('Select an option', 'better-payment-pro'); ?></option>
                                                                    <option value="week" selected><?php esc_html_e('Last Week', 'better-payment-pro'); ?></option>
                                                                    <option value="month"><?php esc_html_e('Last Month', 'better-payment-pro'); ?></option>
                                                                    <option value="year"><?php esc_html_e('Last Year', 'better-payment-pro'); ?></option>
                                                                    <option value="custom"><?php esc_html_e('Custom', 'better-payment-pro'); ?></option>
                                                                </select>

                                                                <div class="analytics-time-period-hidden-inputs">
                                                                    <input type="hidden" name="analytics_time_period_today" class="analytics_time_period_today" value="<?php esc_attr_e($analytics_time_period_today, 'better-payment-pro'); ?>">
                                                                    <input type="hidden" name="analytics_time_period_week" class="analytics_time_period_week" value="<?php esc_attr_e($analytics_time_period_week, 'better-payment-pro'); ?>">
                                                                    <input type="hidden" name="analytics_time_period_month" class="analytics_time_period_month" value="<?php esc_attr_e($analytics_time_period_month, 'better-payment-pro'); ?>">
                                                                    <input type="hidden" name="analytics_time_period_year" class="analytics_time_period_year" value="<?php esc_attr_e($analytics_time_period_year, 'better-payment-pro'); ?>">
                                                                </div>
                                                            </div>

                                                            <div class="analytics-select-custom-button-wrap">
                                                                <button class="button fix-common analytics-select-custom-button" data-target=".analytics_time_period-dropdown">
                                                                    <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                        <path d="M13.3467 0H11.9467V1.4C11.9467 1.68 11.7133 1.86667 11.48 1.86667C11.2467 1.86667 11.0133 1.68 11.0133 1.4V0H3.54667V1.4C3.54667 1.68 3.31333 1.86667 3.08 1.86667C2.84667 1.86667 2.61333 1.68 2.61333 1.4V0H1.21333C0.513333 0 0 0.606667 0 1.4V3.08H14.9333V1.4C14.9333 0.606667 14.0933 0 13.3467 0ZM0 4.06V12.6C0 13.44 0.513333 14 1.26 14H13.3933C14.14 14 14.98 13.3933 14.98 12.6V4.06H0ZM4.15333 11.9H3.03333C2.84667 11.9 2.66 11.76 2.66 11.5267V10.36C2.66 10.1733 2.8 9.98667 3.03333 9.98667H4.2C4.38667 9.98667 4.57333 10.1267 4.57333 10.36V11.5267C4.52667 11.76 4.38667 11.9 4.15333 11.9ZM4.15333 7.7H3.03333C2.84667 7.7 2.66 7.56 2.66 7.32667V6.16C2.66 5.97333 2.8 5.78667 3.03333 5.78667H4.2C4.38667 5.78667 4.57333 5.92667 4.57333 6.16V7.32667C4.52667 7.56 4.38667 7.7 4.15333 7.7ZM7.88667 11.9H6.72C6.53333 11.9 6.34667 11.76 6.34667 11.5267V10.36C6.34667 10.1733 6.48667 9.98667 6.72 9.98667H7.88667C8.07333 9.98667 8.26 10.1267 8.26 10.36V11.5267C8.26 11.76 8.12 11.9 7.88667 11.9ZM7.88667 7.7H6.72C6.53333 7.7 6.34667 7.56 6.34667 7.32667V6.16C6.34667 5.97333 6.48667 5.78667 6.72 5.78667H7.88667C8.07333 5.78667 8.26 5.92667 8.26 6.16V7.32667C8.26 7.56 8.12 7.7 7.88667 7.7ZM11.62 11.9H10.4533C10.2667 11.9 10.08 11.76 10.08 11.5267V10.36C10.08 10.1733 10.22 9.98667 10.4533 9.98667H11.62C11.8067 9.98667 11.9933 10.1267 11.9933 10.36V11.5267C11.9933 11.76 11.8533 11.9 11.62 11.9ZM11.62 7.7H10.4533C10.2667 7.7 10.08 7.56 10.08 7.32667V6.16C10.08 5.97333 10.22 5.78667 10.4533 5.78667H11.62C11.8067 5.78667 11.9933 5.92667 11.9933 6.16V7.32667C11.9933 7.56 11.8533 7.7 11.62 7.7Z" fill="#A5A9BD" />
                                                                    </svg>

                                                                    <span class="mr-2 ml-2 analytics_time_period-button-text"><?php esc_html_e('Last Week', 'better-payment-pro'); ?></span>
                                                                    <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                        <path d="M7.99896 0L4.63636 3.41372L1.27377 0L0 1.29314L4.63636 6L9.27273 1.29314L7.99896 0Z" fill="#9095A2" />
                                                                    </svg>
                                                                </button>

                                                                <div class="box analytics-select-custom-button-dropdown is-hidden analytics_time_period-dropdown">
                                                                    <p class="mb-3">
                                                                        <label class="checkbox">
                                                                            <input class="analytics_time_period-week" type="checkbox" name="analytics_time_period[]" value="week" checked>
                                                                            <?php esc_html_e('Last Week', 'better-payment-pro'); ?>
                                                                        </label>
                                                                    </p>
                                                                    <p class="mb-3">
                                                                        <label class="checkbox">
                                                                            <input class="analytics_time_period-month" type="checkbox" name="analytics_time_period[]" value="month">
                                                                            <?php esc_html_e('Last Month', 'better-payment-pro'); ?>
                                                                        </label>
                                                                    </p>
                                                                    <p class="mb-3">
                                                                        <label class="checkbox">
                                                                            <input class="analytics_time_period-year" type="checkbox" name="analytics_time_period[]" value="year">
                                                                            <?php esc_html_e('Last Year', 'better-payment-pro'); ?>
                                                                        </label>
                                                                    </p>
                                                                    <p class="mb-3">
                                                                        <label class="checkbox">
                                                                            <input class="analytics_time_period-custom-range analytics_time_period-custom" type="checkbox" name="analytics_time_period[]" value="custom">
                                                                            <?php esc_html_e('Custom', 'better-payment-pro'); ?>
                                                                        </label>
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="column">
                                                            <button class="button is-success mr-1 bp-analytics-filter-submit fix-common"><?php esc_html_e('Filter', 'better-payment-pro'); ?></button>
                                                        </div>

                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                    <!-- Chart: Starts -->
                                    <div class="columns">
                                        <div class="column">
                                            <div class="analytics-chart-wrap">
                                                <?php
                                                ob_start();
                                                include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/template-analytics-content.php";
                                                $analytics_chart_content = ob_get_contents();
                                                ob_end_clean();

                                                echo $analytics_chart_content;
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Chart: Ends -->

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="bp-modal">
                <div class="modal analytics-custom-time-period">
                    <div class="modal-background"></div>
                    <div class="modal-card">
                        <header class="modal-card-head">
                            <p class="modal-card-title is-size-5"><?php esc_html_e('Custom Date Range', 'better-payment-pro'); ?></p>
                            <button class="delete" aria-label="close"></button>
                        </header>
                        <section class="modal-card-body">
                            <div class="columns">
                                <div class="column">
                                    <div class="columns">
                                        <div class="column">
                                            <p class="is-size-6"><?php esc_html_e('Start Date', 'better-payment-pro'); ?></p>
                                        </div>

                                        <div class="column is-three-quarters">
                                            <input class="form__control email-recipient-field bp-datepicker analytics-time-period-start-date-custom" type="text" name="analytics_time_period_start_date_custom" value="">
                                            <small class="email-recipient-field-note"><?php esc_html_e('Select start date of desired time period to see the analytics.', 'better-payment-pro'); ?></small>
                                        </div>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="columns">
                                        <div class="column">
                                            <p class="is-size-6"><?php esc_html_e('End Date', 'better-payment-pro'); ?></p>
                                        </div>

                                        <div class="column is-three-quarters">
                                            <input class="form__control email-recipient-field bp-datepicker analytics-time-period-end-date-custom" type="text" name="analytics_time_period_end_date_custom" value="">
                                            <small class="email-recipient-field-note"><?php esc_html_e('Select end date of desired time period to see the analytics.', 'better-payment-pro'); ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </section>
                        <footer class="modal-card-foot">
                            <button class="button cancel-button mr-1 fix-common"><?php esc_html_e('Confirm', 'better-payment-pro'); ?></button>
                            <button class="button cancel-button fix-common"><?php esc_html_e('Cancel', 'better-payment-pro'); ?></button>
                        </footer>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>