<?php

$total_amount_text      = 'Total amount';
$form_title_text        = 'Payment details';
$form_sub_title_text    = '';

switch( $better_payment_form_layout ){
    case 'layout-4-pro':
        $total_amount_text  = ! empty( $settings["layout_sidebar_total_amount_text_layout_4_pro"] ) ? sanitize_text_field( $settings["layout_sidebar_total_amount_text_layout_4_pro"] ) : $total_amount_text;
        $form_title_text    = ! empty( $settings["better_payment_form_form_title_text"] ) ? sanitize_text_field( $settings["better_payment_form_form_title_text"] ) : $form_title_text;
        break;

    case 'layout-5-pro':
        $form_title_text        = ! empty( $settings["better_payment_form_form_title_text"] ) ? sanitize_text_field( $settings["better_payment_form_form_title_text"] ) : 'Donate to charity';
        $form_sub_title_text    = ! empty( $settings["better_payment_form_form_sub_title_text"] ) ? sanitize_text_field( $settings["better_payment_form_form_sub_title_text"] ) : 'Choose your payment method';
        break;

    case 'layout-6-pro':
        $total_amount_text  = ! empty( $settings["layout_sidebar_total_amount_text_layout_6_pro"] ) ? sanitize_text_field( $settings["layout_sidebar_total_amount_text_layout_6_pro"] ) : $total_amount_text;
        $form_title_text    = ! empty( $settings["better_payment_form_form_title_text"] ) ? sanitize_text_field( $settings["better_payment_form_form_title_text"] ) : 'Select payment option';
        break;

    default:
        break;
}

$split_payment_text     = ! empty( $settings["better_payment_form_transaction_details_split_payment_text"] ) ? sanitize_text_field( $settings["better_payment_form_transaction_details_split_payment_text"] ) : '';
$installment_text       = ! empty( $settings["better_payment_form_transaction_details_installment_text"] ) ? sanitize_text_field( $settings["better_payment_form_transaction_details_installment_text"] ) : '';

$recurring_interval = '';
$recurring_price_id = '';

$recurring_unit_amount = 0;
$is_payment_recurring = isset( $is_payment_recurring ) ? $is_payment_recurring : 0;
$is_payment_split_payment = isset( $is_payment_split_payment ) ? $is_payment_split_payment : 0;

if ( $is_payment_recurring || $is_payment_split_payment ) {
    #TODO caching
    $recurring_price_id = ! empty( $settings['better_payment_form_recurring_price_id'] ) ? sanitize_text_field( $settings['better_payment_form_recurring_price_id'] ) : '';
    
    $stripe = new \Stripe\StripeClient( $stripe_secret_key );

    try {
        $recurring_price_details = $stripe->prices->retrieve( $recurring_price_id);
        $recurring_interval = ! empty( $recurring_price_details->recurring->interval ) ? sanitize_text_field( $recurring_price_details->recurring->interval ) : $recurring_interval;
        $recurring_interval = $is_payment_split_payment ? '' : $recurring_interval; // shows total amount
        $recurring_unit_amount = ! empty( $recurring_price_details->unit_amount ) ? floatval( ( $recurring_price_details->unit_amount ) / 100 ) : $recurring_unit_amount;
        
        $product_price = ! empty( $recurring_interval ) ? "$recurring_unit_amount/$recurring_interval" : "$recurring_unit_amount";
        
        $interval_text = $better_payment_helper_obj->get_interval_text( $recurring_interval );

        $payment_type_text = ! empty($interval_text) ? esc_html( "$interval_text {$layout_form_currency_left}$recurring_unit_amount{$layout_form_currency_right}" ) : esc_html( "{$layout_form_currency_left}$recurring_unit_amount{$layout_form_currency_right}" );
    } catch ( Exception $e ) {
        echo $e->getMessage();
    }

    $recurring_payments = [];

    if ( $is_payment_recurring ) {
        $recurring_payments_data = ! empty( $settings['better_payment_recurring_price_ids'] ) ? $settings['better_payment_recurring_price_ids'] : [];
        
        if ( is_array( $recurring_payments_data ) && count( $recurring_payments_data ) > 0){
            foreach( $recurring_payments_data as $recurring_payment_data ) {
                $recurring_price_id_for_btn = ! empty( $recurring_payment_data[ 'better_payment_recurring_price_id' ] ) ? sanitize_text_field( $recurring_payment_data[ 'better_payment_recurring_price_id' ] ) : '';
            
                if ( ! empty( $recurring_price_id_for_btn ) ) {
                    $recurring_price_details = $stripe->prices->retrieve( $recurring_price_id_for_btn);
                    $recurring_installment_interval = ! empty( $recurring_price_details->recurring->interval ) ? sanitize_text_field( $recurring_price_details->recurring->interval ) : '';
                    $recurring_payment_amount_for_btn = ! empty( $recurring_price_details->unit_amount ) ? floatval( ( $recurring_price_details->unit_amount ) / 100 ) : 0;

                    $recurring_interval_text = $better_payment_helper_obj->get_interval_text( $recurring_installment_interval );

                    $recurring_price_name = ! empty( $recurring_payment_data[ 'better_payment_recurring_name' ] ) ? sanitize_text_field( $recurring_payment_data[ 'better_payment_recurring_name' ] ) : $recurring_interval_text;
    
                    $recurring_payments[] = [
                        'recurring_price_name' => $recurring_price_name,
                        'recurring_price_id' => $recurring_price_id_for_btn,
                        'recurring_installment_interval' => $recurring_installment_interval,
                        'recurring_unit_amount' => $recurring_payment_amount_for_btn,
                    ];
                }
            }
        }
    }
    
    $split_payment_installments = [];

    if ( $is_payment_split_payment ) {
        $split_payment_installments_data = ! empty( $settings['better_payment_split_installment_price_ids'] ) ? $settings['better_payment_split_installment_price_ids'] : [];

        if ( is_array( $split_payment_installments_data ) && count( $split_payment_installments_data ) ){
            foreach( $split_payment_installments_data as $split_payment_installment_data ){
                $installment_price_id = ! empty( $split_payment_installment_data[ 'better_payment_split_installment_price_id' ] ) ? sanitize_text_field( $split_payment_installment_data[ 'better_payment_split_installment_price_id' ] ) : '';
            
                if ( ! empty( $installment_price_id ) ) {
                    $installment_price_details = $stripe->prices->retrieve( $installment_price_id);
                    $installment_interval = ! empty( $installment_price_details->recurring->interval ) ? sanitize_text_field( $installment_price_details->recurring->interval ) : '';
                    $installment_unit_amount = ! empty( $installment_price_details->unit_amount ) ? floatval( ( $installment_price_details->unit_amount ) / 100 ) : 0;

                    $installment_interval_text = $better_payment_helper_obj->get_interval_text( $installment_interval );

                    $installment_price_name = ! empty( $split_payment_installment_data[ 'better_payment_split_installment_name' ] ) ? sanitize_text_field( $split_payment_installment_data[ 'better_payment_split_installment_name' ] ) : $installment_interval_text;
    
                    $split_payment_installments[] = [
                        'installment_price_name' => $installment_price_name,
                        'installment_price_id' => $installment_price_id,
                        'installment_interval' => $installment_interval,
                        'installment_unit_amount' => $installment_unit_amount,
                    ];
                }
            }
        }
    }
    
}
