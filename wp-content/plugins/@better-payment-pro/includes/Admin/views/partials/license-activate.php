<div class="main__content__area">
    <div class="content__area__body p50">
        <div class="go__premium">
            <h3 class="better-payment-license-key-step-img-wrap"><?php esc_html_e('Just one more step to go!', 'better-payment-pro'); ?><img class="better-payment-license-key-step-img" src="<?php echo esc_url(BETTER_PAYMENT_ASSETS . '/img/activate-license.svg') ?>" alt="Activate license"></h3>
            <p class=""><?php _e('Enter your license key here, to activate Better Payment, and get automatic updates and premium support. Visit the <a href="//wpdeveloper.com/docs/how-to-activate-better-payment-license-key/" class="color__themeColor" target="_blank">Validation Guide</a> for help.', 'better-payment-pro'); ?></p>
            <div class="better-payment-license-key-steps-list">
                <p><?php _e('1. Log in to <b><a class="has-text-black">your account</a></b> to get your license key.', 'better-payment-pro'); ?></p>
                <p><?php _e('2. If you don\'t yet have a license key, get <b><a class="has-text-black" href="//wpdeveloper.com/in/upgrade-better-payment-pro" target="_blank">Better Payment</a></b> now.', 'better-payment-pro'); ?></p>
                <p><?php esc_html_e('3. Copy the license key from your account and paste it below.', 'better-payment-pro'); ?></p>
                <p><?php esc_html_e('4. Click on "Activate License" button.', 'better-payment-pro'); ?></p>
            </div>

            <div class="better-payment-license-key-input-wrap">
                <div class="has-background-light better-payment-license-key-section">
                    <div class="field bp-license-form-block">
                        <div class="control">
                            <input class="input is-large1 better-payment-license-key-input-activate" id="<?php echo esc_attr( $this->args['item_slug'] ); ?>-license-key" name="<?php echo esc_attr($this->args['item_slug']); ?>-license-key" type="text" placeholder="<?php esc_html_e('Place Your License Key and Activate', 'better-payment-pro'); ?>" />

                            <?php wp_nonce_field( $this->args['item_slug'] . '_license_nonce', $this->args['item_slug'] . '_license_nonce'); ?>
                            <input type="hidden" name="<?php echo esc_attr($this->args['item_slug']); ?>_license_activate" />
                            <button type="submit" class="button button__active better-payment-license-key-button-activate better-payment-license-activation-btn" name="license_activate">Activate License</button>
                        </div>
                        <div class="bp-clearfix">

                        </div>
                    </div>
                </div>
            </div>

            <div class="pt-3 bp-block bp-activate__license__block --activation-form" style="display: <?php echo ( $status === false || $status !== 'valid' ) ? 'block' : ''; ?>">
                <div class="bp-verification-msg" style="display: none;">
                    <p>License Verification code has been sent to this <span class="bp-customer-email"></span>. Please check your email for the code &amp; insert it below 👇</p>
                    <div class="short-description">
                        <b style="font-weight: 700;">Note: </b> Check out this <a href="https://betterlinks.io/docs/verify-betterlinks-pro-license-key/" target="_blank">guide</a> to
                        verify your license key. If you need any assistance with retrieving your License Verification Key, please <a href="https://wpdeveloper.com/support/"
                                                                                                                                        target="_blank">contact support</a>.
                    </div>
                    <div class="bp-verification-input-container better-payment-license-key-section">
                        <div class="bp-license-form-block">
                            <input type="text" id="<?php echo $this->args['item_slug']; ?>-license-otp" class="bp-form__control" placeholder="Enter Your Verification Code">
                            <button type="submit" class="button button__active">Verify</button>
                        </div>
                        <p>Haven’t received an email? Please hit this <a href="#" class="bp-otp-resend">"Resend"</a> to retry. Please note that this verification code will
                            expire after 15 minutes.</p>
                    </div>
                </div>
                <p class="bp-license-error-msg error-message" style="display: none;"></p>
            </div>
        </div>
    </div>
</div>