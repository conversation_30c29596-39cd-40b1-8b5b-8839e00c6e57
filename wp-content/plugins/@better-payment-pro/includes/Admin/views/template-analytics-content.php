<?PHP 

$analytics_transaction_types = isset($analytics_transaction_types) ? $analytics_transaction_types : array();
$analytics_time_period = isset($analytics_time_period) ? $analytics_time_period : array();
$analytics_time_period_today = isset($analytics_time_period_today) ? $analytics_time_period_today : '';
$analytics_time_period_week = isset($analytics_time_period_week) ? $analytics_time_period_week : '';
$analytics_time_period_month = isset($analytics_time_period_month) ? $analytics_time_period_month : '';
$analytics_time_period_year = isset($analytics_time_period_year) ? $analytics_time_period_year : '';
$analytics_time_period_start_date_custom = isset($analytics_time_period_start_date_custom) ? $analytics_time_period_start_date_custom : '';
$analytics_time_period_end_date_custom = isset($analytics_time_period_end_date_custom) ? $analytics_time_period_end_date_custom : '';

$all_transactions_amount = isset($all_transactions_amount) ? $all_transactions_amount : 0;
$completed_transactions_amount = isset($completed_transactions_amount) ? $completed_transactions_amount : 0;
$incomplete_transactions_amount = isset($incomplete_transactions_amount) ? $incomplete_transactions_amount : 0;
$refund_transactions_amount = isset($refund_transactions_amount) ? $refund_transactions_amount : 0;

$show_all_four_stats = isset($show_all_four_stats) ? $show_all_four_stats : 0;
$all_transactions_amount_show = isset($all_transactions_amount_show) ? $all_transactions_amount_show : 0;
$completed_transactions_amount_show = isset($completed_transactions_amount_show) ? $completed_transactions_amount_show : 0;
$incomplete_transactions_amount_show = isset($incomplete_transactions_amount_show) ? $incomplete_transactions_amount_show : 0;
$refund_transactions_amount_show = isset($refund_transactions_amount_show) ? $refund_transactions_amount_show : 0;

// if( !empty($payment_date_from) && !empty($payment_date_to) ) {
//     echo "Showing results for the period: " . $payment_date_from . " - " . $payment_date_to . "<br>";
// }
// if($show_all_four_stats){
//     echo "Total Transactions:" . $all_transactions_amount . "<br>";
//     echo "Completed Transactions:" . $completed_transactions_amount . "<br>";
//     echo "Incomplete Transactions:" . $incomplete_transactions_amount . "<br>";
//     echo "Refund Transactions:" . $refund_transactions_amount . "<br>";
// } else {
//     echo $all_transactions_amount_show ? "Total Transactions:" . $all_transactions_amount . "<br>" : '';
//     echo $completed_transactions_amount_show ? "Completed Transactions:" . $completed_transactions_amount . "<br>" : '';
//     echo $incomplete_transactions_amount_show ? "Incomplete Transactions:" . $incomplete_transactions_amount . "<br>" : '';
//     echo $refund_transactions_amount_show ? "Refund Transactions:" . $refund_transactions_amount . "<br>" : '';
// }
?> 

<div class="template-analytics-content">
    <canvas id="myChart" width="400" height="400"></canvas>
</div>