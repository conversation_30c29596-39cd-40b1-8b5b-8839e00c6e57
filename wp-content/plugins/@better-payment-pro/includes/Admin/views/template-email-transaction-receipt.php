<?php
$better_payment_transaction_order_id = isset($better_payment_transaction_obj->order_id) ? $better_payment_transaction_obj->order_id : '';
$better_payment_transaction_amount = isset($better_payment_transaction_obj->amount) ? $better_payment_transaction_obj->amount : '';
$better_payment_transaction_currency = isset($better_payment_transaction_obj->currency) ? $better_payment_transaction_obj->currency : '';
$better_payment_transaction_status = isset($better_payment_transaction_obj->status) ? $better_payment_transaction_obj->status : 'N/A';
$better_payment_transaction_date = isset($better_payment_transaction_obj->status) ? $better_payment_transaction_obj->status : '';
$better_payment_transaction_payment_date = isset($better_payment_transaction_obj->payment_date) ? wp_date(get_option('date_format') . ' ' . get_option('time_format'), strtotime($better_payment_transaction_obj->payment_date)) : '';
$better_payment_transaction_source = isset($better_payment_transaction_obj->source) ? $better_payment_transaction_obj->source : '';
$better_payment_transaction_transaction_id = isset($better_payment_transaction_obj->transaction_id) ? $better_payment_transaction_obj->transaction_id : '';

$better_payment_transaction_status_btn_color = $better_payment_transaction_status == 'paid' ? 'label__primary' : 'label__warning';
$better_payment_transaction_status_btn_color = $better_payment_transaction_status == 'unpaid' ? 'label__danger' : $better_payment_transaction_status_btn_color;

$bp_form_fields_info = isset($better_payment_transaction_obj->form_fields_info) ? maybe_unserialize($better_payment_transaction_obj->form_fields_info) : array();
$better_payment_transaction_first_name = (isset($bp_form_fields_info['first_name']) && $bp_form_fields_info['first_name']) ? $bp_form_fields_info['first_name'] : __('', 'better-payment-pro');
$better_payment_transaction_last_name = (isset($bp_form_fields_info['last_name']) && $bp_form_fields_info['last_name']) ? $bp_form_fields_info['last_name'] : __('', 'better-payment-pro');
// $better_payment_transaction_email = isset($better_payment_transaction_obj->email) ? $better_payment_transaction_obj->email : '';
$better_payment_transaction_email = (isset($bp_form_fields_info['primary_email']) && $bp_form_fields_info['primary_email']) ? $bp_form_fields_info['primary_email'] : esc_html__('', 'better-payment-pro');
if(empty($better_payment_transaction_email)){
    $better_payment_transaction_email = (isset($bp_form_fields_info['email']) && $bp_form_fields_info['email']) ? $bp_form_fields_info['email'] : esc_html__('', 'better-payment-pro');
}
global $better_payment_pro_config;
$better_payment_lite_settings = isset($better_payment_pro_config->better_payment_lite_settings) ? $better_payment_pro_config->better_payment_lite_settings : array();

$better_payment_transaction_to_name = !empty($better_payment_lite_settings['better_payment_settings_general_email_from_name']) ? $better_payment_lite_settings['better_payment_settings_general_email_from_name'] : get_bloginfo('name');
$better_payment_transaction_to_email = !empty($better_payment_lite_settings['better_payment_settings_general_email_to']) ? $better_payment_lite_settings['better_payment_settings_general_email_to'] : get_option('admin_email');
?>



<div class="bp-receipt-modals">
    <div class="bp-view-receipt bp-modal">
        <div class="" 
        style="
        display: flex;
        bottom: 0;
        left: 0;
        /* position: absolute; */
        right: 0;
        top: 0;

        align-items: center;
        flex-direction: column;
        justify-content: center;
        overflow: hidden;
        /* position: fixed; */
        z-index: 40;
        "
        >
            
            <div class="" style=" 
            margin: 0 auto;
        border: 1px solid #f1f1f1;
            ">
                <div class="bp-modal-sections">
                    <div class="bp-modal-header" style="
                    background: #352FCB;
        padding: 3rem 3rem 0;
                    ">
                        <div class="" style="
                        padding: 2rem;
        background: #fff;
        border-top-left-radius: 3px;
        border-top-right-radius: 3px;
        border: 1px solid #f1f1f1;
        ">
                            <div class="" 
                            
                            style="margin-bottom: calc(1.5rem - 0.75rem);
                             margin-left: -0.75rem;
        margin-right: -0.75rem;
        margin-top: -0.75rem;
        display: flex;
                            "
                            >
                                <div class="" style="
                                display: block;
        flex-basis: 0;
        flex-grow: 1;
        flex-shrink: 1;
        padding: 0.75rem;
                                ">
                                    <h4 
                                    style="color: #352FCB;
        font-size: 22px;
        margin-bottom: 0.5rem; "
                                    ><?php esc_html_e('Order', 'better-payment-pro') ?> <span class="bp-transaction-order-id" style="
                                    color: #A09CFF;
                                    "><?php echo esc_html("#" . $better_payment_transaction_order_id); ?></span></h4>
                                    <p class="" style="color: #2A3256;
        font-size: 14px; 
        color: #575495;
        font-size: 16px;
        
        "><?php echo esc_html($better_payment_transaction_payment_date); ?></p>
                                </div>

                                <div class="">
                                    <div class="" style="
                                    padding: 0;
                                    margin: 0 0 10px;
                                    border-bottom: none;

                                    display: block;
        flex-basis: 0;
        flex-grow: 1;
        flex-shrink: 1;
                                    ">
                                        <?php
                                        $img_name = $better_payment_transaction_source == 'paypal' ? 'paypal.png' : 'stripe.svg';
                                        $img_source = BETTER_PAYMENT_ASSETS . '/' . 'img/' . $img_name;
                                        ?>
                                        <div class="" style=" padding: 0;
        margin: 0;">
                                            <label class="paid_via_selected" style="    margin-right: 10px;
                                            font-size: 14px;
    font-weight: 400;
    color: #2a3256;
                                            ">
                                                <?php esc_html_e('Payment Method:', 'better-payment-pro'); ?>
                                                <span style=" border-color: #6e58f7 !important;
                                                background: #f7f8fa;
    border-radius: 5px;
    border: 1px solid transparent;
    padding: 9px 15px;
    display: inline-block;
    font-size: 0;
    cursor: pointer;
    -webkit-transition: all .3s ease 0s;
    transition: all .3s ease 0s;
    -webkit-transition-property: border;
    transition-property: border;
                                                "><img src="<?php echo esc_url($img_source); ?>" alt="Source"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <p style="color: #2A3256;
        font-size: 14px; 
        margin-bottom: 10px;
        "><?php esc_html_e('Transaction ID:', 'better-payment-pro'); ?> <span class="bp-text-black"><?php echo esc_html($better_payment_transaction_transaction_id); ?></span></p>
                                    <p style="color: #2A3256;
        font-size: 14px; 
        margin-bottom: 10px;"><?php esc_html_e('Payment Status:', 'better-payment-pro'); ?> <span class="text__label <?php echo esc_attr($better_payment_transaction_status_btn_color); ?>" 
        
        style=""
        ><?php echo esc_html(ucwords($better_payment_transaction_status)); ?></span></p>
                                    <p style=" color: #2A3256;
        font-size: 14px;
        margin-bottom: 10px;"><?php esc_html_e('Amount:', 'better-payment-pro'); ?> <span><?php echo esc_html($better_payment_transaction_amount) . ' ' . esc_html($better_payment_transaction_currency); ?></span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="" style="padding: 5rem;
        background: #fff; ">
                        <div class="" style="
                        background: #fff;
                        ">
                            <div class="" style="margin-bottom: calc(1.5rem - 0.75rem); 
                             margin-left: -0.75rem;
        margin-right: -0.75rem;
        margin-top: -0.75rem;
        display: flex;
                            ">
                                <div class="" style="
                                display: block;
        flex-basis: 0;
        flex-grow: 1;
        flex-shrink: 1;
        padding: 0.75rem;
                                ">
                                    <div class="column-content " style="
                                     background: #F7F9FB;
        padding: 2rem 3rem;
        color: #656B86;
                                    ">
                                        <p class="" style="
                                        font-size: 12px !important;

                                        margin-bottom: 0.5rem !important;
                                        "><?php echo esc_html_e('From', 'better-payment-pro'); ?></p>
                                        <p class="" 
                                        style=" 
                                        font-size: 18px !important; 
                                        color: #000 !important;

                                        margin-bottom: 0.5rem !important;
                                        "
                                        ><?php echo esc_html($better_payment_transaction_first_name) . ' ' . esc_html($better_payment_transaction_last_name); ?></p>
                                        <p class="" style=" font-size: 12px !important; 
                                        margin-bottom: 0.5rem !important;
                                        "><?php echo esc_html($better_payment_transaction_email) ?></p>
                                    </div>
                                </div>

                                <div class="" style="
                                display: block;
        flex-basis: 0;
        flex-grow: 1;
        flex-shrink: 1;
        padding: 0.75rem;
                                ">
                                    <div class="column-content " style=" 
                                     background: #F7F9FB;
        padding: 2rem 3rem;
        color: #656B86;
                                    ">
                                        <p class="" style=" font-size: 12px !important; 
                                        margin-bottom: 0.5rem !important;
                                        "><?php esc_html_e('To', 'better-payment-pro'); ?></p>
                                        <p class="" style=" font-size: 18px !important;
                                        color: #000 !important;

                                        margin-bottom: 0.5rem !important;
                                        "><?php esc_html_e($better_payment_transaction_to_name, 'better-payment-pro') ?></p>
                                        <p class="" style=" font-size: 12px !important; 
                                        margin-bottom: 0.5rem !important;
                                        "><?php esc_html_e($better_payment_transaction_to_email, 'better-payment-pro') ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bp-modal-footer" style="
                    background: #352FCB;
        padding: 0.5rem;
        text-align: center;
                    ">
                        <div class="bp-modal-footer-inner">
                            <p class="has-text-white is-size-4" style=" font-size: 12px !important; 
                            color: #fff !important;
                            ">
                                <span class="has-text-light" style=" 
                                color: #f5f5f5 !important;
                                "><?php esc_html_e('Powered By ', 'better-payment-pro') ?></span>
                                <img src="<?php echo esc_url(BETTER_PAYMENT_ASSETS . '/' . 'img/better-payment-icon-white-small.png'); ?>" alt="Better Payment icon">
                                <?php esc_html_e('Better Payment', 'better-payment-pro') ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
           
        </div>
    </div>

    
</div>