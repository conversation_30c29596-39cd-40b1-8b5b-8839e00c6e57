<?php
$better_payment_transaction_order_id = isset($better_payment_transaction_obj->order_id) ? $better_payment_transaction_obj->order_id : '';
$better_payment_transaction_amount = isset($better_payment_transaction_obj->amount) ? $better_payment_transaction_obj->amount : '';
$better_payment_transaction_currency = isset($better_payment_transaction_obj->currency) ? $better_payment_transaction_obj->currency : '';
$better_payment_transaction_status = isset($better_payment_transaction_obj->status) ? $better_payment_transaction_obj->status : 'N/A';
$better_payment_transaction_date = isset($better_payment_transaction_obj->status) ? $better_payment_transaction_obj->status : '';
$better_payment_transaction_payment_date = isset($better_payment_transaction_obj->payment_date) ? wp_date(get_option('date_format') . ' ' . get_option('time_format'), strtotime($better_payment_transaction_obj->payment_date)) : '';
$better_payment_transaction_source = isset($better_payment_transaction_obj->source) ? $better_payment_transaction_obj->source : '';
$better_payment_transaction_transaction_id = isset($better_payment_transaction_obj->transaction_id) ? $better_payment_transaction_obj->transaction_id : '';

$better_payment_transaction_status_btn_color = $better_payment_transaction_status == 'paid' ? 'label__primary' : 'label__warning';
$better_payment_transaction_status_btn_color = $better_payment_transaction_status == 'unpaid' ? 'label__danger' : $better_payment_transaction_status_btn_color;

$bp_form_fields_info = isset($better_payment_transaction_obj->form_fields_info) ? maybe_unserialize($better_payment_transaction_obj->form_fields_info) : array();
$better_payment_transaction_first_name = (isset($bp_form_fields_info['first_name']) && $bp_form_fields_info['first_name']) ? $bp_form_fields_info['first_name'] : __('', 'better-payment-pro');
$better_payment_transaction_last_name = (isset($bp_form_fields_info['last_name']) && $bp_form_fields_info['last_name']) ? $bp_form_fields_info['last_name'] : __('', 'better-payment-pro');
// $better_payment_transaction_email = isset($better_payment_transaction_obj->email) ? $better_payment_transaction_obj->email : '';
$better_payment_transaction_email = (isset($bp_form_fields_info['primary_email']) && $bp_form_fields_info['primary_email']) ? $bp_form_fields_info['primary_email'] : esc_html__('', 'better-payment-pro');
if(empty($better_payment_transaction_email)){
    $better_payment_transaction_email = (isset($bp_form_fields_info['email']) && $bp_form_fields_info['email']) ? $bp_form_fields_info['email'] : esc_html__('', 'better-payment-pro');
}

global $better_payment_pro_config;
$better_payment_lite_settings = isset($better_payment_pro_config->better_payment_lite_settings) ? $better_payment_pro_config->better_payment_lite_settings : array();

$better_payment_transaction_to_name = !empty($better_payment_lite_settings['better_payment_settings_general_email_from_name']) ? $better_payment_lite_settings['better_payment_settings_general_email_from_name'] : get_bloginfo('name');
$better_payment_transaction_to_email = !empty($better_payment_lite_settings['better_payment_settings_general_email_to']) ? $better_payment_lite_settings['better_payment_settings_general_email_to'] : get_option('admin_email');
?>

<div class="payment__getway bp-receipt-buttons-wrap bp-pro-block-wrap">
    <h4 class="title"><i class="bp-icon bp-info"></i> <?php esc_html_e('Payment Receipt', 'better-payment-pro'); ?></h4>
    <div class="content">
        <a href="#" class="button button--sm view-button bp-view-receipt bp-modal-button" data-targetwrap="bp-view-receipt" data-orderid="<?php echo esc_attr($better_payment_transaction_order_id); ?>" data-amount="<?php echo esc_attr($better_payment_transaction_amount); ?>"><?php esc_html_e('View Receipt', 'better-payment-pro'); ?></a>
        <a href="#" class="button button--sm view-button bp-send-receipt bp-modal-button" data-targetwrap="bp-send-receipt" data-orderid="<?php echo esc_attr($better_payment_transaction_order_id); ?>" data-amount="<?php echo esc_attr($better_payment_transaction_amount); ?>"><?php esc_html_e('Send Receipt', 'better-payment-pro'); ?></a>
        <a href="#" class="better-payment-print-receipt-btn" title="<?php esc_html_e('Works well with incognito or private mode!', 'better-payment-pro'); ?>"><img src="<?php echo esc_url(BETTER_PAYMENT_PRO_ASSETS . '/img/print-button.svg'); ?>" alt="Print button" width="20"></a>
    </div>
</div>

<div class="bp-receipt-modals">
    <div class="bp-view-receipt bp-modal">
        <div class="modal">
            <div class="modal-background"></div>
            <div class="modal-content">
                <div class="bp-modal-sections">
                    <div class="bp-modal-header">
                        <div class="bp-modal-header-inner">
                            <div class="columns">
                                <div class="column">
                                    <h4><?php esc_html_e('Order', 'better-payment-pro') ?> <span class="bp-transaction-order-id"><?php echo esc_html("#" . $better_payment_transaction_order_id); ?></span></h4>
                                    <p class="bp-modal-header-subtitle"><?php echo esc_html($better_payment_transaction_payment_date); ?></p>
                                </div>

                                <div class="column">
                                    <div class="payment__getway">
                                        <?php
                                        $img_name = $better_payment_transaction_source == 'paypal' ? 'paypal.png' : 'stripe.svg';
                                        $img_source = BETTER_PAYMENT_ASSETS . '/' . 'img/' . $img_name;
                                        ?>
                                        <div class="pay__by">
                                            <label class="paid_via_selected">
                                                <?php esc_html_e('Payment Method:', 'better-payment-pro'); ?>
                                                <span><img src="<?php echo esc_url($img_source); ?>" alt="Source"></span>
                                            </label>
                                        </div>
                                    </div>
                                    <p><?php esc_html_e('Transaction ID:', 'better-payment-pro'); ?> <span class="bp-text-black"><?php echo esc_html($better_payment_transaction_transaction_id); ?></span></p>
                                    <p><?php esc_html_e('Payment Status:', 'better-payment-pro'); ?> <span class="text__label <?php echo esc_attr($better_payment_transaction_status_btn_color); ?>"><?php echo esc_html(ucwords($better_payment_transaction_status)); ?></span></p>
                                    <p><?php esc_html_e('Amount:', 'better-payment-pro'); ?> <span><?php echo esc_html($better_payment_transaction_amount) . ' ' . esc_html($better_payment_transaction_currency); ?></span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bp-modal-content">
                        <div class="bp-modal-content-inner">
                            <div class="columns">
                                <div class="column">
                                    <div class="column-content gray-box">
                                        <p class="is-size-6 mb-2"><?php echo esc_html_e('From', 'better-payment-pro'); ?></p>
                                        <p class="is-size-5 has-text-black mb-2"><?php echo esc_html($better_payment_transaction_first_name) . ' ' . esc_html($better_payment_transaction_last_name); ?></p>
                                        <p class="is-size-6 mb-2"><?php echo esc_html($better_payment_transaction_email) ?></p>
                                    </div>
                                </div>

                                <div class="column">
                                    <div class="column-content gray-box">
                                        <p class="is-size-6 mb-2"><?php esc_html_e('To', 'better-payment-pro'); ?></p>
                                        <p class="is-size-5 has-text-black mb-2"><?php esc_html_e($better_payment_transaction_to_name, 'better-payment-pro') ?></p>
                                        <p class="is-size-6 mb-2"><?php esc_html_e($better_payment_transaction_to_email, 'better-payment-pro') ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bp-modal-footer">
                        <div class="bp-modal-footer-inner">
                            <p class="has-text-white is-size-6">
                                <span class="has-text-light"><?php esc_html_e('Powered By ', 'better-payment-pro') ?></span>
                                <img src="<?php echo esc_url(BETTER_PAYMENT_ASSETS . '/' . 'img/better-payment-icon-white-small.png'); ?>" alt="Better Payment icon">
                                <?php esc_html_e('Better Payment', 'better-payment-pro') ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <button class="modal-close is-large" aria-label="close"></button>
        </div>
    </div>

    <div class="bp-send-receipt bp-modal">
        <div class="modal is-small">
            <div class="modal-background"></div>
            <div class="modal-card">
                <header class="modal-card-head">
                    <p class="modal-card-title is-size-5"><?php esc_html_e('Send receipt', 'better-payment-pro'); ?></p>
                    <button class="delete" aria-label="close"></button>
                </header>
                <section class="modal-card-body">
                    <div class="columns">
                        <div class="column">
                            <p class="is-size-6"><?php esc_html_e('Deliver to', 'better-payment-pro'); ?></p>
                        </div>

                        <div class="column is-three-quarters">
                            <input class="form__control email-recipient-field" type="text" value="<?php echo !empty($better_payment_transaction_email) ? esc_attr($better_payment_transaction_email) : esc_attr($better_payment_transaction_to_email); ?>">
                            <small class="email-recipient-field-note"><?php esc_html_e('Above email address will receive a copy of the receipt.', 'better-payment-pro'); ?></small>
                        </div>
                    </div>
                </section>
                <footer class="modal-card-foot">
                    <button class="button is-success mr-1 bp-send-receipt-submit" data-orderid="<?php echo esc_attr($better_payment_transaction_order_id); ?>"><?php esc_html_e('Send', 'better-payment-pro'); ?></button>
                    <button class="button cancel-button"><?php esc_html_e('Cancel', 'better-payment-pro'); ?></button>
                </footer>
            </div>
        </div>
    </div>
</div>

<div class="better-payment-print-receipt-wrap bp-d-none">
    <div class="better-payment-print-receipt">
        <style>
            @media print {
                .better-payment-print-receipt .bp-modal-header {
                    box-shadow: inset 0 0 0 1000px #352FCB;
                }

                .better-payment-print-receipt .bp-modal-footer {
                    box-shadow: inset 0 0 0 1000px #352FCB;
                }
            }
        </style>
        <?php
        ob_start();
        include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/template-email-transaction-receipt.php";
        $better_payment_print_receipt_content = ob_get_contents();
        ob_end_clean();

        echo $better_payment_print_receipt_content;
        ?>
    </div>
</div>