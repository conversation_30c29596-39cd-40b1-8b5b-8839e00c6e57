<?php 
    $better_payment_transaction_order_id = isset($better_payment_transaction_obj->order_id) ? $better_payment_transaction_obj->order_id : '';
    $better_payment_transaction_amount = isset($better_payment_transaction_obj->amount) ? $better_payment_transaction_obj->amount : '';
    $better_payment_transaction_status = isset($better_payment_transaction_obj->status) ? $better_payment_transaction_obj->status : '';

    $hide_if_already_refunded = $better_payment_transaction_status == 'refunded' ? 'bp-d-none' : '';
?>

<div class="payment__getway bp-border-danger bp-refund-buttons-wrap <?php echo esc_attr($hide_if_already_refunded); ?>">
    <h4 class="title"><i class="bp-icon bp-info"></i> <?php esc_html_e('Refund Payment', 'better-payment-pro'); ?></h4>
    <div class="content">
        <?php if( ! $is_imported ) : ?>
        <a href="#" class="button button--sm view-button manual-refund" data-orderid="<?php echo esc_attr($better_payment_transaction_order_id); ?>" data-amount="<?php echo esc_attr($better_payment_transaction_amount); ?>" data-refundtype="manual-refund"><?php esc_html_e('Refund Manually', 'better-payment-pro'); ?></a>
        <a href="#" class="button button--sm view-button better-payment-refund" data-orderid="<?php echo esc_attr($better_payment_transaction_order_id); ?>" data-amount="<?php echo esc_attr($better_payment_transaction_amount); ?>" data-refundtype="better-payment-refund"><?php esc_html_e('Refund Via Better Payment', 'better-payment-pro'); ?></a>
        <?php else : ?>
        <p><?php esc_html_e('Not available for imported transactions!', 'better-payment-pro'); ?></p>
        <?php endif; ?>
    </div>
</div>