<?php

$form_fields_info           = maybe_unserialize($better_payment_transaction_obj->form_fields_info);
$form_fields_info           = maybe_unserialize($better_payment_transaction_obj->form_fields_info);
$currency                   = $better_payment_transaction_obj->currency;

$subscription_id            = ! empty( $form_fields_info['subscription_id'] ) ? $form_fields_info['subscription_id'] : '';
$customer_id                = ! empty( $form_fields_info['subscription_customer_id'] ) ? $form_fields_info['subscription_customer_id'] : '';
$subscription_plan_id       = ! empty( $form_fields_info['subscription_plan_id'] ) ? $form_fields_info['subscription_plan_id'] : '';
$subscription_interval      = ! empty( $form_fields_info['subscription_interval'] ) ? $form_fields_info['subscription_interval'] : '';
$current_period_start       = ! empty( $form_fields_info['subscription_current_period_start'] ) ? $form_fields_info['subscription_current_period_start'] : '';
$current_period_end         = ! empty( $form_fields_info['subscription_current_period_end'] ) ? $form_fields_info['subscription_current_period_end'] : '';
$subscription_status        = ! empty( $form_fields_info['subscription_status'] ) ? $form_fields_info['subscription_status'] : '';
$subscription_created_date  = ! empty( $form_fields_info['subscription_created_date'] ) ? $form_fields_info['subscription_created_date'] : '';
$subscription_canceled_date  = ! empty( $form_fields_info['subscription_canceled_at'] ) ? $form_fields_info['subscription_canceled_at'] : '';
$split_payment_cancels_at  = ! empty( $form_fields_info['split_payment_cancels_at'] ) ? $form_fields_info['split_payment_cancels_at'] : '';

$is_payment_split_payment =  ! empty( $form_fields_info['is_payment_split_payment'] ) ? intval( $form_fields_info['is_payment_split_payment'] ) : 0;

$split_payment_installment_price_id = $is_payment_split_payment && ! empty( $form_fields_info['split_payment_installment_price_id'] ) ? sanitize_text_field( $form_fields_info['split_payment_installment_price_id'] ) : '';
$split_payment_installments = $is_payment_split_payment && ! empty( $form_fields_info['split_payment_installment_iteration'] ) ? sanitize_text_field( $form_fields_info['split_payment_installment_iteration'] ) : '';
$split_payment_total_amount_price_id = $is_payment_split_payment && ! empty( $form_fields_info['split_payment_total_amount_price_id'] ) ? sanitize_text_field( $form_fields_info['split_payment_total_amount_price_id'] ) : '';
$split_payment_total_amount = $is_payment_split_payment && ! empty( $form_fields_info['split_payment_total_amount'] ) ? sanitize_text_field( $form_fields_info['split_payment_total_amount'] ) : '';
$split_payment_installment_amount = $is_payment_split_payment && ! empty( $form_fields_info['split_payment_installment_amount'] ) ? sanitize_text_field( $form_fields_info['split_payment_installment_amount'] ) : '';

?>
<div class="transaction__info">
    <div class="info__header">
        <h4 class="title"><i class="bp-icon bp-info"></i> <?php esc_html_e('Subscription Information', 'better-payment-pro'); ?></h4>
    </div>
    <ul class="informations">
        <li><span><?php esc_html_e('Status:', 'better-payment-pro'); ?></span> <?php echo esc_html($subscription_status); ?></li>
        <li><span><?php esc_html_e('Started:', 'better-payment-pro'); ?></span> <?php echo esc_html( wp_date(get_option('date_format').' '.get_option('time_format'), $subscription_created_date) ); ?></li>
        <li><span><?php esc_html_e('Customer ID:', 'better-payment-pro'); ?></span> <?php echo esc_html($customer_id); ?></li>
        <li><span><?php esc_html_e('Subscription ID:', 'better-payment-pro'); ?></span> <?php echo esc_html($subscription_id); ?></li>
        
        <?php if ( $is_payment_split_payment ) : ?>
        <li><span><?php esc_html_e('Product Price ID:', 'better-payment-pro'); ?></span> <?php echo esc_html($subscription_plan_id); ?></li>
        <li><span><?php esc_html_e('Total Amount:', 'better-payment-pro'); ?></span> <?php echo esc_html($split_payment_total_amount)  . ' ' . esc_html( $currency ); ?></li>
        <li><span><?php esc_html_e('Installment Price ID:', 'better-payment-pro'); ?></span> <?php echo esc_html($split_payment_total_amount_price_id); ?></li>
        <li><span><?php esc_html_e('Installment Amount:', 'better-payment-pro'); ?></span> <?php echo esc_html($split_payment_installment_amount) . ' ' . esc_html( $currency ); ?></li>
        <li><span><?php esc_html_e('Total Installments:', 'better-payment-pro'); ?></span> <?php echo esc_html($split_payment_installments); ?></li>
        <?php else : ?>
        <li><span><?php esc_html_e('Plan/Price ID:', 'better-payment-pro'); ?></span> <?php echo esc_html($split_payment_total_amount) ?></li>
        <?php endif; ?>
        <li><span><?php esc_html_e('Interval:', 'better-payment-pro'); ?></span> <?php echo esc_html($subscription_interval) ?></li>
        <li><span><?php esc_html_e('Current Period:', 'better-payment-pro'); ?></span> <?php echo esc_html( wp_date(get_option('date_format'), $current_period_start) ); ?> - <?php echo esc_html( wp_date(get_option('date_format'), $current_period_end) ); ?></li>
        <li class="is-hidden"><span><?php esc_html_e('Status:', 'better-payment-pro'); ?></span> <?php echo esc_html($subscription_status) ?></li>
        <?php if ( 'canceled' === $subscription_status ) : ?>
        <li><span><?php esc_html_e('Canceled At:', 'better-payment-pro'); ?></span> <?php echo esc_html( wp_date(get_option('date_format').' '.get_option('time_format'), $subscription_canceled_date) ); ?></li>
        <?php endif; ?>

        <?php if ( $is_payment_split_payment ) : ?>
        <li><span><?php esc_html_e('Cancels At:', 'better-payment-pro'); ?></span> <?php echo esc_html( wp_date(get_option('date_format').' '.get_option('time_format'), $split_payment_cancels_at) ); ?></li>
        <?php endif; ?>
    </ul>
</div>