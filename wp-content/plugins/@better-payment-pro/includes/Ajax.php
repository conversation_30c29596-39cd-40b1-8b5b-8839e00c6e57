<?php

namespace Better_Payment\Pro;

use Better_Payment\Pro\Admin\Analytics;
use Better_Payment\Pro\Admin\Elementor\UserDashboard;
use Better_Payment\Pro\Admin\Transaction;
use stdClass;
use Better_Payment\Pro\Traits\Helper;

/**
 * Ajax handler class
 * 
 * @since 0.0.1
 */
class Ajax extends Controller{

    use Helper;
    /**
     * Class constructor
     * 
     * @since 0.0.1
     */
    public function __construct() {
        add_action( 'wp_ajax_better_payment_pro_transaction_refund', [ $this, 'transaction_refund'] );
        add_action( 'wp_ajax_better_payment_pro_transaction_send_receipt', [ $this, 'transaction_send_receipt'] );
        
        $analyticsObj = new Analytics();
        add_action( 'wp_ajax_better_payment_pro_analytics_filter', [ $analyticsObj, 'analytics_filter_content'] );
        
        $userDashboardObj = new UserDashboard();
        add_action( 'wp_ajax_better_payment_user_dashboard_subscription_cancel', [ $userDashboardObj, 'cencel_subscription'] );
        // add_action( 'wp_ajax_nopriv_better_payment_user_dashboard_subscription_cancel', [ $userDashboardObj, 'cencel_subscription' ] );
    }
    
    /**
     * Handle transaction refund
     *
     * @return void
     * @since 0.0.1
     */
    public function transaction_refund() {
        if ( ! wp_verify_nonce( $_REQUEST['_wpnonce'], 'better-payment-pro-admin-nonce' ) ) {
            wp_send_json_error( [
                'message' => __( 'Nonce verification failed!', 'better-payment-pro' )
            ] );
        }

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( [
                'message' => __( 'No permission!', 'better-payment-pro' )
            ] );
        }

        global $better_payment_pro_config;
        $transactionObj = new Transaction();
        $is_refunded = 0;
        $refund_info = new stdClass();

        $orderId = isset( $_REQUEST['orderId'] ) ? sanitize_text_field( $_REQUEST['orderId'] ) : '';
        $refundAmount = isset( $_REQUEST['refundAmount'] ) ? sanitize_text_field( $_REQUEST['refundAmount'] ) : '';
        $refundType = isset( $_REQUEST['refundType'] ) ? sanitize_text_field( $_REQUEST['refundType'] ) : '';
        
        $transaction = $transactionObj->get_transaction_by_order_id( $orderId );
        
        if($refundType == 'manual-refund') {
            $refund_info->refund_type = sanitize_text_field('manual-refund');
            $refund_info->reason = sanitize_text_field('');
            $is_refunded = $transactionObj->update_transaction_status( $transaction->order_id, 'refunded', $refund_info );
        }
    
        if($refundType == 'better-payment-refund'){
            if (isset($transaction->source) && $transaction->source == 'paypal'){
                //PayPal
                $capture_id = $transaction->transaction_id;
                
                $paypal_api_baseurl = $better_payment_pro_config->paypal_api_url();
                $access_token_url = $paypal_api_baseurl . '/v1/oauth2/token';

                $curl = curl_init();

                curl_setopt_array($curl, array(
                CURLOPT_URL => $access_token_url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => 'grant_type=client_credentials',
                CURLOPT_HTTPHEADER => array(
                    'Accept: application/json',
                    'Accept-Language: en_US',
                    'Authorization: Basic ' . base64_encode( $better_payment_pro_config->paypal_client_id . ':' . $better_payment_pro_config->paypal_client_secret ),
                    'Content-Type: application/x-www-form-urlencoded'
                ),
                ));

                $response = curl_exec($curl);
                curl_close($curl);
                
                $response = json_decode($response);
                $access_token = is_object($response) && isset($response->access_token) ? $response->access_token : '';

                $transaction_id = $transaction->transaction_id;
                $payment_refund_url = $paypal_api_baseurl . '/v2/payments/captures/' . $transaction_id . '/refund';
                
                $curl = curl_init();

                curl_setopt_array($curl, array(
                CURLOPT_URL => $payment_refund_url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Authorization: Bearer ' . $access_token
                ),
                ));

                $response = curl_exec($curl);
                curl_close($curl);
                
                $refund_info_paypal = json_decode($response);
                
                if( is_object($refund_info) ){
                    $refund_info->refund_id = sanitize_text_field($refund_info_paypal->id);
                    $refund_info->refund_type = sanitize_text_field('better-payment-refund');
                    $refund_info->reason = sanitize_text_field( isset($refund_info_paypal->reason) ? $refund_info_paypal->reason : '');
                }
                $is_refunded = $transactionObj->update_transaction_status( $transaction->order_id, 'refunded', $refund_info );
            }

            if (isset($transaction->source) && $transaction->source == 'stripe'){
                //Stripe  
                $payment_intent = $transaction->transaction_id;
                
                $stripe = new \Stripe\StripeClient($better_payment_pro_config->stripe_secret_key);
                $refund_info_stripe = $stripe->refunds->create(['payment_intent' => $payment_intent]);
                if( is_object($refund_info) ){
                    $refund_info->refund_id = sanitize_text_field($refund_info_stripe->id);
                    $refund_info->refund_type = sanitize_text_field('better-payment-refund');
                    $refund_info->reason = sanitize_text_field($refund_info_stripe->reason);
                }
                $is_refunded = $transactionObj->update_transaction_status( $transaction->order_id, 'refunded', $refund_info );
            }
        }

        wp_send_json_success([
            'message' => __( 'Transaction amount refunded successfully!', 'better-payment-pro' ),
            'transaction_before_refund' => $transaction,
            'is_refunded' => $is_refunded,
            'refund_info' => $refund_info,
        ]);
    }

    /**
     * Handle transaction send receipt
     *
     * @return void
     * @since 1.0.0
     */
    public function transaction_send_receipt() {
        if ( ! wp_verify_nonce( $_REQUEST['_wpnonce'], 'better-payment-pro-admin-nonce' ) ) {
            wp_send_json_error( [
                'message' => __( 'Nonce verification failed!', 'better-payment-pro' )
            ] );
        }

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( [
                'message' => __( 'No permission!', 'better-payment-pro' )
            ] );
        }

        global $better_payment_pro_config;
        $transactionObj = new Transaction();
        $is_refunded = 0;
        $refund_info = new stdClass();

        $orderId = isset( $_REQUEST['orderId'] ) ? sanitize_text_field( $_REQUEST['orderId'] ) : '';
        $email = isset( $_REQUEST['email'] ) ? sanitize_email( $_REQUEST['email'] ) : '';
        $transaction = $transactionObj->get_transaction_by_order_id( $orderId );
        
        if ( empty($transaction) ) {
            wp_send_json_error( [
                'message' => __( 'Transaction not found!', 'better-payment-pro' )
            ] );
        }

        if ( empty($email) ) {
            wp_send_json_error( [
                'message' => __( 'Email not valid!', 'better-payment-pro' )
            ] );
        }

        //Email
        $to = $email;
        $subject = __( 'Better Payment Receipt', 'better-payment-pro' );
        

        wp_enqueue_style( 'better-payment-pro-admin-style' );
        wp_enqueue_script( 'better-payment-pro-admin-script' );
        wp_enqueue_style( 'better-payment-pro-common-style' );
        wp_enqueue_script( 'better-payment-pro-common-script' );
        
        $better_payment_transaction_obj = $transaction;
        ob_start();
		include BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH . "/template-email-transaction-receipt.php";
        $message = ob_get_contents();
        ob_end_clean();

        $headers = array('Content-Type: text/html; charset=UTF-8');
        $attachments = array();

        $email_sent_status = \Better_Payment\Lite\Classes\Handler::send_better_email($to, $subject, $message, $headers);

        wp_send_json_success([
            'message' => __( 'Transaction receipt sent successfully!', 'better-payment-pro' ),
            'transaction' => $transaction,
            'email_sent_status' => $email_sent_status,
        ]);
    }
}
