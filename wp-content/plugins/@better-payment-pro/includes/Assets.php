<?php

namespace Better_Payment\Pro;

/**
 * Assets handler class
 * 
 * @since 0.0.1
 */
class Assets extends Controller{

    /**
     * Class constructor
     * 
     * @since 0.0.1
     */
    public function __construct() {
        add_action( 'wp_enqueue_scripts', [ $this, 'register_assets' ] );
        add_action( 'admin_enqueue_scripts', [ $this, 'register_assets' ] );
    }

    /**
     * All available scripts
     *
     * @return array
     * @since 0.0.1
     */
    public function get_scripts() {
        return [
            'better-payment-pro-common-script' => [
                'src'     => BETTER_PAYMENT_PRO_ASSETS . '/js/common.min.js',
                'version' => filemtime( BETTER_PAYMENT_PRO_PATH . '/assets/js/common.min.js' ),
                'deps'    => [ 'jquery', 'wp-util' ]
            ],
            'better-payment-pro-script' => [
                'src'     => BETTER_PAYMENT_PRO_ASSETS . '/js/frontend.min.js',
                'version' => filemtime( BETTER_PAYMENT_PRO_PATH . '/assets/js/frontend.min.js' ),
                'deps'    => [ 'jquery' ]
            ],
            'better-payment-pro-admin-script' => [
                'src'     => BETTER_PAYMENT_PRO_ASSETS . '/js/admin.min.js',
                'version' => filemtime( BETTER_PAYMENT_PRO_PATH . '/assets/js/admin.min.js' ),
                'deps'    => [ 'jquery', 'wp-util' ]
            ],
            'better-payment-pro-select2-script' => [
                'src'     => BETTER_PAYMENT_PRO_ASSETS . '/vendor/select2/select2.min.js',
                'version' => filemtime( BETTER_PAYMENT_PRO_PATH . '/assets/vendor/select2/select2.min.js' ),
                'deps'    => [ 'jquery', 'wp-util' ]
            ],
            'better-payment-pro-chartjs-script' => [
                'src'     => BETTER_PAYMENT_PRO_ASSETS . '/vendor/chartjs/chart.min.js',
                'version' => filemtime( BETTER_PAYMENT_PRO_PATH . '/assets/vendor/chartjs/chart.min.js' ),
                'deps'    => [ 'jquery', 'wp-util' ]
            ],
            'better-payment-pro-analytics-script' => [
                'src'     => BETTER_PAYMENT_PRO_ASSETS . '/js/analytics.min.js',
                'version' => filemtime( BETTER_PAYMENT_PRO_PATH . '/assets/js/analytics.min.js' ),
                'deps'    => [ 'jquery', 'wp-util' ]
            ],
        ];
    }

    /**
     * All available styles
     *
     * @return array
     * @since 0.0.1
     */
    public function get_styles() {
        return [
            'better-payment-pro-common-style' => [
                'src'     => BETTER_PAYMENT_PRO_ASSETS . '/css/common.min.css',
                'version' => filemtime( BETTER_PAYMENT_PRO_PATH . '/assets/css/common.min.css' )
            ],
            'better-payment-pro-style' => [
                'src'     => BETTER_PAYMENT_PRO_ASSETS . '/css/frontend.min.css',
                'version' => filemtime( BETTER_PAYMENT_PRO_PATH . '/assets/css/frontend.min.css' )
            ],
            'better-payment-pro-admin-style' => [
                'src'     => BETTER_PAYMENT_PRO_ASSETS . '/css/admin.min.css',
                'version' => filemtime( BETTER_PAYMENT_PRO_PATH . '/assets/css/admin.min.css' )
            ],
            'better-payment-pro-select2-style' => [
                'src'     => BETTER_PAYMENT_PRO_ASSETS . '/vendor/select2/select2.min.css',
                'version' => filemtime( BETTER_PAYMENT_PRO_PATH . '/assets/vendor/select2/select2.min.css' )
            ],
        ];
    }

    /**
     * Register scripts and styles
     *
     * @return void
     * @since 0.0.1
     */
    public function register_assets() {
        $scripts = $this->get_scripts();
        $styles  = $this->get_styles();

        foreach ( $scripts as $handle => $script ) {
            $deps = isset( $script['deps'] ) ? $script['deps'] : false;

            wp_register_script( $handle, $script['src'], $deps, $script['version'], true );
        }

        foreach ( $styles as $handle => $style ) {
            $deps = isset( $style['deps'] ) ? $style['deps'] : false;

            wp_register_style( $handle, $style['src'], $deps, $style['version'] );
        }

        wp_localize_script( 'better-payment-pro-admin-script', 'betterPaymentPro', [
            'ajaxurl' => admin_url( 'admin-ajax.php' ),
            'nonce' => wp_create_nonce( 'better-payment-pro-admin-nonce' ),
            'confirm' => __( 'Are you sure?', 'better-payment-pro' ),
            'error' => __( 'Something went wrong', 'better-payment-pro' ),
        ] );
    }
}
