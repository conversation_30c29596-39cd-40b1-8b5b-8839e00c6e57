<?php

namespace Better_Payment\Pro;

use DB;

/**
 * API Class
 * 
 * @since 0.0.1
 */
class Config extends Controller{

	public $better_payment_lite_settings = array();

    public $paypal_live_mode = 'no';
    public $stripe_live_mode = 'no';

    public $stripe_secret_key_live = '<<STRIPE-SECRET-KEY-LIVE>>'; 
    public $stripe_secret_key_test = '<<STRIPE-SECRET-KEY-TEST>>'; 

    public $paypal_client_id_live = '<<PAYPAL-CLIENT-ID-LIVE>>';
    public $paypal_client_secret_live = '<<PAYPAL-CLIENT-SECRET-LIVE>>';    
    public $paypal_client_id_test = '<<PAYPAL-CLIENT-ID-SANDBOX>>';
    public $paypal_client_secret_test = '<<PAYPAL-CLIENT-ID-SANDBOX>>';    
    
    public $paypal_client_id = '';
    public $paypal_client_secret = '';
    public $stripe_secret_key = '';    
    

    public function __construct() {
        $this->better_payment_lite_settings = get_option('better_payment_settings');
        
        $this->paypal_live_mode = isset($this->better_payment_lite_settings['better_payment_settings_payment_paypal_live_mode']) ? $this->better_payment_lite_settings['better_payment_settings_payment_paypal_live_mode'] : 'no';
        $this->stripe_live_mode = isset($this->better_payment_lite_settings['better_payment_settings_payment_stripe_live_mode']) ? $this->better_payment_lite_settings['better_payment_settings_payment_stripe_live_mode'] : 'no';

        $this->stripe_secret_key_live = isset($this->better_payment_lite_settings['better_payment_settings_payment_stripe_live_secret']) ? $this->better_payment_lite_settings['better_payment_settings_payment_stripe_live_secret'] : '';
        $this->stripe_secret_key_test = isset($this->better_payment_lite_settings['better_payment_settings_payment_stripe_test_secret']) ? $this->better_payment_lite_settings['better_payment_settings_payment_stripe_test_secret'] : '';
        
        $this->paypal_client_id_live = isset($this->better_payment_lite_settings['better_payment_settings_payment_paypal_live_client_id']) ? $this->better_payment_lite_settings['better_payment_settings_payment_paypal_live_client_id'] : '';
        $this->paypal_client_id_test = isset($this->better_payment_lite_settings['better_payment_settings_payment_paypal_test_client_id']) ? $this->better_payment_lite_settings['better_payment_settings_payment_paypal_test_client_id'] : '';
        $this->paypal_client_secret_live = isset($this->better_payment_lite_settings['better_payment_settings_payment_paypal_live_secret']) ? $this->better_payment_lite_settings['better_payment_settings_payment_paypal_live_secret'] : '';
        $this->paypal_client_secret_test = isset($this->better_payment_lite_settings['better_payment_settings_payment_paypal_test_secret']) ? $this->better_payment_lite_settings['better_payment_settings_payment_paypal_test_secret'] : '';
        
        $this->paypal_client_id = $this->paypal_live_mode == 'yes' ? $this->paypal_client_id_live : $this->paypal_client_id_test;
        $this->paypal_client_secret = $this->paypal_live_mode == 'yes' ? $this->paypal_client_secret_live : $this->paypal_client_secret_test;
        $this->stripe_secret_key = $this->stripe_live_mode == 'yes' ? $this->stripe_secret_key_live : $this->stripe_secret_key_test;
    }

    public function paypal_api_url() {
        return $this->paypal_live_mode == 'yes' ? 'https://api-m.paypal.com' : 'https://api-m.sandbox.paypal.com';
    }
}
