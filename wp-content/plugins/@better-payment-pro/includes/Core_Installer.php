<?php

namespace Better_Payment\Pro;

/**
 * Better Payment Upsell Class
 * 
 * @since 0.0.1
 */
class Core_Installer extends Controller{

    /**
     * Instantiate the class
     *
     * @param string $affiliate
     * @since 0.0.1
     */
    public function __construct() {
        add_action( 'init', array( $this, 'init_hooks' ) );
    }

    /**
     * Initialize the hooks
     *
     * @return void
     * @since 0.0.1
     */
    public function init_hooks() {

        if ( class_exists( 'Better_Payment' ) ) {
            return;
        }

        if ( ! current_user_can( 'manage_options' ) ) {
            return;
        }

        add_action( 'admin_notices', array( $this, 'activation_notice' ) );

        add_action( 'wp_ajax_Better_Payment_Pro_Install_Core_installer', array( $this, 'install_better_payment_core' ) );
    }
    /**
     * Show the plugin installation notice
     *
     * @return void
     * @since 0.0.1
     */
    public function activation_notice() {

        ?>

        <script>
            jQuery(document).ready( function($) {
                $('#better-payment-install-core').on('click', function (e) {
                    var self = $(this);
                    e.preventDefault();
                    self.addClass('install-now updating-message');
                    self.text('<?php echo esc_js( 'Installing...' ); ?>');

                    $.ajax({
                        url: '<?php echo esc_url( admin_url( 'admin-ajax.php' ) ); ?>',
                        type: 'post',
                        data: {
                            action: 'Better_Payment_Pro_Install_Core_installer',
                            _wpnonce: '<?php echo esc_attr( wp_create_nonce('Better_Payment_Pro_Install_Core_installer') ); ?>',
                        },
                        success: function(response) {
                            self.text('<?php echo esc_js( 'Installed' ); ?>');
                            window.location.href = '<?php echo esc_url( admin_url( 'admin.php?page=better-payment-setup' ) ); ?>';
                        },
                        error: function(error) {
                            self.removeClass('install-now updating-message');
                            alert( error );
                        },
                        complete: function() {
                            self.attr('disabled', 'disabled');
                            self.removeClass('install-now updating-message');
                        }
                    });
                });
            } );
        </script>
        <?php
    }


    /**
     * Fail if plugin installtion/activation fails
     *
     * @param  Object $thing
     *
     * @return void
     * @since 0.0.1 
     */
    public function fail_on_error( $thing ) {
        if ( is_wp_error( $thing ) ) {
            wp_send_json_error( $thing->get_error_message() );
        }
    }

    /**
     * Install Better Payment
     *
     * @return void
     * @since 0.0.1
     */
    public function install_better_payment_core() {
        check_ajax_referer( 'Better_Payment_Pro_Install_Core_installer' );
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( __( 'You don\'t have permission to install the plugins' ) );
        }
        $is_installed = isset( $_POST['installed'] ) ? $_POST['installed'] : false;
        $bp_pro_status = $this->install_plugin( 'better-payment', 'better-payment.php' );
        $this->fail_on_error( $bp_pro_status );

        wp_send_json_success();
    }

    /**
     * Install and activate a plugin
     *
     * @param  string $slug
     * @param  string $file
     *
     * @return WP_Error|null
     * @since 0.0.1
     */
    public function install_plugin( $slug, $file ) {
        include_once ABSPATH . 'wp-admin/includes/plugin-install.php';
        include_once ABSPATH . 'wp-admin/includes/class-wp-upgrader.php';

        $plugin_basename = $slug . '/' . $file;

        // if exists and not activated
        if ( file_exists( WP_PLUGIN_DIR . '/' . $plugin_basename ) ) {
            return activate_plugin( $plugin_basename );
        }

        // seems like the plugin doesn't exists. Download and activate it
        $upgrader = new \Plugin_Upgrader( new \WP_Ajax_Upgrader_Skin() );

        $api      = plugins_api( 'plugin_information', array( 'slug' => $slug, 'fields' => array( 'sections' => false ) ) );
        $result   = $upgrader->install( $api->download_link );

        if ( is_wp_error( $result ) ) {
            return $result;
        }

        return activate_plugin( $plugin_basename );
    }
}
