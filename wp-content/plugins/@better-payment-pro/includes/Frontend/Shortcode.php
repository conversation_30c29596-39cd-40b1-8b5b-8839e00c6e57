<?php

namespace Better_Payment\Pro\Frontend;

use Better_Payment\Pro\Controller;

/**
 * Shortcode handler class
 * 
 * @since 0.0.1
 */
class Shortcode extends Controller{

    /**
     * Initialize the class
     * 
     * @since 0.0.1
     */
    function __construct() {
        add_shortcode( 'better-payment-pro', [ $this, 'render_shortcode' ] );
    }

    /**
     * Shortcode handler class
     *
     * @param  array $atts
     * @param  string $content
     *
     * @return string
     * @since 0.0.1
     */
    public function render_shortcode( $atts, $content = '' ) {
        wp_enqueue_style( 'better-payment-pro-style' );
        wp_enqueue_script( 'better-payment-pro-script' );
        wp_enqueue_style( 'better-payment-pro-common-style' );
        wp_enqueue_script( 'better-payment-pro-common-script' );

        return '<div class="better-payment-pro-shortcode">Better Payment - Shortcode</div>';
    }
}
