<?php

namespace Better_Payment\Pro;

/**
 * Installer class
 * 
 * @since 0.0.1
 */
class Installer extends Controller{

    /**
     * Run the installer
     *
     * @return void
     * @since 0.0.1
     */
    public function run() {
        $this->add_version();
        $this->create_tables();
    }

    /**
     * Add time and version on DB
     * 
     * @since 0.0.1
     */
    public function add_version() {
        $installed = get_option( 'better_payment_pro_installed' );

        if ( ! $installed ) {
            update_option( 'better_payment_pro_installed', time() );
        }

        update_option( 'better_payment_pro_version', BETTER_PAYMENT_PRO_VERSION );
    }

    /**
     * Create necessary database tables
     *
     * @return void
     * @since 0.0.1
     */
    public function create_tables() {
        //
    }
}
