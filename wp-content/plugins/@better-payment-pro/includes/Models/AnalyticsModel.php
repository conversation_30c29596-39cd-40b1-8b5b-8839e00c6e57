<?php

namespace Better_Payment\Pro\Models;

use Better_Payment\Lite\Admin\DB;
use Better_Payment\Pro\Model;

use function Better_Payment\Lite\Classes\better_payment_dd;

/**
 * The Analytics handler database class
 * 
 * @since 0.0.1
 */
class AnalyticsModel extends Model {

    /**
     * Initialize the class
     * 
     * @since 0.0.1
     */
    public function __construct( ) {
        parent::__construct();
    }

    /**
     * Get all transactions amount
     *
     * @return array
     * @since 0.0.1
     */
    public function all_transactions_amount($from_cache = 1, $payment_date_from = '', $payment_date_to = '') {
        $amount = get_transient('better_payment_pro_all_transactions_amount');

        if($amount && $from_cache){
            return $amount;
        }

        $amount = $this->get_transactions_amount_by_statuses(array(), $payment_date_from, $payment_date_to, 'amount', 1);
        set_transient('better_payment_pro_all_transactions_amount', $amount, 3600);

        return $amount;
    }

    /**
     * Get completed transactions amount
     *
     * @return array
     * @since 0.0.1
     */
    public function completed_transactions_amount($from_cache = 1, $payment_date_from = '', $payment_date_to = '') {
        $amount = get_transient('better_payment_pro_completed_transactions_amount');
        
        if($amount && $from_cache){
            return $amount;
        }

        $statuses = $this->allowed_statuses('completed');

        $amount = $this->get_transactions_amount_by_statuses($statuses, $payment_date_from, $payment_date_to);
        set_transient('better_payment_pro_completed_transactions_amount', $amount, 3600);

        return $amount;
    }
    
    /**
     * Get incomplete transactions amount
     *
     * @return array
     * @since 0.0.1
     */
    public function incomplete_transactions_amount($from_cache = 1, $payment_date_from = '', $payment_date_to = '') {
        $amount = get_transient('better_payment_pro_incomplete_transactions_amount');
        
        if($amount && $from_cache){
            return $amount;
        }

        $statuses = $this->allowed_statuses('incomplete');

        $amount = $this->get_transactions_amount_by_statuses($statuses, $payment_date_from, $payment_date_to, 'amount', 1);
        set_transient('better_payment_pro_incomplete_transactions_amount', $amount, 3600);
        
        return $amount;
    }

    /**
     * Get refund transactions amount
     *
     * @return array
     * @since 0.0.1
     */
    public function refund_transactions_amount($from_cache = 1, $payment_date_from = '', $payment_date_to = '' ) {
        $amount = get_transient('better_payment_pro_refund_transactions_amount');

        if($amount && $from_cache){
            return $amount;
        }

        $statuses = $this->allowed_statuses('refunded');

        $amount = $this->get_transactions_amount_by_statuses($statuses, $payment_date_from, $payment_date_to);
        set_transient('better_payment_pro_refund_transactions_amount', $amount, 3600);

        return $amount;
    }

    /**
     * Get unpaid transactions amount
     *
     * @return array
     * @since 0.0.1
     */
    public function unpaid_transactions_amount($from_cache = 1, $payment_date_from = '', $payment_date_to = '' ) {
        $amount = get_transient('better_payment_pro_refund_transactions_amount');

        if($amount && $from_cache){
            return $amount;
        }

        $statuses = $this->allowed_statuses('unpaid');

        $amount = $this->get_transactions_amount_by_statuses($statuses, $payment_date_from, $payment_date_to);
        set_transient('better_payment_pro_refund_transactions_amount', $amount, 3600);

        return $amount;
    }

    /**
     * Get transactions amount
     *
     * @return array
     * @since 0.0.1
     */
    public function get_transactions_amount_by_statuses($statuses = array(), $payment_date_from = '', $payment_date_to = '', $count_or_amount= 'amount', $fetchNull = 0) {
        $better_payment_db_obj = new DB();
        
        $amount = $better_payment_db_obj->get_transactions_amount_by_statuses($statuses, $payment_date_from, $payment_date_to, $count_or_amount, $fetchNull);

        return $amount;
    }

    /**
     * Get all transactions amount
     *
     * @return array
     * @since 0.0.1
     */
    public function all_transactions($from_cache = 1, $payment_date_from = '', $payment_date_to = '', $user_email = '') {
        $transactions = get_transient('better_payment_pro_all_transactions');

        if($transactions && $from_cache){
            return $transactions;
        }

        $transactions = $this->get_transactions_by_statuses(array(), $payment_date_from, $payment_date_to, 1);

        if ( ! empty( $user_email ) ) {
            $transactions = array_filter($transactions, function($transaction) use ($user_email) {
                return ! empty( $transaction->email ) && ( $transaction->email === $user_email );
            });
        }
        
        set_transient('better_payment_pro_all_transactions', $transactions, 3600);

        return $transactions;
    }

    /**
     * Get completed transactions amount
     *
     * @return array
     * @since 0.0.1
     */
    public function completed_transactions($from_cache = 1, $payment_date_from = '', $payment_date_to = '', $user_email = '') {
        $transactions = get_transient('better_payment_pro_completed_transactions');
        
        if($transactions && $from_cache){
            return $transactions;
        }

        $statuses = $this->allowed_statuses('completed');

        $transactions = $this->get_transactions_by_statuses($statuses, $payment_date_from, $payment_date_to);

        if ( ! empty( $user_email ) ) {
            $transactions = array_filter($transactions, function($transaction) use ($user_email) {
                return ! empty( $transaction->email ) && ( $transaction->email === $user_email );
            });
        }

        set_transient('better_payment_pro_completed_transactions', $transactions, 3600);

        return $transactions;
    }
    
    /**
     * Get incomplete transactions amount
     *
     * @return array
     * @since 0.0.1
     */
    public function incomplete_transactions($from_cache = 1, $payment_date_from = '', $payment_date_to = '', $user_email = '') {
        $transactions = get_transient('better_payment_pro_incomplete_transactions');
        
        if($transactions && $from_cache){
            return $transactions;
        }

        $statuses = $this->allowed_statuses('incomplete', 'v2');

        $transactions = $this->get_transactions_by_statuses($statuses, $payment_date_from, $payment_date_to, 1);
        
        if ( ! empty( $user_email ) ) {
            $transactions = array_filter($transactions, function($transaction) use ($user_email) {
                return ! empty( $transaction->email ) && ( $transaction->email === $user_email );
            });
        }

        set_transient('better_payment_pro_incomplete_transactions', $transactions, 3600);
        
        return $transactions;
    }

    /**
     * Get refund transactions amount
     *
     * @return array
     * @since 0.0.1
     */
    public function refund_transactions($from_cache = 1, $payment_date_from = '', $payment_date_to = '', $user_email = '') {
        $transactions = get_transient('better_payment_pro_refund_transactions');

        if($transactions && $from_cache){
            return $transactions;
        }

        $statuses = $this->allowed_statuses('refunded');

        $transactions = $this->get_transactions_by_statuses($statuses, $payment_date_from, $payment_date_to);
        
        if ( ! empty( $user_email ) ) {
            $transactions = array_filter($transactions, function($transaction) use ($user_email) {
                return ! empty( $transaction->email ) && ( $transaction->email === $user_email );
            });
        }
        
        set_transient('better_payment_pro_refund_transactions', $transactions, 3600);

        return $transactions;
    }

    /**
     * Get transactions amount
     *
     * @return array
     * @since 0.0.1
     */
    public function get_transactions_by_statuses($statuses = array(), $payment_date_from = '', $payment_date_to = '', $fetchNull = 0) {
        $better_payment_db_obj = new DB();
        $transactions = $better_payment_db_obj->get_transactions_by_statuses($statuses, $payment_date_from, $payment_date_to, $fetchNull);
        return $transactions;
    }

    /**
     * Allowed transaction statuses
     * 
     * @since 0.0.1
     */
    public function allowed_statuses($type = 'all', $version = 'v2' ){
        $better_payment_db_obj = new DB();
        $statuses = $better_payment_db_obj->allowed_statuses($type, $version);
        return $statuses;
    }
    
} 