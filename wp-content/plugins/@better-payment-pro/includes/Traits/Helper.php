<?php

namespace Better_Payment\Pro\Traits;


/**
 * Helper trait
 * 
 * @since 0.0.1
 */
trait Helper {
	use Elements, ElementorHelper, WordPressHelper;

    /**
     *
     * Strip tag based on allowed html tag
	 * @param $text
	 * @return string
	 */
    public function better_payment_wp_kses( $text ){
        return wp_kses( $text, $this->allowed_html() );
    }

	/**
	 * Allowed html tags
	 * 
	 * @since 0.0.1
	 */
    public function allowed_html(){
        $allowed_html_post = wp_kses_allowed_html( 'post' );
        
        $allowed_html_extra = [
			'input' => [
				'id' => [],
				'class' => [],
				'name' => [],
				'type'  => [],
				'placeholder' => [],
				'aria-required' => [],
				'required' => [],
				'min' => [],
				'max' => [],
                'value' => [],
			],
			'style' => [],
			'a' => [
                'href' => [],
                'title' => [],
                'class' => [],
                'rel' => [],
                'id' => [],
                'style' => []
            ],
            'q' => [
                'cite' => [],
                'class' => [],
                'id' => [],
            ],
            'img' => [
                'src' => [],
                'alt' => [],
                'height' => [],
                'width' => [],
                'class' => [],
                'id' => [],
                'style' => []
            ],
            'span' => [
                'class' => [],
                'id' => [],
                'style' => []
            ],
            'dfn' => [
                'class' => [],
                'id' => [],
                'style' => []
            ],
            'time' => [
                'datetime' => [],
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'cite' => [
                'title' => [],
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'hr' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'b' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'p' => [
                'class' => [],
                'id' => [],
                'style' => []
            ],
            'i' => [
                'class' => [],
                'id' => [],
                'style' => []
            ],
            'u' => [
                'class' => [],
                'id' => [],
                'style' => []
            ],
            's' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'br' => [],
            'em' => [
                'class' => [],
                'id' => [],
                'style' => []
            ],
            'code' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'mark' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'small' => [
                'class' => [],
                'id' => [],
                'style' => []
            ],
            'abbr' => [
                'title' => [],
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'strong' => [
                'class' => [],
                'id' => [],
                'style' => []
            ],
            'del' => [
                'class' => [],
                'id' => [],
                'style' => []
            ],
            'ins' => [
                'class' => [],
                'id' => [],
                'style' => []
            ],
            'sub' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'sup' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'div' => [
                'class' => [],
                'id' => [],
                'style' => []
            ],
            'strike' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'acronym' => [],
            'h1' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'h2' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'h3' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'h4' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'h5' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'h6' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'button' => [
                'class' => [],
                'id' => [],
                'style' => [],
            ],
            'center' => [
	            'class' => [],
	            'id'    => [],
	            'style' => [],
            ],
            'ul' => [
	            'class' => [],
	            'id'    => [],
	            'style' => [],
            ],
            'ol' => [
	            'class' => [],
	            'id'    => [],
	            'style' => [],
            ],
            'li' => [
	            'class' => [],
	            'id'    => [],
	            'style' => [],
            ],
            'table' => [
	            'class' => [],
	            'id'    => [],
	            'style' => [],
                'dir'   => [],
                'align' => [],
            ],
            'thead' => [
	            'class' => [],
	            'id'    => [],
	            'style' => [],
                'align' => [],
            ],
            'tbody' => [
	            'class' => [],
	            'id'    => [],
	            'style' => [],
                'align' => [],
            ],
            'tfoot' => [
	            'class' => [],
	            'id'    => [],
	            'style' => [],
                'align' => [],
            ],
            'th' => [
	            'class' => [],
	            'id'    => [],
	            'style' => [],
                'align' => [],
            ],
            'tr' => [
	            'class' => [],
	            'id'    => [],
	            'style' => [],
                'align' => [],
            ],
            'td' => [
	            'class' => [],
	            'id'    => [],
	            'style' => [],
                'align' => [],
            ],
		];

        $allowed_html_merged = wp_parse_args( $allowed_html_extra, $allowed_html_post );

        return $allowed_html_merged;
    }

	/**
	 * Render a view file
	 * 
	 * @since 0.0.1
	 */
	public function view($filename, $data = []){
		$this->enqueue_backend_styles_scripts();
        
        ob_start();
        $this->render_view($filename, $data);
        $view_content = ob_get_clean();

        echo $view_content;
	}
	/**
	 * Render a view
	 * 
	 * @since 0.0.1
	 */
	public function render_view($filename, $data = array()){
		$view_path = $this->get_view_path($filename);
		if(!file_exists($view_path)){
			return;
		}
		extract($data);
		
		include $view_path;
	}

	/**
	 * Get the view path
	 * 
	 * @since 0.0.1
	 */
	public function get_view_path(){
		$args = func_get_args();
		$view_name = array_shift($args);
		$view_path = $this->get_view_dir() . '/' . $view_name . '.php';
		return $view_path;
	}

	/**
	 * Get the view dir
	 * 
	 * @since 0.0.3
	 */
	public function get_view_dir(){
		$view_dir = BETTER_PAYMENT_PRO_ADMIN_VIEWS_PATH;
		return $view_dir;
	}

	/**
	 * Enqueue common styles and scripts
	 * 
	 * @since 0.0.3
	 */
	public function enqueue_backend_styles_scripts(){
		wp_enqueue_style( 'better-payment-pro-admin-style' );
        wp_enqueue_script( 'better-payment-pro-admin-script' );
        wp_enqueue_style( 'better-payment-pro-common-style' );
        wp_enqueue_script( 'better-payment-pro-common-script' );
		wp_enqueue_style('better-payment-pro-select2-style');
        wp_enqueue_script('better-payment-pro-select2-script');

	}

    public function get_licensing_arguments(){
        $args = [
            'plugin_file'    => BETTER_PAYMENT_PRO_FILE,
            'version'        => BETTER_PAYMENT_PRO_VERSION,
            'item_id'        => BETTER_PAYMENT_PRO_SL_ITEM_ID,
            'item_name'      => BETTER_PAYMENT_PRO_SL_ITEM_NAME,
            'item_slug'      => BETTER_PAYMENT_PRO_SL_ITEM_SLUG,
            'storeURL'       => BETTER_PAYMENT_PRO_STORE_URL,
            'textdomain'     => 'better-payment-pro',
            'db_prefix'      => BETTER_PAYMENT_PRO_SL_ITEM_SLUG,
            'scripts_handle' => 'better-payment-pro-admin-script',
            'page_slug'      => 'better-payment-settings',
            'screen_id'      => [ "toplevel_page_better-payment-settings" ],
            'api'            => 'ajax',
            'ajax'           => [
                'textdomain'    => 'better-payment-pro',
                'action_prefix' => 'better-payment-pro'
            ]
        ];

        return $args;
    }
}
