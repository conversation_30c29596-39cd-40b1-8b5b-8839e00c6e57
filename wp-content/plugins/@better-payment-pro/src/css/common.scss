.better-payment {
    a {
        color: inherit;
        transition: color .3s ease, background .3s ease, border .3s ease, box-shadow .3s ease;
        text-decoration: none;
        display: inline-block;
    }

    label {
        margin-bottom: 0;
    }

    h1,
    h2,  
    h3,
    h4,
    h5,
    h6,
    p {
        margin: 0;
        padding: 0;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        font-weight: 700;
        line-height: 1.3; 
    }

    img,
    video{
        max-width: 100%;
    }

    .card-field {
        position: relative;
    }

    .card-field .icons {
        position: absolute;
        right: 20px;
        bottom: 17px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .better-payment-container {
        max-width: 1170px;
        margin: 0 auto;
        padding: 0 15px;
    }

    .payment-button {
        height: unset;
    }

    .bp-flex {
        display: flex;
    }

    .bp-flex-1 {
        flex: 1;
    }

    .bp-flex-3 {
        flex: 3;
    }

    .bp-w-100 {
        width: 100%;
    }   

    .split-payment-text-wrap input,
    .split-payment-text-wrap select {
        border-radius: 8px;
        // border: 1px solid rgba(255, 255, 255, 0.30);
        // width: 75px;
        min-width: 75px;
        width: unset;
        height: 36px;
        background: transparent;
        padding: 0 10px;
        // color: #fff;
        outline: none;
        font-family: 'IBM Plex Sans', sans-serif;
    }

    .layout-4-general {
        background: #fff;
        border-radius: 24px;
        box-shadow: 0px 2px 3px 0px rgba(32, 31, 80, 0.15);
        font-family: 'IBM Plex Sans', sans-serif;
        padding: 50px;
        display: flex;
        gap: 32px;
        align-items: flex-start;

        h1, h2, h3, h4, h5, h6 {
            font-family: 'IBM Plex Sans', sans-serif;
        }
    
        .reversed {
            flex-direction: row-reverse;
        }

        .split-payment .payment-type-one-time {
            flex: 0 0 65% !important;
        }
    
        .payment-button {
            border-radius: 8px;
            background: #6E58F7;
            color: #FFF;
            text-align: center;
            font-size: 18px;
            font-weight: 500;
            width: 100%;
            padding: 15.5px 20px;
            border: 0 !important;
            outline: none !important;
            box-shadow: none;
            cursor: pointer;
        }

        .split-payment-text-wrap input, 
        .split-payment-text-wrap select {
            border: 1px solid #d3dbee;
        }
    
        .col-wrap {
            display: flex;
            gap: 16px;
        }
    
        .col-wrap .span-50 {
            flex: 1 1 calc(50% - 8px);
        }
    
        .check-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }
    
        .check-item input {
            display: none;
        }
    
        .check-item .checkbox {
            border-radius: 4px;
            border: 2px solid #D3DBEE;
            background: #FFF;
            display: block;
            height: 20px;
            width: 20px;
            cursor: pointer;
            position: relative;
        }
    
        .check-item .checkbox::before {
            border-radius: 2px;
            background: #6E58F7;
            height: 12px;
            width: 12px;
            opacity: 0;
            transition: all .3s ease;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            content: '';
        }
    
        .check-item input:checked + .checkbox::before {
            opacity: 1;
        }
    
        .transaction-details-wrap {
            border-radius: 16px;
            background: #F4F4F8;
            padding: 32px;
            min-width: 340px;
            flex-basis: 340px;
        }
    
        .transaction-details-wrap .transaction-details-header {
            margin-bottom: 40px;
        }
    
        .transaction-details-wrap .transaction-details-header h3 {
            color: #2B2748;
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 10px;
        }
    
        .transaction-details-wrap .transaction-details-header p,
        .split-payment-note-wrap span,
        .transaction-details-wrap .transaction-details-footer  li.split-payment-note-wrap span {
            color: #8088A6;
            font-size: 16px;
            font-weight: 500;
            word-break: break-word;
        }
        
        .transaction-details-wrap .transaction-details-footer ul {
            padding: 0;
            margin: 0;
            list-style: none;
        }
    
        .transaction-details-wrap .transaction-details-footer ul li {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    
        .transaction-details-wrap .transaction-details-footer ul li:not(:last-child) {
            margin-bottom: 8px;
        }
    
        .transaction-details-wrap .transaction-details-footer ul li span {
            color: #48506D;
            font-size: 18px;
            font-weight: 500;
        }
    
        .transaction-details-wrap .transaction-details-footer ul li.discount .amount {
            color: #6E58F7;
        }
    
        .transaction-details-wrap .transaction-details-footer ul li .total-amount-text span {
            color: #6E58F7;
        }
    
        .transaction-details-wrap .transaction-details-footer ul li .total-amount-text span {
            color: #6E58F7;
        }
    
        .transaction-details-wrap .transaction-details-footer ul li.total {
            padding-top: 15px;
            margin-top: 20px;
            // border-top: 2px solid #D3DBEE;
        }
    
        .transaction-details-wrap .transaction-details-footer ul li.total .amount {
            color: #2B2748;
            font-size: 20px;
            font-weight: 600;
        }
    
        .general-form-wrap {
            border-radius: 16px;
            border: 2px solid #D3DBEE;
            background: #FFF;
            padding: 30px;
            flex: 1;
            width: 100%;
        }
    
        .general-form-wrap .payment-details-title {
            color: #2B2748;
            font-size: 32px;
            font-weight: 500;
            margin-bottom: 20px;
        }
    
        .general-form-wrap .payment-method-items {
            display: flex;
            gap: 24px;
            flex-wrap: wrap;
        }
    
        .general-form-wrap .payment-method-items .payment-method-item {
            flex: 1;
        }
    
        .general-form-wrap .payment-method-items .payment-method-item input {
            display: none;
        }
    
        .general-form-wrap .payment-method-items .payment-method-item .payment-method-image-wrap {
            border-radius: 8px;
            background: #F4F4F8;
            padding: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 7px;
        }
    
        .general-form-wrap .payment-method-items .payment-method-item input:checked + .payment-method-image-wrap {
            border: 1px solid #6E58F7;
        }
    
        .general-form-wrap .payment-method-items .payment-method-item .payment-method-image-wrap span {
            display: inline-block;
        }
    
        .general-form-wrap .payment-method-items .payment-method-item .payment-method-image-wrap .payment-method-image {
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            
            &:not(.card-icon) {
                width: 100%;
            }
        }
    
        .general-form-wrap .payment-method-items .payment-method-item .payment-method-image-wrap .payment-method-image img {
            max-height: 100%;
        }
    
        .general-form-wrap .payment-method-items .payment-method-item .payment-method-image-wrap .donation-form-header {
            color: #2B2748;
            font-size: 16px;
            font-weight: 600;
            display: block;
        }
    
        .general-form-wrap .form-group .label {
            color: #2B2748;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
            display: inline-block;
        }
    
        .general-form-wrap .form-group .label .required,
        .general-form-wrap .payment-amount-wrap .label .required {
            color: #e04f5f;
        }
    
        .general-form-wrap .form-group .form-control {
            border-radius: 8px;
            border: 2px solid #D3DBEE;
            width: 100%;
            height: 56px;
            padding: 0 20px;
            outline: none;
            font-size: 18px;
            font-weight: 500;
            color: #2B2748;
        }
    
        .general-form-wrap .form-group .form-control:focus {
            border: 2px solid #6E58F7;
        }
    
        .general-form-wrap .form-group .form-control::-webkit-input-placeholder {
            color: #8088A6;
        }
    
        .general-form-wrap .form-group .form-control::-moz-placeholder {
            color: #8088A6;
        }
    
        .general-form-wrap .form-group .form-control:-ms-input-placeholder {
            color: #8088A6;
        }
        
        .general-form-wrap .form-group .form-control:-moz-placeholder {
            color: #8088A6;
        }

        .general-form-wrap .payment-amount-wrap {
            margin-bottom: 20px;
        }
    
        .general-form-wrap .payment-amount-wrap .label {
            color: #2B2748;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 15px;
            display: inline-block;
        }
    
        .general-form-wrap .payment-amount-wrap .payment-amounts {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 24px;
            margin-bottom: 20px;
        }
    
        .general-form-wrap .payment-amount-wrap .payment-amounts .payment-amount {
            flex: 0 0 calc(20% - 20px);
        }
        
        .general-form-wrap .payment-amount-wrap .payment-amounts .payment-amount input {
            display: none;
        }
    
        .general-form-wrap .payment-amount .text {
            border-radius: 8px;
            background: #F4F4F8;
            padding: 17px 10px;
            cursor: pointer;
            transition: all .3s ease;
            width: 100%;
            display: block;
            text-align: center;
            border: 1px solid transparent;
            color: #2B2748;
            font-size: 18px;
            font-weight: 500;
        }
    
        .general-form-wrap .payment-amount input:checked + .text {
            border: 1px solid #6E58F7;
            background: #FFF;
            color: #2B2748;
        }
    
        .general-form-wrap .payment-amount-wrap .custom-amount input {
            border-radius: 8px;
            border: 1px solid #F4F4F8;
            background: #FFF;
            height: 56px;
            width: 100%;
            padding: 0 20px;
            color: #2B2748;
            font-size: 18px;
            font-weight: 500;
            outline: none;
        }
    }

    .layout-5-donation {
        border-radius: 16px;
        background: #FFF;
        padding: 48px;
        display: flex;
        gap: 48px;
        font-family: 'IBM Plex Sans', sans-serif;
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'IBM Plex Sans', sans-serif;
        }

        .reversed {
            flex-direction: row-reverse;
        }

        .donation-image-wrap {
            flex: 0 0 calc(45% - 48px);
            border-radius: 16px;
            overflow: hidden;
            background-image: url('../img/donation.jpg');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center center;
        }

        .col-wrap {
            display: flex;
            gap: 16px;
        }

        .col-wrap .span-50 {
            flex: 1 1 calc(50% - 8px);
        }

        .donation-image-wrap img {
            height: 100%;
            width: 100%;
            object-fit: cover;
        }

        .donation-form-wrap {
            flex: 0 0 55%;
        }

        .donation-form-wrap .donation-form-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 30px;
    
            .icon {
                width: unset;
            }
        }

        .donation-form-wrap .donation-form-header h3 {
            color: #2B2748;
            font-size: 32px;
            font-weight: 500;
        }

        .donation-form-wrap .payment-method-items-wrap {
            margin-bottom: 30px;
        }
        
        .donation-form-wrap .payment-method-items-wrap .label {
            color: #48506D;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 15px;
        }

        .donation-form-wrap .payment-method-items-wrap .payment-method-items {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 25px;
        }

        .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item input {
            display: none;
        }

        .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .indicator {
            border-radius: 12px;
            border: 1px solid #92CBAC;
            background: #E9F8F1;
            height: 20px;
            width: 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all .3s ease;
            position: relative;
        }

        .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .indicator::before {
            border-radius: 10px;
            background: #13AE5C;
            height: 10px;
            width: 10px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            content: "";
            opacity: 0;
            transition: all .3s ease;
        }

        .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .text {
            color: #2B2748;
            font-size: 20px;
            font-weight: 500;
        }

        .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item input:checked + .indicator {
            border: 1px solid #13AE5C;
        }

        .donation-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item input:checked + .indicator::before {
            opacity: 1;
        }

        .donation-form-wrap .payment-type-items-wrap {
            margin-bottom: 20px;

            &.bp-flex {
                flex-wrap: wrap;
                gap: 10px;
                justify-content: start;
            }

            .payment-type-item-wrap {
                min-width: 175px;
            }
        }

        .donation-form-wrap .payment-type-items-wrap .label {
            color: #48506D;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 15px;
        }

        .donation-form-wrap .payment-type-items-wrap .payment-type-items {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 24px;
        }

        // .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item {
        //     flex: 0 0 calc(33.33% - 16px);
        // }

        .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item > div {
            border-radius: 8px;
            background: #EEF6F2;
            padding: 17.5px 10px;
            justify-content: center;
            display: flex;
            align-items: center;
            gap: 0;
            cursor: pointer;
            transition: all .3s ease;
            gap: 5px;
        }

        .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item input {
            display: none;
        }

        .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item input:checked + div {
            background: #13AE5C;
        }

        .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item .indicator {
            height: 20px;
            width: 20px;
            margin-left: -20px;
            opacity: 0;
            transition: all .3s ease;
        }

        .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item input:checked + div .indicator {
            margin-left: 0;
            opacity: 1;
        }

        .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item .text {
            color: #4C7B62;
            font-size: 18px;
            font-weight: 500;
            transition: all .3s ease;
        }

        .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item input:checked + div .text {
            color: #FFFFFF;
        }

        .donation-form-wrap .payment-amount-wrap {
            margin-bottom: 20px;
        }

        .donation-form-wrap .payment-amount-wrap .label {
            color: #2B2748;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 15px;
        }

        .donation-form-wrap .payment-amount-wrap .payment-amounts {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 24px;
            margin-bottom: 20px;
        }

        .donation-form-wrap .payment-amount-wrap .payment-amounts .payment-amount {
            flex: 0 0 calc(20% - 20px);
        }

        .donation-form-wrap .payment-amount-wrap .payment-amounts .payment-amount input {
            display: none;
        }

        .donation-form-wrap .payment-amount .text {
            border-radius: 8px;
            background: #EEF6F2;
            padding: 17px 0;
            cursor: pointer;
            transition: all .3s ease;
            width: 100%;
            display: block;
            text-align: center;
            border: 1px solid transparent;
            color: #4C7B62;
            font-size: 18px;
            font-weight: 500;
        }

        .donation-form-wrap .payment-amount-wrap .payment-amounts .payment-amount input:checked + .text {
            border: 1px solid #13AE5C;
            background: #FFF;
            color: #335B46;
        }

        .donation-form-wrap .payment-amount-wrap .custom-amount input,
        .donation-form-wrap .field-primary_payment_amount .form-group input {
            border-radius: 8px;
            border: 1px solid #4C7B62;
            background: #FFF;
            height: 56px;
            width: 100%;
            padding: 0 20px;
            color: #335B46;
            font-size: 18px;
            font-weight: 500;
            outline: none;
        }

        .donation-form-wrap .form-group .label {
            color: #2B2748;
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
            display: inline-block;
        }

        .donation-form-wrap .form-group .label .required,
        .donation-form-wrap .payment-amount-wrap .label .required {
            color: #e04f5f;
        }

        .donation-form-wrap .form-group .form-control {
            border-radius: 8px;
            border: none;
            background: #EEF6F2;
            width: 100%;
            height: 56px;
            padding: 0 20px;
            outline: none;
            font-size: 18px;
            font-weight: 500;
            color: #4C7B62;
        }

        .donation-form-wrap .form-group .form-control::-webkit-input-placeholder {
            color: #4C7B62;
        }

        .donation-form-wrap .form-group .form-control::-moz-placeholder {
            color: #4C7B62;
        }

        .donation-form-wrap .form-group .form-control:-ms-input-placeholder {
            color: #4C7B62;
        }

        .donation-form-wrap .form-group .form-control:-moz-placeholder {
            color: #4C7B62;
        }

        .donation-button {
            border-radius: 8px;
            background: #13AE5C;
            color: #FFF;
            text-align: center;
            font-size: 18px;
            font-weight: 500;
            width: 100%;
            padding: 15.5px 20px;
            border: 0 !important;
            outline: none !important;
            box-shadow: none;
            cursor: pointer;
        }

        .payment-label-wrap {
            margin-bottom: 8px;
        }
    }

    .layout-6-woo {
        border-radius: 8px;
        background: #FFF;
        box-shadow: 0px 2px 3px 0px rgba(32, 31, 80, 0.15);
        font-family: 'IBM Plex Sans', sans-serif;
        display: flex;

        h1, h2, h3, h4, h5, h6 {
            font-family: 'IBM Plex Sans', sans-serif;
        }

        .reversed {
            flex-direction: row-reverse;
        }

        .split-payment .payment-type-one-time {
            flex: 0 0 65% !important;
        }

        .order-details-wrap {
            border-radius: 8px 0px 0px 8px;
            background: linear-gradient(196deg, #AF6CFB 0%, #7C5ADD 74.17%);
            flex: 0 0 50%;
            min-width: 50%;
        }

        .order-details {
            padding: 46px;
        }

        .order-details .title {
            color: #FFF;
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 30px;
        }

        .order-details .order-list {
            border-bottom: 1px solid rgba(255, 255, 255, 0.30);
        }

        .order-details .order-list .order-item {
            display: flex;
            align-items: center;
            gap: 15px;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .order-details .order-list .order-item:not(:last-child) {
            
        }

        .order-details .order-list .order-item .thumb {
            width: 60px;
            height: 60px;
            min-width: 60px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #fff;
        }

        .order-details .order-list .order-item .thumb img {
            height: 100%;
            width: 100%;
            object-fit: cover;
        }

        .order-details .order-list .order-item .name {
            flex: 1;
        }

        .order-details .order-list .order-item .name p {
            color: #FFF;
            font-size: 18px;
            font-weight: 500;
        }

        .order-details .order-list .order-item .quantity {
            min-width: 60px;
        }

        .order-details .order-list .order-item .quantity input {
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.30);
            width: 75px;
            height: 36px;
            background: transparent;
            padding: 0 10px;
            color: #fff;
            outline: none;
            font-family: 'IBM Plex Sans', sans-serif;
        }

        .order-details .order-list .order-item .quantity .arrow {
            position: absolute;
            right: 10px;
            color: rgba(255, 255, 255, 0.50);
            width: 12px;
            transition: all .3s ease;
        }

        .order-details .order-list .order-item .quantity .arrow:hover{
            color: rgba(255, 255, 255, 1);
        }

        .order-details .order-list .order-item .quantity .arrow.arrow-up {
            top: 7px;
        }

        .order-details .order-list .order-item .quantity .arrow.arrow-down {
            top: 16px;
        }

        .order-details .order-list .order-item .price {
            min-width: 100px;
            text-align: right;
        }

        .order-details .order-list .order-item .price p {
            color: #FFF;
            font-size: 20px;
            font-weight: 500;
        }

        .order-details .order-summary {
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;
            flex-direction: column;
            padding-top: 15px;
        }

        .order-details .order-summary .summary-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 350px;
            width: 100%;
            gap: 15px;
        }

        .order-details .order-summary .summary-item:not(:last-child) {
            margin-bottom: 10px;
        }

        .order-details-wrap .total-amount-text,
        .order-details-wrap .total-amount-number {
            color: #FFF;
            font-size: 16px;
            font-weight: 500;
        }

        .order-details .order-summary .summary-item.total {
            border-top: 1px solid rgba(255, 255, 255, 0.30);
            padding-top: 10px;
            margin-top: 10px;
        }

        .order-details .order-summary .summary-item.total p {
            font-size: 18px;
        }

        .woo-form-wrap {
            flex: 0 0 50%;
            min-width: 50%;
        }

        .woo-form-wrap .payment-method-items-wrap {
            padding: 48px;
        }

        .woo-form-wrap .payment-method-items-wrap .title {
            color: #2B2748;
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 20px;
        }

        .woo-form-wrap .payment-method-items-wrap .payment-method-items {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item {
            flex: 1 1 calc(50% - 8px);
        }

        .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item input {
            display: none;
        }

        .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option {
            border-radius: 8px;
            background: #F4F4F8;
            padding: 18px 10px;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
        }

        .woo-form-wrap .payment-method-items input:checked + .payment-option {
            border: 1px solid #6E58F7;
        }

        .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option p {
            font-size: 16px;
            font-weight: 600;
            color: #2B2748;
        }

        .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option span {
            display: inline-block;
        }

        .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option .icon {
            &.paypal-icon,
            &.stripe-icon {
                width: 66px;
            }
    
            &.paystack-icon {
                width: 100px;
            }
        }

        .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option .icon image {
            height: 20px;
        }

        .woo-form-wrap .payment-method-items-wrap .payment-method-items .payment-method-item .payment-option .title {
            color: #2B2748;
            font-size: 16px;
            font-weight: 600;
        }

        .woo-form-wrap .payment-method-items-wrap .form-group {
            position: relative;
        }

        .woo-form-wrap .payment-method-items-wrap .form-group .icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
        }

        .woo-form-wrap .payment-method-items-wrap .form-control {
            border-radius: 4px;
            border: 1px solid #D3DBEE;
            background: #FFF;
            height: 56px;
            padding: 0 20px;
            width: 100%;
            outline: none;
            color: #2B2748;
            font-size: 14px;
            font-weight: 500;
        }

        .woo-form-wrap .payment-method-items-wrap select.form-control {
            appearance: none;
        }

        .woo-form-wrap .payment-method-items-wrap .form-control:focus {
            border: 1px solid #2B2748;
        }

        .woo-form-wrap .payment-method-items-wrap .form-group label {
            position: absolute;
            top: -9px;
            // top: 19px;
            left: 18px;
            background: #fff;
            padding: 0 3px;
            color: #5F667D;
            font-size: 14px;
            font-weight: 500;
            transition: all .3s ease;
            pointer-events: none;
        }

        .woo-form-wrap .payment-method-items-wrap .form-control:focus + label {
            top: -9px;
        }

        .woo-form-wrap .payment-method-items-wrap .form-control:valid + label {
            top: -9px;
        }

        .col-wrap {
            display: flex;
            gap: 16px;
        }

        .col-wrap .span-50 {
            flex: 1 1 calc(50% - 8px);
        }

        .check-item {
            display: flex;
            align-items: center;
            gap: 15px;
            pointer-events: all;
        }

        .check-item input {
            display: none;
        }

        .check-item .checkbox {
            border-radius: 4px;
            border: 2px solid #D3DBEE;
            background: #FFF;
            display: block;
            height: 20px;
            width: 20px;
            cursor: pointer;
            position: relative;
        }

        .check-item .checkbox::before {
            border-radius: 2px;
            background: #6E58F7;
            height: 12px;
            width: 12px;
            opacity: 0;
            transition: all .3s ease;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            content: '';
        }

        .check-item input:checked + .checkbox::before {
            opacity: 1;
        }

        .woo-form-wrap .button {
            border-radius: 8px;
            color: #FFF;
            text-align: center;
            font-size: 18px;
            font-weight: 500;
            width: 100%;
            padding: 15.5px 20px;
            box-shadow: none;
            cursor: pointer;
            background: linear-gradient(228deg, #AF6CFB 27.95%, #7C5ADD 82.65%);
        }

        .payment-method-items-wrap .payment-amount-wrapper {
            margin-bottom: 20px;
        }

        .payment-method-items-wrap .payment-amount-wrapper .label {
            color: #2B2748;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
            display: inline-block;
        }

        .payment-method-items-wrap .payment-amount-wrapper .payment-amounts {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 24px;
            margin-bottom: 20px;
        }

        .payment-method-items-wrap .payment-amount-wrapper .payment-amounts .payment-amount {
            flex: 0 0 calc(20% - 20px);
        }

        .payment-method-items-wrap .payment-amount-wrapper .payment-amounts .payment-amount input {
            display: none;
        }

        .payment-method-items-wrap .payment-amount .text {
            border-radius: 8px;
            background: #F4F4F8;
            padding: 17px 10px;
            cursor: pointer;
            transition: all .3s ease;
            width: 100%;
            display: block;
            text-align: center;
            border: 1px solid transparent;
            color: #2B2748;
            font-size: 18px;
            font-weight: 500;
        }

        .payment-method-items-wrap .payment-amount-wrapper .payment-amounts .payment-amount input:checked + .text {
            border: 1px solid #6E58F7;
            background: #FFF;
            color: #2B2748;
        }

        .payment-method-items-wrap .payment-amount-wrapper .custom-amount input {
            border-radius: 8px;
            border: 1px solid #F4F4F8;
            background: #FFF;
            height: 56px;
            width: 100%;
            padding: 0 20px;
            color: #2B2748;
            font-size: 18px;
            font-weight: 500;
            outline: none;
        }
    }

    .method-specific-options.hidden {
        display: none;
    }

    #split-payment-installment-wrap {
        display: none;
    }
}

@media all and (max-width: 439px) {
    .better-payment {
        .layout-5-donation {
            .donation-form-wrap .payment-type-items-wrap .payment-type-items {
                gap: 16px;
            }

            .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item {
                flex: 0 0 100% !important;
            }
        }
    }
}

@media all and (max-width: 575px) {
    .better-payment {
        .layout-6-woo {
            .order-details .order-list .order-item {
                gap: 10px;
            }

            .order-details .order-list .order-item .price {
                min-width: 72px;
            }
            
            .order-details .order-list .order-item .price p {
                font-size: 16px;
            }
            
            .col-wrap {
                flex-direction: column;
                gap: 0;
            }
            
            .col-wrap .span-50 {
                flex: 1 1 100%;
            }
        }
    }
}

@media all and (max-width: 767px) {
    .better-payment {
        .card-field .icons {
            display: none;
        }

        .layout-4-general {
            .col-wrap {
                display: flex;
                flex-direction: column;
                gap: 0;
            }
            .col-wrap .span-50 {
                flex: 1 1 100%;
            }
    
            .general-form-wrap .payment-method-items {
                gap: 20px;
            }
            .general-form-wrap .payment-method-items .payment-method-item .payment-method-image-wrap {
                padding: 15px 12px;
            }
    
            .general-form-wrap .payment-amount-wrap .payment-amounts {
                gap: 15px;
            }
        }

        .layout-5-donation {
            .col-wrap {
                flex-direction: column;
                gap: 0;
            }

            .col-wrap .span-50 {
                flex: 1 1 100%;
            }

            .donation-form-wrap .payment-type-items-wrap .payment-type-items {
                gap: 16px;
            }

            .donation-form-wrap .payment-type-items-wrap .payment-type-items .payment-type-item {
                flex: 0 0 calc(50% - 8px);
            }

            .donation-form-wrap .payment-amount-wrap .payment-amounts {
                gap: 15px;
            }

            .donation-form-wrap .payment-amount-wrap .payment-amounts .payment-amount {
                flex: 0 0 calc(30% - 20px);
            }
        }

        .layout-6-woo {
            .payment-method-items-wrap .payment-amount-wrapper .payment-amounts {
                gap: 15px;
            }
            
            .order-details .order-list .order-item .quantity input {
                width: 55px;
            }
        }
    }
}

@media all and (max-width: 991px) {
    .better-payment {
        .layout-4-general {
            flex-direction: column;
            padding: 30px;
            gap: 30px;

            .transaction-details-wrap {
                padding: 32px 32px 60px;
                min-width: auto;
                width: 100%;
                flex-basis: auto;
            }
        }

        .layout-5-donation {
            padding: 30px;
            display: block;

            .donation-image-wrap {
                display: none;
            }
        }

        .layout-6-woo {
            display: block;

            .order-details {
                padding: 25px;
            }
            .woo-form-wrap .payment-method-items-wrap {
                padding: 25px;
            }
        }
    }
}

@media all and (max-width: 1199px) {
    .better-payment {
        .layout-4-general {
            .general-form-wrap .payment-method-items .payment-method-item {
                flex: 0 0 calc(50% - 12px);
            }
        }
    }
}

@media all and (max-width: 767px) {
    .better-payment {
        .layout-4-general {
            .general-form-wrap .payment-method-items .payment-method-item {
                flex: 0 0 100%;
            }

            .general-form-wrap .better-payment-field-advanced-layout.field-display-inline {
                width: 100% !important;
                margin: 0;
            }

            .general-form-wrap .payment-amount-wrap .payment-amounts .payment-amount {
                flex: 0 0 calc(50% - 8px);
            }

            .payment-button {
                font-size: 16px;
            }
        }
    }
}