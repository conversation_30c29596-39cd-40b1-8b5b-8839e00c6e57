(function ($) {
  "use strict";

  $(document).on("click", ".bp-refund-buttons-wrap a", function (e) {
    e.preventDefault();
    let $this = $(this);

    let orderId = $this.attr("data-orderid");
    let refundAmount = $this.attr("data-amount");
    let refundType = $this.attr("data-refundtype");

    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, refund it!",
      focusCancel: 1,
    }).then((result) => {
      if (result.value) {
        $.post(betterPaymentPro.ajaxurl, {
          action: "better_payment_pro_transaction_refund",
          orderId: orderId,
          refundAmount: refundAmount,
          refundType: refundType,
          _wpnonce: betterPaymentPro.nonce,
        })
          .done(function (response) {
            toastr.success("Successfully refunded!");

            setTimeout(function () {
              location.reload();
            }, 1500);
          })
          .fail(function () {
            toastr.error("Something went wrong!");
          });
      } else if (result.dismiss == "cancel") {
      } else if (result.dismiss == "esc") {
      }
    });
  });

  $(document).on("click", ".bp-send-receipt-submit", function (e) {
    e.preventDefault();
    let $this = $(this);
    let email = $(
      ".better-payment .bp-send-receipt .email-recipient-field"
    ).val();
    let orderId = $this.attr("data-orderid");

    let isemailValidated = admin_better_email_validation(email);

    if (email == "" || !isemailValidated) {
      toastr.error("Email address is not valid!");
      return false;
    }

    $.post(betterPaymentPro.ajaxurl, {
      action: "better_payment_pro_transaction_send_receipt",
      email: email,
      orderId: orderId,
      _wpnonce: betterPaymentPro.nonce,
    })
      .done(function (response) {
        toastr.success("Email sent successfully!");

        setTimeout(function () {
          location.reload();
        }, 1500);
      })
      .fail(function () {
        toastr.error("Something went wrong!");
      });
  });

  $(document).on("click", ".better-payment-print-receipt-btn", function (e) {
    e.preventDefault();
    let $this = $(this);
    let printContent = $(".better-payment-print-receipt-wrap").html();

    let combined = document.createElement("div");
    combined.innerHTML = printContent;

    let wheight = $(window).height();
    let winPrint = window.open(
      "",
      "",
      "left=50%,top=10%,width=" +
        700 +
        ",height=" +
        wheight +
        ",toolbar=0,scrollbars=0,status=0"
    );
    winPrint.document.write(combined.outerHTML);
    winPrint.document.close();
    winPrint.focus();
    winPrint.print();
    winPrint.close();
  });

  $(document)
    .on(
      "click",
      '.better-payment .bp-license-form-block button[type="submit"][name="license_activate"]',
      function (e) {
        e.preventDefault();
        $(".bp-license-error-msg").hide().text("");
        $(".bp-verification-msg").hide();

        let button = $(this);
        button.text("Activating...");

        $.ajax({
          url: wpdeveloperLicenseManagerConfig.api_url,
          type: "POST",
          data: {
            action: `${wpdeveloperLicenseManagerConfig.action}/license/activate`,
            _nonce: wpdeveloperLicenseManagerConfig.nonce,
            license_key: $(
              `#${wpdeveloperLicenseManagerConfig.action}-license-key`
            ).val(),
          },
          success: function (response) {
            if (response.success) {
              $(`#${wpdeveloperLicenseManagerConfig.action}-license-key`).attr(
                "disabled",
                "disabled"
              );

              if (response.data.license !== "required_otp") {
                $(".bp-activate__license__block")
                  .hide()
                  .siblings(".--deactivation-form")
                  .show();
                $(".--deactivation-form input").val(response.data.license_key);
                $(`#${wpdeveloperLicenseManagerConfig.action}-license-key`)
                  .val("")
                  .removeAttr("disabled")
                  .siblings("button")
                  .removeAttr("disabled")
                  .text("Activate");
                return;
              }

              button
                .text("Verification Required")
                .attr("disabled", "disabled")
                .addClass("--verification-required");
              $(".bp-customer-email").text(response.data.customer_email);
              $(".bp-verification-msg").show();
            } else {
              $(".bp-license-error-msg").text(response.data.message).show();
              button.text("Activate");
            }
          },
          error: function (response) {
            console.log(response);
            button.text("Activate");
          },
        });
      }
    )
    .on("click", '.bp-verification-msg button[type="submit"]', function (e) {
      e.preventDefault();
      $(".bp-license-error-msg").hide().text("");

      let button = $(this);
      button.text("Verifying...");

      $.ajax({
        url: wpdeveloperLicenseManagerConfig.api_url,
        type: "POST",
        data: {
          action: `${wpdeveloperLicenseManagerConfig.action}/license/submit-otp`,
          _nonce: wpdeveloperLicenseManagerConfig.nonce,
          license: $(
            `#${wpdeveloperLicenseManagerConfig.action}-license-key`
          ).val(),
          otp: $(
            `#${wpdeveloperLicenseManagerConfig.action}-license-otp`
          ).val(),
        },
        success: function (response) {
          if (response.success) {
            window.location.reload();
          } else {
            $(".bp-license-error-msg")
              .text(
                "Whoops! Your License Verification Code has expired. Please try again."
              )
              .show();
          }

          button.text("Verify");
        },
        error: function (response) {
          console.log(response);
          button.text("Verify");
        },
      });
    })
    .on(
      "click",
      '.bp-license-form-block button[type="submit"][name="license_deactivate"]',
      function (e) {
        e.preventDefault();

        let button = $(this);
        button.text("Deactivating...");

        $.ajax({
          url: wpdeveloperLicenseManagerConfig.api_url,
          type: "POST",
          data: {
            action: `${wpdeveloperLicenseManagerConfig.action}/license/deactivate`,
            _nonce: wpdeveloperLicenseManagerConfig.nonce,
          },
          success: function (response) {
            if (response.success) {
              window.location.reload();
            } else {
              $(".bp-license-error-msg").text(response.data.message).show();
            }

            button.text("Deactivate");
          },
          error: function (response) {
            console.log(response);
            button.text("Deactivate");
          },
        });
      }
    )
    .on("click", ".bp-otp-resend", function (e) {
      e.preventDefault();

      $.ajax({
        url: wpdeveloperLicenseManagerConfig.api_url,
        type: "POST",
        data: {
          action: `${wpdeveloperLicenseManagerConfig.action}/license/resend-otp`,
          _nonce: wpdeveloperLicenseManagerConfig.nonce,
          license: $(
            `#${wpdeveloperLicenseManagerConfig.action}-license-key`
          ).val(),
        },
        success: function (response) {
          if (response.success) {
            $(".bp-license-error-msg")
              .text(
                "License Verification Code has been sent to your email address. Please check your email to find the code."
              )
              .addClass("notice-message")
              .show();
            setTimeout(function () {
              $(".bp-license-error-msg")
                .removeClass("notice-message")
                .text("")
                .hide();
            }, 3000);
          } else {
            $(".bp-license-error-msg").text(response.data.message).show();
          }
        },
        error: function (response) {
          console.log(response);
        },
      });
    });

  function toasterOptions() {
    toastr.options = {
      timeOut: "2000",
      toastClass: "font-size-md",
      positionClass: "toast-top-center",
      showMethod: "slideDown",
      hideMethod: "slideUp",
    };
  }

  function admin_better_email_validation(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }

  toasterOptions();
})(jQuery);
