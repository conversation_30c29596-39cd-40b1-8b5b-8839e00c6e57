// Page: Analytics Starts
(function ($) {
  "use strict";

  $(document).ready(function () {
    $(document).on(
      "change",
      ".better-payment .analytics_time_period-custom-range",
      function (e) {
        let selectedValues = $(this).is(":checked");
        if (selectedValues === true) {
          $(".better-payment .modal.analytics-custom-time-period").addClass(
            "is-active"
          );
        }
      }
    );

    // Chart JS: Starts
    setTimeout(function () {
      let analyticsChartConfigData = {};

      if (typeof betterPaymentProAnalytics.analytics != "undefined") {
        analyticsChartConfigData = getAnalyticsChartConfigData(
          betterPaymentProAnalytics.analytics
        );
      }

      const labels =
        typeof analyticsChartConfigData.labels != "undefined"
          ? analyticsChartConfigData.labels
          : [];
      const datasets =
        typeof analyticsChartConfigData.datasets != "undefined"
          ? analyticsChartConfigData.datasets
          : [];

      const data = {
        labels: labels,
        datasets: datasets,
      };

      const config = {
        type: "line",
        data: data,
        options: {
          maintainAspectRatio: !1,
          scaleShowHorizontalLines: !0,
          scaleShowVerticalLines: !1,
          bezierCurveTension: 0.3,
          responsive: true,
          spanGaps: false,
          tooltips: {
            mode: "nearest",
            position: "nearest",
            intersect: false,
          },
          hover: {
            position: "nearest",
            intersect: false,
          },
          scales: {
            y: {
              ticks: {
                // Include a dollar sign in the ticks
                callback: function (value, index, values) {
                  return "$" + value;
                },
              },
            },
          },
        },
      };

      const myChart = new Chart(document.getElementById("myChart"), config);
    }, 1000); // Run after 1000ms

    $(document).on(
      "click",
      ".better-payment .bp-analytics-filter-submit",
      function (e) {
        e.preventDefault();
        $(".analytics-chart-wrap").css("opacity", 0.5);

        let $this = $(this);
        let form = $("#better-payment-analytics-form");

        $.post(betterPaymentPro.ajaxurl, {
          action: "better_payment_pro_analytics_filter",
          data: form.serialize(),
          _wpnonce: betterPaymentPro.nonce,
        })
          .done(function (response) {
            if (
              typeof response.data !== "undefined" &&
              typeof response.data.analytics !== "undefined"
            ) {
              // $(".analytics-chart-wrap").html(
              //   response.data.analytics_filter_content
              // );
              let analyticsData = response.data.analytics;
              let analyticsChartConfigDataAjax =
                getAnalyticsChartConfigData(analyticsData);

              myChart.data = {
                labels:
                  typeof analyticsChartConfigDataAjax.labels != "undefined"
                    ? analyticsChartConfigDataAjax.labels
                    : [],
                datasets:
                  typeof analyticsChartConfigDataAjax.datasets != "undefined"
                    ? analyticsChartConfigDataAjax.datasets
                    : [],
              };
              myChart.update();

              $(".analytics-chart-wrap").css("opacity", 1);
              $(".analytics-total-transaction").attr(
                "title",
                "Filtered Total Transactions: " +
                  analyticsData.all_transactions_total
              );
              $(".analytics-completed-transaction").attr(
                "title",
                "Filtered Completed Transactions: " +
                  analyticsData.completed_transactions_total
              );
              $(".analytics-incomplete-transaction").attr(
                "title",
                "Filtered Incomplete Transactions: " +
                  analyticsData.incomplete_transactions_total
              );
              $(".analytics-refund-transaction").attr(
                "title",
                "Filtered Refund Transactions: " +
                  analyticsData.refund_transactions_total
              );
            }
          })
          .fail(function () {
            $(".analytics-chart-wrap").css("opacity", 1);
            toastr.error("Something went wrong!");
          });
      }
    );
    // Chart JS: Ends

    $(document).on("click", ".analytics-select-custom-button", function (e) {
      e.preventDefault();
      let $this = $(this);
      let dataTarget = $this.attr("data-target");
      $(dataTarget).toggleClass("is-hidden");

      if (dataTarget === ".analytics_transaction_types-dropdown") {
        if (!$(".analytics_time_period-dropdown").hasClass("is-hidden")) {
          $(".analytics_time_period-dropdown").addClass("is-hidden");
        }
      } else {
        if (!$(".analytics_transaction_types-dropdown").hasClass("is-hidden")) {
          $(".analytics_transaction_types-dropdown").addClass("is-hidden");
        }
      }
    });

    $(document).on("click", "body", function (e) {
      let filter_button_container = $(".analytics-select-custom-button-wrap");
      if (
        !filter_button_container.is(e.target) &&
        filter_button_container.has(e.target).length === 0
      ) {
        if (!$(".analytics_transaction_types-dropdown").hasClass("is-hidden")) {
          $(".analytics_transaction_types-dropdown").addClass("is-hidden");
        }

        if (!$(".analytics_time_period-dropdown").hasClass("is-hidden")) {
          $(".analytics_time_period-dropdown").addClass("is-hidden");
        }
      }
    });

    function getAnalyticsChartConfigData(analyticsData) {
      let datasets = [];
      let labels = [];

      let allTransactionDataset = {
        label: "Total Transaction",
        data: analyticsData.all_transactions,
        borderColor: "#735EF8",
        backgroundColor: "#735EF8",
      };

      let completedTransactionDataset = {
        label: "Completed Transaction",
        data: analyticsData.completed_transactions,
        borderColor: "#0ECA86",
        backgroundColor: "#0ECA86",
      };

      let incompleteTransactionDataset = {
        label: "Incomplete Transaction",
        data: analyticsData.incomplete_transactions,
        borderColor: "#FFDA15",
        backgroundColor: "#FFDA15",
      };

      let refundedTransactionDataset = {
        label: "Refunded Transaction",
        data: analyticsData.refund_transactions,
        borderColor: "#FF0202",
        backgroundColor: "#FF0202",
      };

      if (
        typeof analyticsData.all_transactions_show != "undefined" &&
        analyticsData.all_transactions_show == 1
      ) {
        datasets.push(allTransactionDataset);
      }

      if (
        typeof analyticsData.completed_transactions_show != "undefined" &&
        analyticsData.completed_transactions_show == 1
      ) {
        datasets.push(completedTransactionDataset);
      }

      if (
        typeof analyticsData.incomplete_transactions_show != "undefined" &&
        analyticsData.incomplete_transactions_show == 1
      ) {
        datasets.push(incompleteTransactionDataset);
      }

      if (
        typeof analyticsData.refund_transactions_show != "undefined" &&
        analyticsData.refund_transactions_show == 1
      ) {
        datasets.push(refundedTransactionDataset);
      }

      if (
        typeof analyticsData.show_all_four_stats != "undefined" &&
        analyticsData.show_all_four_stats == 1
      ) {
        datasets = [
          allTransactionDataset,
          completedTransactionDataset,
          incompleteTransactionDataset,
          refundedTransactionDataset,
        ];
      }

      if (typeof analyticsData.payment_date_period_index != "undefined") {
        labels = analyticsData.payment_date_period_index;
      }

      return { datasets: datasets, labels: labels };
    }

    $(
      ".analytics-select-custom-button-dropdown input[name='analytics_transaction_types[]']"
    ).change(function () {
      if (this.checked) {
        if (this.value !== "all") {
          $(
            ".analytics_transaction_types-dropdown .analytics_transaction_types-all"
          ).prop("checked", false);
          $(".analytics_transaction_types-button-text").html("Transactions");
        } else if (this.value === "all") {
          $(
            ".analytics_transaction_types-dropdown input[name='analytics_transaction_types[]']"
          ).prop("checked", false);
          $(
            ".analytics_transaction_types-dropdown .analytics_transaction_types-all"
          ).prop("checked", true);
          $(".analytics_transaction_types-button-text").html(
            "All Transactions"
          );
        }
      } else {
        //If four checkboxes are unchecked then check All Transactions checkbox
        if (
          !$(
            ".analytics_transaction_types-dropdown input[name='analytics_transaction_types[]']:checked"
          ).length
        ) {
          $(
            ".analytics_transaction_types-dropdown .analytics_transaction_types-all"
          ).prop("checked", true);
          $(".analytics_transaction_types-button-text").html(
            "All Transactions"
          );
        } else {
          $(".analytics_transaction_types-button-text").html("Transactions");
        }
      }
    });

    $(
      ".analytics-select-custom-button-dropdown input[name='analytics_time_period[]']"
    ).change(function () {
      if (this.checked) {
        //Uncheck other checkboxes but this one
        $(
          ".analytics_time_period-dropdown input[name='analytics_time_period[]']"
        )
          .not(this)
          .prop("checked", false);

        updateDropdownText(this.value);
      } else {
        //If four checkboxes are unchecked then check Last Week checkbox
        if (
          !$(
            ".analytics_time_period-dropdown input[name='analytics_time_period[]']:checked"
          ).length
        ) {
          $(".analytics_time_period-dropdown .analytics_time_period-week").prop(
            "checked",
            true
          );
          updateDropdownText("week");
        }
      }
    });

    function capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    }

    function updateDropdownText(text, inputName = "analytics_time_period") {
      if (inputName === "analytics_time_period") {
        if (text !== "custom") {
          $(".analytics_time_period-button-text").html(
            "Last " + capitalizeFirstLetter(text)
          );
        } else {
          $(".analytics_time_period-button-text").html("Custom");
        }
      }

      // if(inputName === 'analytics_transaction_types'){
      //     if(text !== 'all'){
      //         //Fetch all checked checkboxes and update the dropdown text
      //         let checkedCheckboxes = $(".analytics_transaction_types-dropdown input[name='analytics_transaction_types[]']:checked");
      //         let dropdownText = '';
      //         for(let i = 0; i < checkedCheckboxes.length; i++){
      //             //Remove -transaction from the text
      //             let optionText = checkedCheckboxes[i].value.replace('-transaction', '');

      //             if(i === checkedCheckboxes.length - 1){
      //                 dropdownText += capitalizeFirstLetter(optionText) + ' Transactions';
      //             } else {
      //                 dropdownText += capitalizeFirstLetter(optionText) + ', ';
      //             }
      //         }
      //         $(".analytics_transaction_types-button-text").html(dropdownText);
      //     }else {
      //         $(".analytics_transaction_types-button-text").html("All Transactions");
      //     }
      // }
    }
  });
})(jQuery);
// Page: Analytics Ends
