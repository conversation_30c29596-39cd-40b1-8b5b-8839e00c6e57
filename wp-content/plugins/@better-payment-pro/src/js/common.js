;(function($) {
    
    $(document).ready(function() {
        // $('.better-payment .select2').select2({
        //     placeholder: 'Select an option',
        //     allowClear: true,
        //     extraClass: ''
        // });

        // $('.better-payment .better-payment-analytics .select2').select2({
        //     placeholder: 'Select an option',
        //     allowClear: true,
        //     extraClass: 'analytic-reports-select2'
        // });

        $(document).on('change', '.better-payment .order-details-wrap .quantity input', function(e){
            let quantity = $(this).val();
            let price = parseFloat($(this).data('price'));

            let total_price = quantity * price;
            let element = $(this).closest('.order-item').find('.order-item-price-amount');
            $(element).html(total_price.toFixed(2));
            
            let overall_total = 0;
            $('.better-payment .order-item').each(function() {
                let item_total = parseFloat($(this).find('.order-item-price-amount').text());
                overall_total += item_total;
            });

            // $('.summary-item-subtotal-amount').html( total_price );
            // $('.better-payment .payment-amount-wrap input').val( total_price );
            $('.better-payment .summary-item-subtotal-amount').html(overall_total.toFixed(2));
            $('.better-payment .payment-amount-wrap input').val( overall_total );

        });
        
        $(document).on('change', '.better-payment .bp-custom-payment-amount-quantity', function(e){
            let quantity = $(this).val();
            let price = $('.bp-custom-payment-amount').val();

            let total_price = quantity * price;
            $('.bp-transaction-details-amount-text').html( total_price );
        });

        $(document).on('change', '.better-payment .split-payment-text-wrap select', function (e) {
            let selectedOption = $(this).find('option:selected');
            let noteWrap = $('.better-payment .split-payment-installment-note-wrap');
            let noteAmount = $('.better-payment .split-payment-installment-note-amount');

            let unitAmount = selectedOption.data('unit-amount') ?? 0;
            let interval = selectedOption.data('interval') ?? '';
            let currencyLeft = noteAmount.data('currency-left') ?? '';
            let currencyRight = noteAmount.data('currency-right') ?? '';
            let separator = interval ? '/' : '';
            
            if ( unitAmount ) {
                noteAmount.text( `${currencyLeft}${unitAmount}${currencyRight} ${separator} ${interval}` );
                noteWrap.removeClass('is-hidden');
            } else {
                noteWrap.addClass('is-hidden');
            }
        });
        
        // BP User Dashboard
        $(document).on(
            "click",
            ".bp-user-dashboard-subscription-cancel",
            function(e) {
                e.preventDefault();
                bpUserDashboardSubscriptionCancel(this);
            }
        );
  
        function bpUserDashboardSubscriptionCancel(button) {
            let subscriptionCancelButton = $(button),
                nonce = betterPaymentPro.nonce;

            let subscription_id = subscriptionCancelButton.attr('data-subscriptionid');
  
            if (!subscription_id) {
                return false;
            }

            $.ajax({
                type: "POST",
                url: betterPaymentPro.ajaxurl,
                data: {
                    action: "better_payment_user_dashboard_subscription_cancel",
                    nonce: nonce,
                    subscription_id: subscription_id,
                },
                beforeSend: function() {
                    subscriptionCancelButton.addClass("is-loading");
                },
                success: function(res) {
                    subscriptionCancelButton.removeClass("is-loading");
                    if ( res.success ) {
                        let status_button = $(button).closest('.better-payment-user-dashboard-table-body').find('.bp-user-dashboard-subscription-status button');
                        status_button.html('Inactive').addClass('inactive').removeClass('active');
                        toastr.success(res.data.message ?? 'Successfully unsubscribed!');
                    } else {
                        toastr.error(res.data.message ?? 'Something went wrong!');
                    }
                },
            });
        }
        
        $('.better-payment input[name="payment_type"]').on('change', function () {
            var selectedValue = $(this).val();
            var $dropdown = $('#split_payment_installment');
    
            if ($dropdown.length) {
                $dropdown.val(selectedValue);
            }
        });
        
        $(document).on('click', '.better-payment .payment-type-items', function(e) {
            const $this = $(this);
            const priceId = $this.data('price_id');
            const amount = $this.data('amount');
            
            if (!priceId || !amount) {
                console.error('Invalid price ID or amount');
                return;
            }
            
            // Update the amount input
            $('.better-payment .field-primary_payment_amount input[name="primary_payment_amount"]').val(amount);
    
            // Update the hidden price ID input
            $('.better-payment input[name="better_payment_recurring_price_id"]').val(priceId);
        });
        
    });
})(jQuery);