<?php return array(
    'root' => array(
        'pretty_version' => '65180.x-dev',
        'version' => '65180.9999999.9999999.9999999-dev',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => 'b62993b43a46fcd5501c064d2e51eba9e9fb03ca',
        'name' => 'wpdevteam/betterlinks',
        'dev' => true,
    ),
    'versions' => array(
        'coenjacobs/mozart' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'type' => 'library',
            'install_path' => __DIR__ . '/../coenjacobs/mozart',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'reference' => '175fbdc382c09a8c57a7ee8e81bad92eef4adeac',
            'dev_requirement' => true,
        ),
        'league/flysystem' => array(
            'pretty_version' => '3.x-dev',
            'version' => '3.9999999.9999999.9999999-dev',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'reference' => '22af5a392e46bdf7e48fc74f711a02a1e09ee9cf',
            'dev_requirement' => true,
        ),
        'league/flysystem-local' => array(
            'pretty_version' => '3.x-dev',
            'version' => '3.9999999.9999999.9999999-dev',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-local',
            'aliases' => array(),
            'reference' => 'e0e8d52ce4b2ed154148453d321e97c8e931bd27',
            'dev_requirement' => true,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.15.0',
            'version' => '1.15.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'reference' => 'ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301',
            'dev_requirement' => true,
        ),
        'psr/container' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(
                0 => '2.0.x-dev',
            ),
            'reference' => '707984727bd5b2b670e59559d3ed2500240cf875',
            'dev_requirement' => true,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => true,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'stripe/stripe-php' => array(
            'pretty_version' => 'v7.128.0',
            'version' => '*********',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stripe/stripe-php',
            'aliases' => array(),
            'reference' => 'c704949c49b72985c76cc61063aa26fefbd2724e',
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => '7.2.x-dev',
            'version' => '7.2.9999999.9999999-dev',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'reference' => 'cc2dd55c49e04b10cec4aa6e47e099a2d1656845',
            'dev_requirement' => true,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(
                0 => '3.5.x-dev',
            ),
            'reference' => '0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1',
            'dev_requirement' => true,
        ),
        'symfony/finder' => array(
            'pretty_version' => '7.2.x-dev',
            'version' => '7.2.9999999.9999999-dev',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'reference' => '0470b8dc10ca08d23aeaabbed5b961c928e4d89d',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => '1.x-dev',
            'version' => '1.9999999.9999999.9999999-dev',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'reference' => '0424dff1c58f028c451efff2045f5d92410bd540',
            'dev_requirement' => true,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => '1.x-dev',
            'version' => '1.9999999.9999999.9999999-dev',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'reference' => '64647a7c30b2283f5d49b874d84a18fc22054b7a',
            'dev_requirement' => true,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => '1.x-dev',
            'version' => '1.9999999.9999999.9999999-dev',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'reference' => 'a95281b0be0d9ab48050ebd988b967875cdb9fdb',
            'dev_requirement' => true,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => '1.x-dev',
            'version' => '1.9999999.9999999.9999999-dev',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'reference' => '8740a072b86292957feb42703edde77fcfca84fb',
            'dev_requirement' => true,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(
                0 => '3.5.x-dev',
            ),
            'reference' => 'bd1d9e59a81d8fa4acdcea3f617c581f7475a80f',
            'dev_requirement' => true,
        ),
        'symfony/string' => array(
            'pretty_version' => '7.2.x-dev',
            'version' => '7.2.9999999.9999999-dev',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'reference' => 'c38cbd6dcf2a45bcfbd79fb89d6bd08f7afdc7dc',
            'dev_requirement' => true,
        ),
        'wpdeveloper/licensing' => array(
            'pretty_version' => 'v3.x-dev',
            'version' => '3.9999999.9999999.9999999-dev',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../wpdeveloper/licensing',
            'aliases' => array(),
            'reference' => '937b873ce84ed656783edcdceebddf6ff998b3f8',
            'dev_requirement' => false,
        ),
        'wpdevteam/betterlinks' => array(
            'pretty_version' => '65180.x-dev',
            'version' => '65180.9999999.9999999.9999999-dev',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => 'b62993b43a46fcd5501c064d2e51eba9e9fb03ca',
            'dev_requirement' => false,
        ),
    ),
);
