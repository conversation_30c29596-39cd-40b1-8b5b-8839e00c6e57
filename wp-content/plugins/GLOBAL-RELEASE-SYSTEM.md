# 🌍 Global WordPress Plugin Release System

A universal release automation system that works across multiple WordPress development projects and environments.

## 🚀 Quick Start

### Single Plugin Release
```bash
./prepare-release-global essential-addons-dev/better-payment 1.3.3
./prepare-release-global nhrrob-dev/nhrrob-movies 2.1.0
```

### Multi-Plugin Release (Release Groups)
```bash
./prepare-release-global better-payment-suite 1.3.3      # Both Better Payment plugins
./prepare-release-global nhrrob-suite 2.0.0              # All NHRROB plugins
./prepare-release-global essential-addons-suite 6.4.0    # Both Essential Addons plugins
```

## 📁 System Architecture

```
wp-content/plugins/
├── prepare-release-global          # Simple global release script (recommended)
├── global-release-manager         # Advanced global release manager
├── global-release-config.json     # Configuration for all projects & plugins
├── GLOBAL-RELEASE-SYSTEM.md       # This documentation
└── [existing plugin directories]
```

## 🎯 Configured Projects & Plugins

### 📁 essential-addons-dev Project
| Plugin Slug | Plugin Name | Path |
|-------------|-------------|------|
| `better-payment` | Better Payment | `/Users/<USER>/Sites/essential-addons-dev/wp-content/plugins/better-payment` |
| `better-payment-pro` | Better Payment Pro | `/Users/<USER>/Sites/essential-addons-dev/wp-content/plugins/@better-payment-pro` |
| `essential-addons-elementor` | Essential Addons for Elementor | `/Users/<USER>/Sites/essential-addons-dev/wp-content/plugins/essential-addons-elementor` |
| `essential-addons-for-elementor-lite` | Essential Addons for Elementor Lite | `/Users/<USER>/Sites/essential-addons-dev/wp-content/plugins/essential-addons-for-elementor-lite` |

### 📁 nhrrob-dev Project
| Plugin Slug | Plugin Name | Path |
|-------------|-------------|------|
| `nhrrob-movies` | NHRROB Movies | `/Users/<USER>/Sites/nhrrob-dev/wp-content/plugins/@nhrrob-movies` |
| `nhrrob-core-contributions` | NHRROB Core Contributions | `/Users/<USER>/Sites/nhrrob-dev/wp-content/plugins/nhrrob-core-contributions` |
| `nhrrob-options-table-manager` | NHRROB Options Table Manager | `/Users/<USER>/Sites/nhrrob-dev/wp-content/plugins/nhrrob-options-table-manager` |

## 📦 Release Groups

| Group Slug | Project | Plugins Included |
|------------|---------|------------------|
| `better-payment-suite` | essential-addons-dev | better-payment, better-payment-pro |
| `essential-addons-suite` | essential-addons-dev | essential-addons-elementor, essential-addons-for-elementor-lite |
| `nhrrob-suite` | nhrrob-dev | nhrrob-movies, nhrrob-core-contributions, nhrrob-options-table-manager |
| `all-essential-addons` | essential-addons-dev | All essential-addons-dev plugins |
| `all-nhrrob` | nhrrob-dev | All nhrrob-dev plugins |

## 🛠️ Scripts

### 1. `prepare-release-global` (Recommended)
**Simple one-command release for any project/plugin**

```bash
./prepare-release-global <project>/<plugin> <version>
./prepare-release-global <release-group> <version>
```

**Examples:**
```bash
# Single plugin releases
./prepare-release-global essential-addons-dev/better-payment 1.3.3
./prepare-release-global nhrrob-dev/nhrrob-movies 2.1.0

# Group releases
./prepare-release-global better-payment-suite 1.3.3
./prepare-release-global nhrrob-suite 2.0.0
```

### 2. `global-release-manager` (Advanced)
**Full-featured global release manager with options**

```bash
./global-release-manager <project>/<plugin> <version> [options]
./global-release-manager <release-group> <version> [options]
```

**Options:**
- `--auto-changelog` - Auto-generate changelog (default: true)
- `--auto-merge` - Auto-merge PR after creation
- `--create-tag` - Create git tag after merge
- `--dry-run` - Show what would be done without making changes

**Examples:**
```bash
# Basic releases
./global-release-manager essential-addons-dev/better-payment 1.3.3
./global-release-manager nhrrob-dev/nhrrob-movies 2.1.0

# Advanced options
./global-release-manager better-payment-suite 1.3.3 --auto-merge --create-tag
./global-release-manager nhrrob-suite 2.0.0 --dry-run
```

## 🤖 AI Assistant Integration

You can now tell your AI assistant:

### Single Plugin Releases
> **"Prepare release for essential-addons-dev/better-payment 1.3.4"**
> **"Prepare release for nhrrob-dev/nhrrob-movies 2.1.0"**

### Multi-Plugin Releases
> **"Prepare release for better-payment-suite 1.3.4"**
> **"Prepare release for nhrrob-suite 2.0.0"**

The assistant will automatically run:
```bash
cd /Users/<USER>/Sites/essential-addons-dev/wp-content/plugins
./prepare-release-global <target> <version>
```

## 🔄 Workflow Examples

### Single Plugin Workflow
1. **Prepare release:**
   ```bash
   ./prepare-release-global essential-addons-dev/better-payment 1.3.3
   ```
2. **Review PR** on GitHub
3. **Merge PR** when ready
4. **Deploy to WordPress.org**

### Cross-Project Workflow
1. **Release Better Payment plugins:**
   ```bash
   ./prepare-release-global better-payment-suite 1.3.3
   ```
2. **Release NHRROB plugins:**
   ```bash
   ./prepare-release-global nhrrob-suite 2.0.0
   ```
3. **Review all PRs** across different repositories
4. **Merge and deploy**

### Testing Workflow
1. **Test what would happen:**
   ```bash
   ./global-release-manager nhrrob-dev/nhrrob-movies 2.1.0 --dry-run
   ```
2. **Run actual release:**
   ```bash
   ./prepare-release-global nhrrob-dev/nhrrob-movies 2.1.0
   ```

## ⚙️ Configuration

### Adding a New Project
Edit `global-release-config.json`:

```json
{
  "projects": {
    "your-new-project": {
      "name": "Your New Project",
      "path": "/Users/<USER>/Sites/your-new-project",
      "plugins_path": "wp-content/plugins",
      "plugins": {
        "your-plugin": {
          "name": "Your Plugin Name",
          "path": "your-plugin-directory",
          "main_file": "your-plugin.php",
          "readme_file": "readme.txt",
          "repository": {
            "owner": "your-github-username",
            "name": "your-plugin-repo",
            "dev_branch": "dev",
            "main_branch": "master"
          }
        }
      }
    }
  }
}
```

### Adding a New Plugin to Existing Project
Add to the `plugins` section of an existing project:

```json
"your-new-plugin": {
  "name": "Your New Plugin",
  "path": "your-new-plugin-directory",
  "main_file": "your-new-plugin.php",
  "readme_file": "readme.txt",
  "repository": {
    "owner": "your-github-username",
    "name": "your-new-plugin-repo",
    "dev_branch": "dev",
    "main_branch": "main"
  }
}
```

### Creating Release Groups
Add to the `release_groups` section:

```json
"your-plugin-suite": {
  "project": "your-project-name",
  "plugins": ["plugin1", "plugin2", "plugin3"]
}
```

## 🛡️ Safety Features

- **Project validation** - Checks if project paths exist
- **Plugin validation** - Verifies plugin directories and files
- **Version validation** - Ensures semantic versioning format
- **Git validation** - Confirms directories are git repositories
- **Branch checking** - Warns if not on correct branch
- **Cross-project support** - Handles different repository structures
- **Confirmation prompts** - Asks before making changes
- **Dry run mode** - Test without making actual changes

## 🔧 What the System Does

### Automatic Detection
- **Project paths** - Automatically navigates to correct project directories
- **Plugin structures** - Adapts to different plugin file patterns
- **Repository settings** - Uses project-specific git branches and remotes
- **Version patterns** - Handles various version declaration formats

### Version Updates
- Updates version in plugin headers
- Updates version constants in plugin files
- Updates stable tag in readme.txt files
- Handles different date formats and changelog styles

### Git Operations
- Switches to correct project directories
- Commits version and changelog changes
- Pushes to appropriate dev branches
- Creates pull requests to correct main branches
- Supports different repository owners and naming conventions

## 🚨 Troubleshooting

### Project Path Not Found
Check the `path` in `global-release-config.json` matches your actual project directory.

### Plugin Directory Not Found
Verify the `path` under the plugin configuration matches the actual plugin directory name.

### GitHub CLI Issues
```bash
gh auth login
```

### Permission Issues
```bash
chmod +x prepare-release-global
chmod +x global-release-manager
```

### Wrong Branch
The system will prompt to switch to the correct dev branch automatically.

## 🎯 Benefits

1. **Universal** - Works across multiple projects from one location
2. **Flexible** - Single plugin or multi-plugin releases
3. **Consistent** - Same process regardless of project or plugin
4. **Safe** - Built-in validations and confirmations
5. **Scalable** - Easy to add new projects and plugins
6. **Intelligent** - Auto-detects project structures and requirements

## 📋 Usage Summary

**For single plugin releases across projects:**
```bash
./prepare-release-global <project>/<plugin> <version>
```

**For multi-plugin releases within projects:**
```bash
./prepare-release-global <release-group> <version>
```

**For testing across projects:**
```bash
./global-release-manager <project>/<plugin> <version> --dry-run
```

This global system allows you to manage releases for all your WordPress plugins across multiple development environments from a single command! 🌍🚀
