# Multi-Plugin Release Automation System

This system allows you to manage releases for multiple WordPress plugins from a single location.

## 🚀 Quick Start

### Single Plugin Release
```bash
./prepare-release better-payment 1.3.3
./prepare-release essential-addons-elementor 6.4.0
```

### Multiple Plugin Release (Release Groups)
```bash
./prepare-release better-payment-suite 1.3.3      # Both Better Payment plugins
./prepare-release essential-addons-suite 6.4.0    # Both Essential Addons plugins
./prepare-release all-plugins 1.0.0               # All plugins (use with caution)
```

## 📁 File Structure

```
wp-content/plugins/
├── prepare-release              # Simple release script (recommended)
├── release-manager             # Advanced release manager
├── multi-release-config.json   # Configuration for all plugins
├── MULTI-PLUGIN-RELEASE.md     # This documentation
├── better-payment/             # Plugin directory
├── @better-payment-pro/        # Plugin directory
├── essential-addons-elementor/ # Plugin directory
└── essential-addons-for-elementor-lite/ # Plugin directory
```

## 🎯 Available Plugins

| Plugin Slug | Plugin Name | Repository |
|-------------|-------------|------------|
| `better-payment` | Better Payment | WPDevelopers/better-payment |
| `better-payment-pro` | Better Payment Pro | WPDevelopers/better-payment-pro |
| `essential-addons-elementor` | Essential Addons for Elementor | WPDevelopers/essential-addons-elementor |
| `essential-addons-for-elementor-lite` | Essential Addons for Elementor Lite | WPDevelopers/essential-addons-for-elementor-lite |

## 📦 Release Groups

| Group Slug | Plugins Included |
|------------|------------------|
| `better-payment-suite` | better-payment, better-payment-pro |
| `essential-addons-suite` | essential-addons-elementor, essential-addons-for-elementor-lite |
| `all-plugins` | All configured plugins |

## 🛠️ Scripts

### 1. `prepare-release` (Recommended)
**Simple one-command release for any plugin**

```bash
./prepare-release <plugin-slug> <version>
```

**Examples:**
```bash
./prepare-release better-payment 1.3.3
./prepare-release essential-addons-elementor 6.4.0
./prepare-release better-payment-suite 1.3.3
```

### 2. `release-manager` (Advanced)
**Full-featured release manager with options**

```bash
./release-manager <plugin-slug> <version> [options]
```

**Options:**
- `--auto-changelog` - Auto-generate changelog (default: true)
- `--auto-merge` - Auto-merge PR after creation
- `--create-tag` - Create git tag after merge
- `--dry-run` - Show what would be done without making changes

**Examples:**
```bash
# Basic release
./release-manager better-payment 1.3.3

# Full automation
./release-manager better-payment 1.3.3 --auto-merge --create-tag

# Test run
./release-manager better-payment 1.3.3 --dry-run
```

## ⚙️ Configuration

Edit `multi-release-config.json` to:
- Add new plugins
- Modify repository settings
- Create new release groups
- Change default behaviors

### Adding a New Plugin

```json
{
  "plugins": {
    "your-plugin-slug": {
      "name": "Your Plugin Name",
      "path": "your-plugin-directory",
      "main_file": "your-plugin.php",
      "readme_file": "readme.txt",
      "repository": {
        "owner": "YourGitHubUsername",
        "name": "your-plugin-repo",
        "dev_branch": "dev",
        "main_branch": "master"
      },
      "has_git": true,
      "auto_changelog": true
    }
  }
}
```

## 🤖 AI Assistant Integration

You can now tell your AI assistant:

> **"Prepare release for better-payment 1.3.4"**

And the assistant will run:
```bash
cd /Users/<USER>/Sites/essential-addons-dev/wp-content/plugins
./prepare-release better-payment 1.3.4
```

Or for multiple plugins:

> **"Prepare release for better-payment-suite 1.3.4"**

## 🔄 Workflow Examples

### Single Plugin Workflow
1. **Prepare release:**
   ```bash
   ./prepare-release better-payment 1.3.3
   ```
2. **Review PR** on GitHub
3. **Merge PR** when ready
4. **Deploy to WordPress.org**

### Multi-Plugin Workflow
1. **Prepare releases for suite:**
   ```bash
   ./prepare-release better-payment-suite 1.3.3
   ```
2. **Review all PRs** on GitHub
3. **Merge PRs** when ready
4. **Deploy all to WordPress.org**

### Testing Workflow
1. **Test what would happen:**
   ```bash
   ./release-manager better-payment 1.3.3 --dry-run
   ```
2. **Run actual release:**
   ```bash
   ./prepare-release better-payment 1.3.3
   ```

## 🛡️ Safety Features

- **Plugin validation** - Checks if plugin exists in config
- **Version validation** - Ensures semantic versioning format
- **Directory validation** - Verifies plugin directories exist
- **Git validation** - Ensures directories are git repositories
- **Branch checking** - Warns if not on correct branch
- **Confirmation prompts** - Asks before making changes
- **Dry run mode** - Test without making actual changes

## 🔧 What Each Script Does

### Version Updates
- Updates version in plugin header
- Updates version constants in plugin files
- Updates stable tag in readme.txt

### Changelog Generation
- Analyzes recent git commits since last tag
- Categorizes changes (Added, Fixed, Improved)
- Formats according to WordPress standards
- Adds current date automatically

### Git Operations
- Commits version and changelog changes
- Pushes to dev branch
- Creates pull request to master branch
- Optionally merges PR and creates tags

## 🚨 Troubleshooting

### Plugin Not Found
```bash
# List available plugins
./prepare-release --help
```

### GitHub CLI Not Authenticated
```bash
gh auth login
```

### Not in Correct Directory
```bash
cd /Users/<USER>/Sites/essential-addons-dev/wp-content/plugins
```

### Permission Denied
```bash
chmod +x prepare-release
chmod +x release-manager
```

### Plugin Directory Not Found
Check the `path` in `multi-release-config.json` matches the actual directory name.

## 📋 Supported Plugin Structures

The system supports plugins with these file patterns:

**Version in Plugin Header:**
```php
/**
 * Plugin Name: Your Plugin
 * Version: 1.3.3
 */
```

**Version as Constant:**
```php
const version = '1.3.3';
```

**Readme.txt Stable Tag:**
```
Stable tag: 1.3.3
```

## 🎯 Benefits

1. **Consistency** - Same release process for all plugins
2. **Automation** - Reduces manual work and errors
3. **Scalability** - Easy to add new plugins
4. **Flexibility** - Single or multi-plugin releases
5. **Safety** - Built-in validations and confirmations
6. **Integration** - Works with AI assistants

## 🔮 Future Enhancements

- WordPress.org deployment automation
- Slack/Discord notifications
- Release notes generation
- Automated testing integration
- Version dependency management

## 📞 Usage Summary

**For single plugin releases:**
```bash
./prepare-release <plugin-slug> <version>
```

**For multiple plugin releases:**
```bash
./prepare-release <release-group> <version>
```

**For testing:**
```bash
./release-manager <plugin-slug> <version> --dry-run
```

This system transforms your manual release process into a simple, automated workflow! 🚀
