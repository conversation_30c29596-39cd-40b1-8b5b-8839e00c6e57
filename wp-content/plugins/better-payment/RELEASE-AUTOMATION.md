# Better Payment Release Automation

This directory contains automated scripts to streamline the release process for Better Payment plugin.

## Quick Start

For a simple release, just run:

```bash
./prepare-release 1.3.3
```

This will automatically:
- Generate changelog from recent commits
- Update version numbers
- Commit and push changes
- Create a pull request

## Available Scripts

### 1. `prepare-release` (Recommended)
**Simple one-command release preparation**

```bash
./prepare-release <version>
```

**Example:**
```bash
./prepare-release 1.3.3
```

### 2. `release-advanced.sh`
**Full-featured release script with options**

```bash
./release-advanced.sh <version> [options]
```

**Options:**
- `--auto-changelog` - Auto-generate changelog from commits
- `--auto-merge` - Auto-merge PR after creation
- `--create-tag` - Create git tag after merge
- `--dry-run` - Show what would be done without making changes

**Examples:**
```bash
# Basic release with auto-changelog
./release-advanced.sh 1.3.3 --auto-changelog

# Full automation (use with caution)
./release-advanced.sh 1.3.3 --auto-changelog --auto-merge --create-tag

# Test what would happen
./release-advanced.sh 1.3.3 --dry-run
```

### 3. `release.sh`
**Basic release script**

```bash
./release.sh <version> [changelog_entries]
```

## Prerequisites

1. **GitHub CLI** - Install with: `brew install gh`
2. **Authentication** - Run: `gh auth login`
3. **Git repository** - Must be in the plugin root directory
4. **Dev branch** - Should be on the `dev` branch

## What the Scripts Do

### Version Updates
- Updates `Version:` in plugin header (`better-payment.php`)
- Updates `const version` in plugin class (`better-payment.php`)
- Updates `Stable tag:` in readme (`readme.txt`)

### Changelog Generation
- Analyzes recent git commits
- Categorizes changes (Added, Fixed, Improved)
- Formats according to WordPress standards
- Adds release date automatically

### Git Operations
- Commits version changes
- Pushes to dev branch
- Creates pull request to master
- Optionally merges PR and creates tags

## Changelog Categories

The auto-changelog feature categorizes commits based on keywords:

- **Added:** commits containing "add", "new", "feature", "support"
- **Fixed:** commits containing "fix", "bug", "issue", "resolve"
- **Improved:** commits containing "improve", "enhance", "update", "refactor"
- **Security:** commits containing "security", "vulnerability", "patch"

## Configuration

Edit `release-config.json` to customize:
- Plugin details
- Repository settings
- Release preferences
- WordPress requirements

## Workflow

### Standard Release Process

1. **Prepare release:**
   ```bash
   ./prepare-release 1.3.3
   ```

2. **Review PR** on GitHub

3. **Merge PR** when ready

4. **Create GitHub release** (optional)

5. **Deploy to WordPress.org**

### Advanced Workflow

1. **Test first:**
   ```bash
   ./release-advanced.sh 1.3.3 --dry-run
   ```

2. **Prepare with auto-changelog:**
   ```bash
   ./release-advanced.sh 1.3.3 --auto-changelog
   ```

3. **Full automation** (for trusted releases):
   ```bash
   ./release-advanced.sh 1.3.3 --auto-changelog --auto-merge --create-tag
   ```

## Troubleshooting

### GitHub CLI Not Authenticated
```bash
gh auth login
```

### Not on Dev Branch
```bash
git checkout dev
git pull origin dev
```

### Uncommitted Changes
```bash
git status
git add .
git commit -m "Prepare for release"
```

### Script Permission Denied
```bash
chmod +x prepare-release
chmod +x release-advanced.sh
chmod +x release.sh
```

## Safety Features

- **Version validation** - Ensures semantic versioning format
- **Branch checking** - Warns if not on dev branch
- **Uncommitted changes** - Warns about uncommitted changes
- **Dry run mode** - Test without making changes
- **Confirmation prompts** - Asks before making changes

## Integration with AI Assistant

You can now simply tell your AI assistant:

> "Prepare release 1.3.4"

And the assistant can run:
```bash
./prepare-release 1.3.4
```

This automates the entire process you previously did manually!

## Files Created

- `prepare-release` - Simple release script
- `release-advanced.sh` - Advanced release script with options
- `release.sh` - Basic release script
- `release-config.json` - Configuration file
- `RELEASE-AUTOMATION.md` - This documentation

## Next Steps

1. Test the scripts with a patch version
2. Customize `release-config.json` if needed
3. Add scripts to your development workflow
4. Consider adding to CI/CD pipeline
