#!/bin/bash

# Simple Release Preparation Script
# Usage: ./prepare-release <version>
# Example: ./prepare-release 1.3.3

# This is a simple wrapper around the advanced release script
# with sensible defaults for quick releases

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Check if version is provided
if [ -z "$1" ]; then
    print_error "Version number is required!"
    echo "Usage: $0 <version>"
    echo "Example: $0 1.3.3"
    exit 1
fi

VERSION="$1"

print_info "🚀 Preparing release for Better Payment v$VERSION"
print_info "This will:"
echo "  ✓ Auto-generate changelog from recent commits"
echo "  ✓ Update version numbers in plugin files"
echo "  ✓ Commit and push changes to dev branch"
echo "  ✓ Create pull request to master branch"
echo ""

read -p "Continue? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Release preparation cancelled"
    exit 0
fi

# Check if advanced script exists
if [ ! -f "release-advanced.sh" ]; then
    print_error "release-advanced.sh not found!"
    exit 1
fi

# Run the advanced script with auto-changelog enabled
print_info "Running release automation..."
./release-advanced.sh "$VERSION" --auto-changelog

print_success "🎉 Release preparation completed!"
print_info "Next steps:"
echo "  1. Review the pull request on GitHub"
echo "  2. Merge the pull request when ready"
echo "  3. Create a release on GitHub"
echo "  4. Deploy to WordPress.org"
