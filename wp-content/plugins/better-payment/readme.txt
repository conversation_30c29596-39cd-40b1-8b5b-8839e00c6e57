=== Better Payment - Instant Payments Through PayPal & Stripe ===
Contributors: wpdevteam, re_enter_rupok, asif2bd, nhrrob, rahatsheikhleon, fuadragib
Donate link: https://wpdeveloper.com
Tags: payment form, donation, PayPal, Custom Forms, WooCommerce Payments
Requires at least: 6.0
Tested up to: 6.8
Requires PHP: 7.4
Stable tag: 1.3.2
License: GPLv3 or later
License URI: http://www.gnu.org/licenses/gpl-3.0.html

Better Payment allows you to automate payment transactions to manage payments, donations, subscriptions, sell products, etc on your Elementor website.

== Description ==

Transform your WordPress payment process with the leading plugin, [Better Payment](https://wpdeveloper.com/better-payment/) which comes with versatile payment form styles and unlimited customization.

Better Payment allows you to manage your own website payments with one click, without any hassle. With seamless integration with Elementor, Better Payment allows your customers to pay through PayPal, Stripe, or others. 💳

With Better Payment, your customers can pay instantly using accepted credit cards, PayPal, Stripe accounts, or other popular choices. Simply by clicking a button, your customers can complete their payment for donations, subscriptions, fundraising, membership, eCommerce products, and the list goes on. You can easily send personalized success messages to customers without any coding. Also, retrieve all the payment history instantly at any time you want. 💰

## ⚙️ POPULAR WEBSITE BUILDER, ELEMENTOR COMPATIBILITY
Better Payment is fully compatible with Elementor. You can make tweaks using Elementor to your Better Payment form. Also, you can integrate with Elementor Form and make the appearance of the payment form more stunning.

## 🛠️ ONE-CLICK STRIPE PAYMENT INTEGRATION
Better Payment lets you easily integrate your Stripe account into your website with one click. You will get a Stripe Elements Checkout option with a securely embedded feature, an onsite checkout option, etc.

## 🔌 PAYPAL PAYMENT GATEWAY INTEGRATION
Better Payment gives you another integration option, it offers a PayPal payment gateway, where you can easily connect your PayPal account and start accepting payments and donations.

Additionally, Better Payment provides credit cards, American Express, Visa, Master Card, and other payment methods as ways to finish the transaction.

https://youtu.be/Mwg7bjP-ISA?si=93QpysX_T2GryJ0i

## 🏆 COMPLETE PAYMENT DIRECTLY WITH PAYSTACK
With Better Payment, you can effortlessly integrate with Paystack to donate & collect money in a quick and simple process. It is an easy process to set up a Paystack account with Better Payment.

## 💷 MULTIPLE CURRENCY SUPPORT
You can easily set up multiple currencies in Better Payment to facilitate your customers’ complete payments in their currency, regardless of where your business is set. At present, Better Payment supports 22 global currencies.

## 🎨 ONE PLUGIN WITH VERSATILE PAYMENT FORM TEMPLATES
Whether you want to add a simple payment form to your website or manage donation and product payments, you can do all with Better Payment. It offers a variety of payment form styling.

### 🖌️ GENERAL FORM STYLES [FREE]
For general purposes, you can design custom payment forms with these beautiful general layouts, showing a custom name, email, payment amount, and total payment summaries. You can adjust the height, width, margin, etc.

### 🖌️ DONATION FORM STYLES [PRO]
Specially designed for collecting donations in one click from your website. You can add multiple payment amount buttons, custom payment amount buttons, and other additional donation, fundraising, and collecting input fields. 

### 🖌️ WOO PAYMENT FORM STYLES [PRO]
Better Payment can be integrated with Woo and collect product payments for available ones. You can pick the product for which you want to make a one-click payment by selecting Woo as the source. You can design and customize the payment forms' appearance easily.

### 🖌️ SUBSCRIPTION FORM STYLES [PRO]
Enable recurring payments in your payment form and manage subscriptions seamlessly for your product, service or anything. With Better Payment, you can create subscription payment forms with stunning designs and start collecting payments.

## 🌟 POWERFUL DONATION FORMS AT YOUR FINGERTIP 🌟
Interactive design layouts and multiple donation fundraising options come with Better Payment plugin. It is very easy to set up your website checkout with the utmost flexibility at the touch of a finger.

## 🛍️ MANAGE WOOCOMMERCE PAYMENTS EASILY
If you have an online store built with WooCommerce, then you can create separate purchase forms for each product and place them anywhere you want on your website. Collect WooCommerce payments seamlessly.

## 📧CUSTOM EMAIL NOTIFICATION
Better Payment allows you to send a notification email to both admin and customer with each transaction.

- **Admin notice email:** You can use the ready admin email template and send email notifications to customers for each transaction. You can add additional headers, shortcodes, etc.
- **Customer notice email:** Customer email address will be auto-populated from the payment form. This email will be used to send email notifications for each transaction. Here you can also add details, shortcodes, etc.

https://youtu.be/tbK8G03QdxE?si=R9MPjU5xQBSBZpHy

## 💸 TRACK EVERY PAYMENT TRANSACTIONS 
Better Payment comes with a list of transaction pages for all transactions. This payment plugin has an advanced filter to search transactions based on transaction ID, email, amount, source, and payment dates.

## ⏬ EXPORT & IMPORT PAYMENT TRANSACTIONS
Better Payment provides you with a one-stop solution for storing all payment transactions. You can import transactions from other places and export files as well in CSV format.

## 🥳 DESIGN ERROR & SUCCESS MESSAGE
After every transaction, whether it is a success or something wrong happened - you can notify your customer directly from your website by designing a custom ‘Thank You’ message, success message with logo, error message, etc. 

## 🔁 MANAGE SUBSCRIPTIONS [PRO]
Build a unique type of membership site, subscriber site with Better Payment Subscription feature. Better Payment offers direct Stripe integration with its payment forms. So, you can easily create a subscription form, collect subscriptions, check the status and many more.

https://youtu.be/gIRfxc9CQu0

## 💹 BUILT-IN PAYMENT ANALYTICS [PRO] 
Clear and short report or analytics on any type of Better Payment transaction that shows total, completed, and in-progress transactions. Also, you can keep track of refunded ones from here as well.

## ⚡ COMING SOON
Better Payment reign doesn’t stop here; more advanced and exclusive features are coming soon. Have a glimpse of the Better Payment roadmap here.

- Advanced email templates
- Coupon management
- Generate invoices
- Fundraising campaigns

🙌 After reading this feature list, you can probably imagine that Better Payment is the best WordPress payment plugin on the market. So, do you want to unlock the advanced features? [Upgrade to our Pro version](https://wpdeveloper.com/better-payment/#pricing).

## 🔥 WHAT’S NEXT
Consider checking out our other WordPress solutions & boost your WordPress website:

🔝 [Essential Addons For Elementor](https://essential-addons.com/elementor/) – Most popular Elementor extensions with 2 millions active users in the WordPress repository.

🔔 [NotificationX](https://notificationx.com/) – Best Social Proof & FOMO Marketing Solution to increase conversion rates.

📄 [EmbedPress](https://embedpress.com/): Easiest WordPress embedding plugin to add content from 150+ sources with one-click. Works in Gutenberg, Elementor, and more.

⏰ [SchedulePress](https://wordpress.org/plugins/wp-scheduled-posts/) – Complete solution for scheduling WordPress posts through an editorial calendar & social share.

Visit [WPDeveloper](https://wpdeveloper.com/) to learn more about how to do better in WordPress with [Help Tutorial, Tips & Tricks](https://wpdeveloper.com/blog).


## 💙 LOVED BETTER PAYMENT? ##

Join our [Facebook Group](https://www.facebook.com/groups/wpdevelopercommunity/)

[Or rate us on WordPress](https://wordpress.org/support/plugin/better-payment/reviews/?rate=5#new-post). 😊

== Installation ==

= Modern Way: =
1. Go to the WordPress Dashboard "Add New Plugin" section.
2. Search For "Better Payment".
3. Install, then Activate it.

= Old Way: =
1. Upload `better-payment` to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress


== Frequently Asked Questions ==

= Does it work with any WordPress theme? =

Yes, it will work with any standard WordPress theme.

= Can I Create Multiple Payment Forms? =

Yes, you can create as many forms as needed on your website, donation, fundraising, product selling, etc. We provide a widget as well as integration with the Elementor form widget.

= Can I Collect More Than Just Payment Data? =

Currently only collecting payment data is available. But soon, you can gather more data using Better Payment.

= Can I Accept Payment Without Leaving My Website? =

Currently, we are redirecting to PayPal or Stripe. And after the completion of payment, we will redirect users back to your website.

= Can I Build Contact Forms or Others Without Payment Methods? =

Yes, you can build regular forms like contact forms without adding payment methods. It has a powerful form builder where you can build any sort of form.


== Screenshots ==

1. Payment form layout
2. Donation form layout
3. WooCommerce form layout
4. Multiple payment methods
5. Payment transaction details
6. In-depth analytics for transaction
7. Payment gateway settings
8. Custom email settings


== Changelog ==

= 1.3.2 - 04/06/2025 =
- Added: NGN currency support
- Added: Error message for Stripe minimum amount limit
- Added: Notice for unsupported currencies in payment gateways
- Fixed: Currency symbol issue in Elementor Form
- Fixed: CSS/JS enqueue issue for Elementor Form in frontend
- Fixed: Static method issue in Handler class
- Fixed: Trait name issue across multiple files
- Few minor bug fixes & improvements

= 1.3.1 - 28/05/2025 =
- Added: KES currency support
- Added: Custom redirection after payment for Paystack
- Fixed: Resubmit button remained disabled after form submission error, preventing users from correcting and resubmitting the form
- Few minor bug fixes & improvements

= 1.3.0 - 13/05/2025 =
- Added: Payment success screen new design
- Fixed: Transaction List | Copy button tooltip not showing for filtered transactions
- Fixed: Quick Setup Wizard | Redirection issue on ajax request
- Few minor bug fixes & improvements

= 1.2.13 - 17/04/2025 =
- Fixed: Function _load_textdomain_just_in_time was called incorrectly notice
- Few minor bug fixes & improvements

= 1.2.12 - 07/04/2025 =
- Added: Transaction Details | Mark as Completed button added to update transaction status
- Few minor bug fixes & improvements

= 1.2.11 - 18/02/2025 =
- Fixed: Recurring payment ends date issue
- Few minor bug fixes & improvements

= 1.2.10 - 21/01/2025 =
- Added: Translation support added for validation messages 
- Fixed: Forms Integration | PayPal redirection not working
- Few minor bug fixes & improvements

= 1.2.9 - 16/01/2025 =
- Fixed: Forms Integration | Redirection not working 
- Few minor bug fixes & improvements

= 1.2.8 - 05/01/2025 =
- Added: RON currency support
- Added: User Dashboard | Transactions tab no items label control
- Revamped: User Dashboard | Responsive design improved
- Fixed: Transaction Details | Sidebar design issues
- Fixed: Transaction List | Import Export button overlapping issue
- Few minor bug fixes & improvements

= 1.2.7 - 22/12/2024 =
- Fixed: Misconfiguration notice not showing properly
- Fixed: Import Export | Subscription data not showing correctly 
- Few minor bug fixes & improvements

= 1.2.6 - 17/12/2024 =
- Added: Decimal point support added for dynamic value 
- Revamped: User Dashboard | Responsive design improved 
- Fixed: Textdomain mismatched issue fixed
- Fixed: Function _load_textdomain_just_in_time was called incorrectly notice
- Few minor bug fixes & improvements

= 1.2.5 - 27/11/2024 =
- Fixed: Form visibility issue fixed
- Few minor bug fixes & improvements

= 1.2.4 - 25/11/2024 =
- Added: MYR currency support
- Fixed: Woo Layout | Validation message not showing input name
- Improved: Security enhancement
- Few minor bug fixes & improvements

= 1.2.3 - 14/11/2024 =
- Added: WordPress 6.7 compatibility. 
- Added: User Dashboard | Dashboard tab added
- Added: Widget Email Settings | PDF support added for customer attachment
- Revamped: Copy to Clipboard feature 
- Fixed: Use Woo Currency dropdown selection not working
- Fixed: User Dashboard | Table extra spacing issues
- Fixed: Email Notification | Plain content type email shows extra stylesheets and markups
- Fixed: Email Notification | Message typography issues
- Few minor bug fixes & improvements

= 1.2.2 - 23/10/2024 =
- Added: User Dashboard | Transactions tab added
- Added: Documentation links on the dashboard settings page
- Fixed: Fatal error while fetching widget settings data 
- Few minor bug fixes & improvements

= 1.2.1 - 02/10/2024 =
- Added: Split Payment support added
- Fixed: Decimal value not working for PayPal
- Few minor bug fixes & improvements

= 1.2.0 - 17/09/2024 =
- Added: User Dashboard widget to allow subscription cancellation
- Few minor bug fixes & improvements

= 1.1.8 - 25/08/2024 =
- Added: WooCommerce multi-product selection support
- Added: Dynamic default price using url parameter
- Few minor bug fixes & improvements

= 1.1.7 - 13/08/2024 =
- Revamped: Email Notification | Admin and customer email templates
- Improved: Security Enhancement
- Few minor bug fixes & improvements

= 1.1.6 - 28/07/2024 =
- Fixed: Forms Integration | Customer email is not fetched from form fields
- Few minor bug fixes & improvements

= 1.1.5 - 01/07/2024 =
- Fixed: Minimum WordPress versions number updated
- Few minor bug fixes & improvements

= 1.1.4 - 30/06/2024 =
- Added: Payment gateways documentation link on the dashboard settings page
- Fixed: Minimum PHP and WordPress versions not showing during plugin zip file installation
- Few minor bug fixes & improvements

= 1.1.3 - 27/05/2024 =
- Fixed: Fatal error on Quick Setup Wizard
- Few minor bug fixes & improvements

= 1.1.2 - 06/05/2024 =
- Few minor bug fixes & improvements

= 1.1.1 - 23/04/2024 =
- Added: Attachment support for customer email on each transaction notification
- Fixed: Settings blank screen due to plugin conflict
- Few minor bug fixes & improvements

= 1.1.0 - 25/03/2024 =
- Added: Bulgarian currency BGN (Stripe only)
- Added: Pre loader added on submit buttons
- Added: Quantity support added for amount list buttons
- Added: Quantity support added for Woo products
- Revamped: Admin notice mechanism for better performance
- Few minor bug fixes & improvements

= 1.0.8 - 18/03/2024 =
- Fixed: Dashboard Transactions List | Imported icon showing for all transactions
- Few minor bug fixes & improvements

= 1.0.7 - 11/03/2024 =
- Added: Dashboard Transactions List | Email address copy button
- Added: Widget Email Settings | Custom logo support
- Added: Widget Email Settings | Greeting text hide show option added
- Improved: Dashboard Transactions List | Action button texts are replaced with icons
- Improved: Dashboard Transactions List | Source column will now show logo instead of text
- Few minor bug fixes & improvements

= 1.0.6 - 28/02/2024 =
- Revamped: Admin notice mechanism for better performance
- Added: On dashboard transactions page imported transactions will now have imported text.
- Few minor bug fixes & improvements

= 1.0.5 - 13/02/2024 =
- Revamped: Code structure for better performance
- Few minor bug fixes & improvements

= 1.0.4 - 22/01/2024 =
- Added: Option to hide show layout sidebar
- Added: Documentation url for payment methods on Dashboard => Settings page
- Revamped: Currency listing order updated to show popular currencies first
- Fixed: Undefined variable related errors on Setup Wizard
- Few minor bug fixes & improvements

= 1.0.3 - 09/01/2024 =
- Added: Import transactions feature to bulk import Better Payment transactions
- Added: Banners added to display pro features for better visibility
- Added: Form fields icon size and color controls
- Fixed: WooCommerce currency symbols are now shown on the layout sidebar
- Fixed: Undefined variable related errors
- Few minor bug fixes & improvements

= 1.0.2 - 27/12/2023 =
- Revamped: Admin notice mechanism for better performance
- Few minor bug fixes & improvements

= 1.0.1 - 11/12/2023 =
- Added: Min, max and default value support for amount field.
- Added: Fixed amount feature using readonly field.
- Added: Forms Integration | Amount list option
- Added: Forms Integration | Paystack payment gateway support
- Improved: Code structure
- Improved: Upgraded minimum required PHP version to 7.3
- Fixed: Amount text not showing for all presets
- Fixed: Layout responsiveness issues
- Fixed: Image alt tag missing issues
- Fixed: Undefined variable and dynamic property related errors
- Fixed: Several console errors
- Fixed: Accessibility improvements on label without for attribute
- Fixed: Forms Integration | Names missing in Dashboard => Transactions page
- Fixed: Forms Integration | PayPal redirection url issue
- Few minor bug fixes & improvements

= 1.0.0 - 13/11/2023 =
- Added: Paystack payment gateway integrated
- Added: Transaction delete feature
- Added: Forms Integration | Default value option
- Added: Forms Integration | Actions after submit related info
- Added: Dashboard settings page doc links
- Fixed: Typography related errors
- Fixed: Global Stripe settings are now properly fetched to local (widget) settings
- Fixed: Forms Integration | Payment status is incomplete
- Fixed: Forms Integration | min and max value not working
- Few minor bug fixes & improvements

= 0.0.7 - 17/10/2023 =
- Fixed: Settings page link missing in plugins list page
- Few minor bug fixes & improvements

= 0.0.6 - 26/07/2023 =
- Fixed: Transaction status not getting updated
- Added: Compatibility support for Elementor 3.15
- Few minor bug fixes & improvements

= 0.0.5 - 10/07/2023 =
- Added: Export transactions feature
- Added: Two additional currencies (AED & ZAR) support
- Added: Layout's border radius responsive control
- Added: Payment button text change control
- Added: Custom currency position support
- Added: Custom redirection after payment
- Fixed: Amount field is required error when hidden
- Fixed: Elementor form integration shows error when required is enabled
- Improved: If different email is used to do payment, then we send email to both addresses.
- Improved: Write with AI control used where needed.
- Improved: Pagination improved with dots
- Improved: Search filter pagination support
- Few minor bug fixes & improvements

= 0.0.4 - 19/06/2023 =
- Revamped: Code structure for better performance
- Improved: Asset mechanism
- Improved: Search filter in Dashboard => Transactions page
- Few minor bug fixes & improvements

= 0.0.3 - 18/04/2022 =
- Revamped: Code structure for better performance
- Added: 3 new layouts
- Added: WooCommerce product payment supports
- Improved: Payment success screen design
- Improved: Filtering options for transactions
- Few minor bug fixes & improvements

= 0.0.2 - 09/02/2022 =
- Added: Quick Setup Wizard
- Few minor bug fixes & improvements

= 0.0.1 - 05/10/2021 =
- Initial beta release


== Upgrade Notice ==

