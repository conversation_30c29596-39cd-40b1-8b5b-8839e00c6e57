#!/bin/bash

# Better Payment Advanced Release Automation Script
# Usage: ./release-advanced.sh <version> [options]
# Options:
#   --auto-changelog    Auto-generate changelog from commits
#   --auto-merge        Auto-merge PR after creation
#   --create-tag        Create git tag after merge
#   --dry-run          Show what would be done without making changes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Default options
AUTO_CHANGELOG=false
AUTO_MERGE=false
CREATE_TAG=false
DRY_RUN=false

# Function to print colored output
print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_debug() { echo -e "${PURPLE}[DEBUG]${NC} $1"; }

# Function to show usage
show_usage() {
    echo "Usage: $0 <version> [options]"
    echo ""
    echo "Options:"
    echo "  --auto-changelog    Auto-generate changelog from commits"
    echo "  --auto-merge        Auto-merge PR after creation"
    echo "  --create-tag        Create git tag after merge"
    echo "  --dry-run          Show what would be done without making changes"
    echo ""
    echo "Examples:"
    echo "  $0 1.3.3"
    echo "  $0 1.3.3 --auto-changelog"
    echo "  $0 1.3.3 --auto-changelog --auto-merge --create-tag"
    echo "  $0 1.3.3 --dry-run"
}

# Parse command line arguments
if [ -z "$1" ]; then
    print_error "Version number is required!"
    show_usage
    exit 1
fi

NEW_VERSION="$1"
shift

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        --auto-changelog)
            AUTO_CHANGELOG=true
            shift
            ;;
        --auto-merge)
            AUTO_MERGE=true
            shift
            ;;
        --create-tag)
            CREATE_TAG=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

CURRENT_DATE=$(date +"%d/%m/%Y")

print_status "Starting release process for version $NEW_VERSION"
if [ "$DRY_RUN" = true ]; then
    print_warning "DRY RUN MODE - No changes will be made"
fi

# Validate version format
if ! [[ $NEW_VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    print_error "Invalid version format. Use semantic versioning (e.g., 1.3.3)"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "better-payment.php" ]; then
    print_error "better-payment.php not found. Please run this script from the plugin root directory."
    exit 1
fi

# Function to execute command or show what would be executed
execute_or_show() {
    if [ "$DRY_RUN" = true ]; then
        print_debug "Would execute: $1"
    else
        eval "$1"
    fi
}

# Check current branch and switch to dev if needed
CURRENT_BRANCH=$(git branch --show-current)
if [ "$CURRENT_BRANCH" != "dev" ]; then
    print_warning "Not on dev branch. Current branch: $CURRENT_BRANCH"
    if [ "$DRY_RUN" = false ]; then
        read -p "Switch to dev branch? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            execute_or_show "git checkout dev && git pull origin dev"
        else
            print_error "Please switch to dev branch first"
            exit 1
        fi
    fi
fi

# Get current version
CURRENT_VERSION=$(grep "Version:" better-payment.php | sed 's/.*Version: //' | tr -d ' ')
print_status "Current version: $CURRENT_VERSION → New version: $NEW_VERSION"

# Check for uncommitted changes
if ! git diff-index --quiet HEAD --; then
    print_warning "Uncommitted changes detected"
    if [ "$DRY_RUN" = false ]; then
        git status --porcelain
        read -p "Continue anyway? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
fi

# Generate changelog
if [ "$AUTO_CHANGELOG" = true ]; then
    print_status "Auto-generating changelog from commits..."
    
    # Get commits since last tag
    LAST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
    if [ -n "$LAST_TAG" ]; then
        COMMITS=$(git log --oneline --pretty=format:"- %s" $LAST_TAG..HEAD | grep -v "Merge\|Version bump\|Updated changelog" | head -10)
    else
        COMMITS=$(git log --oneline --pretty=format:"- %s" -10 | grep -v "Merge\|Version bump\|Updated changelog")
    fi
    
    # Categorize commits
    ADDED_FEATURES=""
    FIXED_BUGS=""
    IMPROVEMENTS=""
    
    while IFS= read -r commit; do
        if [[ $commit =~ ^-.*[Aa]dd ]]; then
            ADDED_FEATURES="$ADDED_FEATURES\n- Added:${commit#*dd}"
        elif [[ $commit =~ ^-.*[Ff]ix ]]; then
            FIXED_BUGS="$FIXED_BUGS\n- Fixed:${commit#*ix}"
        else
            IMPROVEMENTS="$IMPROVEMENTS\n$commit"
        fi
    done <<< "$COMMITS"
    
    CHANGELOG_ENTRIES=""
    [ -n "$ADDED_FEATURES" ] && CHANGELOG_ENTRIES="$CHANGELOG_ENTRIES$ADDED_FEATURES"
    [ -n "$FIXED_BUGS" ] && CHANGELOG_ENTRIES="$CHANGELOG_ENTRIES$FIXED_BUGS"
    [ -n "$IMPROVEMENTS" ] && CHANGELOG_ENTRIES="$CHANGELOG_ENTRIES$IMPROVEMENTS"
    
    if [ -z "$CHANGELOG_ENTRIES" ]; then
        CHANGELOG_ENTRIES="- Few minor bug fixes & improvements"
    fi
    
    CHANGELOG_ENTRIES="$CHANGELOG_ENTRIES\n- Few minor bug fixes & improvements"
else
    CHANGELOG_ENTRIES="- Few minor bug fixes & improvements"
fi

print_status "Changelog entries:"
echo -e "$CHANGELOG_ENTRIES"

if [ "$DRY_RUN" = false ]; then
    read -p "Proceed with these changes? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Update version in files
print_status "Updating version numbers..."
if [ "$DRY_RUN" = false ]; then
    # Update better-payment.php
    sed -i.bak "s/Version: $CURRENT_VERSION/Version: $NEW_VERSION/" better-payment.php
    sed -i.bak "s/const version = '$CURRENT_VERSION';/const version = '$NEW_VERSION';/" better-payment.php
    rm better-payment.php.bak
    
    # Update readme.txt
    sed -i.bak "s/Stable tag: $CURRENT_VERSION/Stable tag: $NEW_VERSION/" readme.txt
    rm readme.txt.bak
    
    # Add changelog entry
    CHANGELOG_ENTRY="= $NEW_VERSION - $CURRENT_DATE =
$(echo -e "$CHANGELOG_ENTRIES")

"
    awk -v entry="$CHANGELOG_ENTRY" '
    /^== Changelog ==/ {
        print $0
        print ""
        print entry
        next
    }
    {print}
    ' readme.txt > readme.txt.tmp && mv readme.txt.tmp readme.txt
fi

print_success "Version numbers updated"

# Commit and push changes
print_status "Committing and pushing changes..."
execute_or_show "git add better-payment.php readme.txt"
execute_or_show "git commit -m 'Version bump to $NEW_VERSION and updated changelog'"
execute_or_show "git push origin dev"

# Create PR
print_status "Creating pull request..."
PR_TITLE="Release v$NEW_VERSION"
PR_BODY="## Release v$NEW_VERSION

### Changes:
$(echo -e "$CHANGELOG_ENTRIES")

### Files Updated:
- Version bumped to $NEW_VERSION in main plugin file
- Updated changelog in readme.txt

Ready for release to WordPress.org repository."

if [ "$DRY_RUN" = false ]; then
    if gh auth status >/dev/null 2>&1; then
        PR_URL=$(gh pr create --base master --head dev --title "$PR_TITLE" --body "$PR_BODY" --json url --jq .url)
        print_success "Pull request created: $PR_URL"
        
        # Auto-merge if requested
        if [ "$AUTO_MERGE" = true ]; then
            print_status "Auto-merging pull request..."
            sleep 5  # Wait a bit for PR to be fully created
            gh pr merge --merge --delete-branch=false
            print_success "Pull request merged"
            
            # Create tag if requested
            if [ "$CREATE_TAG" = true ]; then
                print_status "Creating git tag..."
                git checkout master
                git pull origin master
                git tag -a "v$NEW_VERSION" -m "Release version $NEW_VERSION"
                git push origin "v$NEW_VERSION"
                git checkout dev
                print_success "Tag v$NEW_VERSION created"
            fi
        fi
    else
        print_warning "GitHub CLI not authenticated. Opening browser..."
        ENCODED_BODY=$(echo "$PR_BODY" | sed 's/ /%20/g' | sed 's/\n/%0A/g')
        open "https://github.com/WPDevelopers/better-payment/compare/master...dev?quick_pull=1&title=$PR_TITLE&body=$ENCODED_BODY"
    fi
fi

print_success "Release process completed for version $NEW_VERSION!"
print_status "Summary:"
echo "- Version updated from $CURRENT_VERSION to $NEW_VERSION"
echo "- Changelog updated with release date $CURRENT_DATE"
echo "- Changes committed and pushed to dev branch"
echo "- Pull request created (or browser opened)"

if [ "$AUTO_MERGE" = true ] && [ "$DRY_RUN" = false ]; then
    echo "- Pull request auto-merged"
fi

if [ "$CREATE_TAG" = true ] && [ "$AUTO_MERGE" = true ] && [ "$DRY_RUN" = false ]; then
    echo "- Git tag v$NEW_VERSION created"
fi

print_status "Next steps:"
if [ "$AUTO_MERGE" = false ]; then
    echo "1. Review and merge the pull request"
fi
if [ "$CREATE_TAG" = false ]; then
    echo "2. Create a release tag on GitHub"
fi
echo "3. Deploy to WordPress.org repository"
