{"plugin": {"name": "Better Payment", "slug": "better-payment", "main_file": "better-payment.php", "readme_file": "readme.txt", "text_domain": "better-payment"}, "repository": {"owner": "WPDevelopers", "name": "better-payment", "dev_branch": "dev", "main_branch": "master"}, "release": {"auto_changelog": true, "auto_merge": false, "create_tag": false, "changelog_categories": {"added": ["add", "new", "feature", "support"], "fixed": ["fix", "bug", "issue", "resolve"], "improved": ["improve", "enhance", "update", "refactor"], "security": ["security", "vulnerability", "patch"]}}, "wordpress": {"min_version": "6.0", "tested_version": "6.8", "min_php": "7.4"}}