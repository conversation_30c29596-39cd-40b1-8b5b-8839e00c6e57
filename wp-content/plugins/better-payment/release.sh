#!/bin/bash

# Better Payment Release Automation Script
# Usage: ./release.sh <version> [changelog_entries]
# Example: ./release.sh 1.3.3 "Added new feature, Fixed bug"

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if version is provided
if [ -z "$1" ]; then
    print_error "Version number is required!"
    echo "Usage: $0 <version> [changelog_entries]"
    echo "Example: $0 1.3.3 \"Added new feature, Fixed bug\""
    exit 1
fi

NEW_VERSION="$1"
CHANGELOG_ENTRIES="$2"
CURRENT_DATE=$(date +"%d/%m/%Y")

print_status "Starting release process for version $NEW_VERSION"

# Validate version format (basic check)
if ! [[ $NEW_VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    print_error "Invalid version format. Use semantic versioning (e.g., 1.3.3)"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "better-payment.php" ]; then
    print_error "better-payment.php not found. Please run this script from the plugin root directory."
    exit 1
fi

# Check if we're on dev branch
CURRENT_BRANCH=$(git branch --show-current)
if [ "$CURRENT_BRANCH" != "dev" ]; then
    print_warning "Not on dev branch. Current branch: $CURRENT_BRANCH"
    read -p "Do you want to switch to dev branch? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git checkout dev
        git pull origin dev
    else
        print_error "Please switch to dev branch first"
        exit 1
    fi
fi

# Get current version from plugin file
CURRENT_VERSION=$(grep "Version:" better-payment.php | sed 's/.*Version: //' | tr -d ' ')
print_status "Current version: $CURRENT_VERSION"
print_status "New version: $NEW_VERSION"

# Check if there are uncommitted changes
if ! git diff-index --quiet HEAD --; then
    print_warning "There are uncommitted changes in the repository"
    git status --porcelain
    read -p "Do you want to continue? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

print_status "Updating version in better-payment.php..."

# Update version in plugin header
sed -i.bak "s/Version: $CURRENT_VERSION/Version: $NEW_VERSION/" better-payment.php

# Update version constant
sed -i.bak "s/const version = '$CURRENT_VERSION';/const version = '$NEW_VERSION';/" better-payment.php

# Remove backup file
rm better-payment.php.bak

print_success "Updated version in better-payment.php"

print_status "Updating version in readme.txt..."

# Update stable tag in readme.txt
sed -i.bak "s/Stable tag: $CURRENT_VERSION/Stable tag: $NEW_VERSION/" readme.txt

# Remove backup file
rm readme.txt.bak

print_success "Updated stable tag in readme.txt"

# Auto-generate changelog if not provided
if [ -z "$CHANGELOG_ENTRIES" ]; then
    print_status "Auto-generating changelog from recent commits..."
    
    # Get commits since last tag/version
    LAST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
    if [ -n "$LAST_TAG" ]; then
        COMMITS=$(git log --oneline --pretty=format:"- %s" $LAST_TAG..HEAD | grep -v "Merge\|Version bump\|Updated changelog" | head -10)
    else
        COMMITS=$(git log --oneline --pretty=format:"- %s" -10 | grep -v "Merge\|Version bump\|Updated changelog")
    fi
    
    if [ -n "$COMMITS" ]; then
        CHANGELOG_ENTRIES="$COMMITS"
    else
        CHANGELOG_ENTRIES="- Few minor bug fixes & improvements"
    fi
fi

print_status "Adding changelog entry..."

# Create changelog entry
CHANGELOG_ENTRY="= $NEW_VERSION - $CURRENT_DATE =
$CHANGELOG_ENTRIES

"

# Add changelog entry to readme.txt
awk -v entry="$CHANGELOG_ENTRY" '
/^== Changelog ==/ {
    print $0
    print ""
    print entry
    next
}
{print}
' readme.txt > readme.txt.tmp && mv readme.txt.tmp readme.txt

print_success "Added changelog entry"

print_status "Committing changes..."

# Add and commit changes
git add better-payment.php readme.txt
git commit -m "Version bump to $NEW_VERSION and updated changelog"

print_success "Changes committed"

print_status "Pushing to dev branch..."

# Push to dev branch
git push origin dev

print_success "Pushed to dev branch"

print_status "Creating pull request..."

# Create PR using GitHub CLI
PR_TITLE="Release v$NEW_VERSION"
PR_BODY="## Release v$NEW_VERSION

### Changes:
$CHANGELOG_ENTRIES

### Files Updated:
- Version bumped to $NEW_VERSION in main plugin file
- Updated changelog in readme.txt

Ready for release to WordPress.org repository."

# Check if gh CLI is authenticated
if ! gh auth status >/dev/null 2>&1; then
    print_warning "GitHub CLI not authenticated. Please run 'gh auth login' first."
    print_status "Opening browser to create PR manually..."
    
    # URL encode the PR body for browser
    ENCODED_BODY=$(echo "$PR_BODY" | sed 's/ /%20/g' | sed 's/\n/%0A/g')
    open "https://github.com/WPDevelopers/better-payment/compare/master...dev?quick_pull=1&title=$PR_TITLE&body=$ENCODED_BODY"
else
    # Create PR using GitHub CLI
    gh pr create --base master --head dev --title "$PR_TITLE" --body "$PR_BODY"
    print_success "Pull request created successfully!"
fi

print_success "Release process completed for version $NEW_VERSION!"
print_status "Next steps:"
echo "1. Review and merge the pull request"
echo "2. Create a release tag on GitHub"
echo "3. Deploy to WordPress.org repository"
