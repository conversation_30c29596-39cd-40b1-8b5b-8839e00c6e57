{"projects": {"essential-addons-dev": {"name": "Essential Addons Development", "path": "/Users/<USER>/Sites/essential-addons-dev", "plugins_path": "wp-content/plugins", "plugins": {"better-payment": {"name": "Better Payment", "path": "better-payment", "main_file": "better-payment.php", "readme_file": "readme.txt", "repository": {"owner": "WPDevelopers", "name": "better-payment", "dev_branch": "dev", "main_branch": "master"}}, "better-payment-pro": {"name": "Better Payment Pro", "path": "@better-payment-pro", "main_file": "better-payment-pro.php", "readme_file": "readme.txt", "repository": {"owner": "WPDevelopers", "name": "better-payment-pro", "dev_branch": "dev", "main_branch": "master"}}, "essential-addons-elementor": {"name": "Essential Addons for Elementor", "path": "essential-addons-elementor", "main_file": "essential_adons_elementor.php", "readme_file": "readme.txt", "repository": {"owner": "WPDevelopers", "name": "essential-addons-elementor", "dev_branch": "dev", "main_branch": "master"}}, "essential-addons-for-elementor-lite": {"name": "Essential Addons for Elementor Lite", "path": "essential-addons-for-elementor-lite", "main_file": "essential_adons_elementor.php", "readme_file": "readme.txt", "repository": {"owner": "WPDevelopers", "name": "essential-addons-for-elementor-lite", "dev_branch": "dev", "main_branch": "master"}}}}, "nhrrob-dev": {"name": "NHRROB Development", "path": "/Users/<USER>/Sites/nhrrob-dev", "plugins_path": "wp-content/plugins", "plugins": {"nhrrob-movies": {"name": "NHRROB Movies", "path": "@nhrrob-movies", "main_file": "nhrrob-movies.php", "readme_file": "readme.txt", "repository": {"owner": "nhrrob", "name": "nhrrob-movies", "dev_branch": "dev", "main_branch": "main"}}, "nhrrob-core-contributions": {"name": "NHRROB Core Contributions", "path": "nhrrob-core-contributions", "main_file": "nhrrob-core-contributions.php", "readme_file": "readme.txt", "repository": {"owner": "nhrrob", "name": "nhrrob-core-contributions", "dev_branch": "dev", "main_branch": "main"}}, "nhrrob-options-table-manager": {"name": "NHRROB Options Table Manager", "path": "nhrrob-options-table-manager", "main_file": "nhrrob-options-table-manager.php", "readme_file": "readme.txt", "repository": {"owner": "nhrrob", "name": "nhrrob-options-table-manager", "dev_branch": "dev", "main_branch": "main"}}}}}, "global_settings": {"default_dev_branch": "dev", "default_main_branch": "master", "auto_changelog": true, "auto_merge": false, "create_tag": false, "wordpress": {"min_version": "6.0", "tested_version": "6.8", "min_php": "7.4"}}, "release_groups": {"better-payment-suite": {"project": "essential-addons-dev", "plugins": ["better-payment", "better-payment-pro"]}, "essential-addons-suite": {"project": "essential-addons-dev", "plugins": ["essential-addons-elementor", "essential-addons-for-elementor-lite"]}, "nhrrob-suite": {"project": "nhrrob-dev", "plugins": ["nhrrob-movies", "nhrrob-core-contributions", "nhrrob-options-table-manager"]}, "all-essential-addons": {"project": "essential-addons-dev", "plugins": ["better-payment", "better-payment-pro", "essential-addons-elementor", "essential-addons-for-elementor-lite"]}, "all-nhrrob": {"project": "nhrrob-dev", "plugins": ["nhrrob-movies", "nhrrob-core-contributions", "nhrrob-options-table-manager"]}}}