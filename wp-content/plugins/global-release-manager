#!/bin/bash

# Global WordPress Plugin Release Manager
# Works across multiple projects and environments
# Usage: ./global-release-manager <project>/<plugin> <version> [options]
# Example: ./global-release-manager essential-addons-dev/better-payment 1.3.3
# Example: ./global-release-manager nhrrob-dev/nhrrob-movies 2.1.0

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Default options
AUTO_CHANGELOG=true
AUTO_MERGE=false
CREATE_TAG=false
DRY_RUN=false

# Functions
print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_debug() { echo -e "${PURPLE}[DEBUG]${NC} $1"; }
print_project() { echo -e "${CYAN}[PROJECT]${NC} $1"; }

show_usage() {
    echo "🌍 Global WordPress Plugin Release Manager"
    echo ""
    echo "Usage: $0 <project>/<plugin> <version> [options]"
    echo "   or: $0 <release-group> <version> [options]"
    echo ""
    echo "Project/Plugin Format:"
    echo "  essential-addons-dev/better-payment 1.3.3"
    echo "  nhrrob-dev/nhrrob-movies 2.1.0"
    echo ""
    echo "Available Projects & Plugins:"
    python3 -c "
import json
import sys
try:
    with open('$(dirname "$0")/global-release-config.json', 'r') as f:
        config = json.load(f)
    
    for project_key, project in config.get('projects', {}).items():
        print(f'  📁 {project_key} ({project[\"name\"]}):')
        for plugin_key, plugin in project.get('plugins', {}).items():
            print(f'     - {project_key}/{plugin_key} ({plugin[\"name\"]})')
        print()
    
    print('Release Groups:')
    for group_key, group in config.get('release_groups', {}).items():
        project = group.get('project', 'multiple')
        plugins = ', '.join(group.get('plugins', []))
        print(f'  📦 {group_key} ({project}): {plugins}')
        
except Exception as e:
    print(f'Error reading config: {e}')
    sys.exit(1)
"
    echo ""
    echo "Options:"
    echo "  --auto-changelog    Auto-generate changelog from commits (default: true)"
    echo "  --auto-merge        Auto-merge PR after creation"
    echo "  --create-tag        Create git tag after merge"
    echo "  --dry-run          Show what would be done without making changes"
    echo ""
    echo "Examples:"
    echo "  $0 essential-addons-dev/better-payment 1.3.3"
    echo "  $0 nhrrob-dev/nhrrob-movies 2.1.0 --auto-merge"
    echo "  $0 better-payment-suite 1.3.3"
    echo "  $0 nhrrob-suite 2.0.0 --dry-run"
}

# Check if config file exists
CONFIG_FILE="$(dirname "$0")/global-release-config.json"
if [ ! -f "$CONFIG_FILE" ]; then
    print_error "Global configuration file not found: $CONFIG_FILE"
    exit 1
fi

# Parse arguments
if [ -z "$1" ] || [ -z "$2" ]; then
    print_error "Project/plugin and version are required!"
    show_usage
    exit 1
fi

TARGET="$1"
NEW_VERSION="$2"
shift 2

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        --auto-changelog)
            AUTO_CHANGELOG=true
            shift
            ;;
        --no-auto-changelog)
            AUTO_CHANGELOG=false
            shift
            ;;
        --auto-merge)
            AUTO_MERGE=true
            shift
            ;;
        --create-tag)
            CREATE_TAG=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Function to get project/plugin config
get_config() {
    local target="$1"
    local key="$2"
    python3 -c "
import json
import sys
try:
    with open('$CONFIG_FILE', 'r') as f:
        config = json.load(f)
    
    # Check if it's a release group
    if '$target' in config.get('release_groups', {}):
        group = config['release_groups']['$target']
        project_key = group.get('project')
        plugins = group.get('plugins', [])
        
        if '$key' == 'type':
            print('group')
        elif '$key' == 'project':
            print(project_key)
        elif '$key' == 'plugins':
            for plugin in plugins:
                print(plugin)
        sys.exit(0)
    
    # Check if it's project/plugin format
    if '/' in '$target':
        project_key, plugin_key = '$target'.split('/', 1)
        
        if project_key in config.get('projects', {}) and plugin_key in config['projects'][project_key].get('plugins', {}):
            project = config['projects'][project_key]
            plugin = project['plugins'][plugin_key]
            
            if '$key' == 'type':
                print('single')
            elif '$key' == 'project_path':
                print(project.get('path', ''))
            elif '$key' == 'plugins_path':
                print(project.get('plugins_path', ''))
            elif '$key' == 'plugin_path':
                print(plugin.get('path', ''))
            elif '$key' == 'main_file':
                print(plugin.get('main_file', ''))
            elif '$key' == 'readme_file':
                print(plugin.get('readme_file', ''))
            elif '$key' == 'plugin_name':
                print(plugin.get('name', ''))
            elif '$key' == 'repo_owner':
                print(plugin.get('repository', {}).get('owner', ''))
            elif '$key' == 'repo_name':
                print(plugin.get('repository', {}).get('name', ''))
            elif '$key' == 'dev_branch':
                print(plugin.get('repository', {}).get('dev_branch', 'dev'))
            elif '$key' == 'main_branch':
                print(plugin.get('repository', {}).get('main_branch', 'master'))
            sys.exit(0)
    
    sys.exit(1)
except Exception as e:
    sys.exit(1)
"
}

# Function to release a single plugin
release_plugin() {
    local project_plugin="$1"
    local version="$2"
    
    print_status "🚀 Releasing $project_plugin v$version"
    
    # Get plugin configuration
    local config_type=$(get_config "$project_plugin" "type")
    if [ "$config_type" != "single" ]; then
        print_error "Invalid plugin format: $project_plugin"
        return 1
    fi
    
    local project_path=$(get_config "$project_plugin" "project_path")
    local plugins_path=$(get_config "$project_plugin" "plugins_path")
    local plugin_path=$(get_config "$project_plugin" "plugin_path")
    local main_file=$(get_config "$project_plugin" "main_file")
    local readme_file=$(get_config "$project_plugin" "readme_file")
    local plugin_name=$(get_config "$project_plugin" "plugin_name")
    local dev_branch=$(get_config "$project_plugin" "dev_branch")
    local main_branch=$(get_config "$project_plugin" "main_branch")
    
    if [ -z "$project_path" ] || [ -z "$plugin_path" ]; then
        print_error "Plugin configuration incomplete for: $project_plugin"
        return 1
    fi
    
    local full_path="$project_path/$plugins_path/$plugin_path"
    
    if [ ! -d "$full_path" ]; then
        print_error "Plugin directory not found: $full_path"
        return 1
    fi
    
    print_project "Project: $(basename "$project_path")"
    print_status "Plugin: $plugin_name"
    print_status "Path: $full_path"
    print_status "Main file: $main_file"
    
    # Store original directory
    local original_dir=$(pwd)
    
    # Change to plugin directory
    cd "$full_path"
    
    # Check if it's a git repository
    if [ ! -d ".git" ]; then
        print_error "Not a git repository: $full_path"
        cd "$original_dir"
        return 1
    fi
    
    # Get current version
    if [ ! -f "$main_file" ]; then
        print_error "Main plugin file not found: $main_file"
        cd "$original_dir"
        return 1
    fi
    
    local current_version=$(grep -E "Version:|const version" "$main_file" | head -1 | sed 's/.*[Vv]ersion[: =]*['\''"]*//' | sed 's/['\''";].*//' | tr -d ' ')
    
    print_status "Current version: $current_version → New version: $version"
    
    # Validate version format
    if ! [[ $version =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        print_error "Invalid version format. Use semantic versioning (e.g., 1.3.3)"
        cd "$original_dir"
        return 1
    fi
    
    # Check current branch
    local current_branch=$(git branch --show-current)
    
    if [ "$current_branch" != "$dev_branch" ]; then
        print_warning "Not on $dev_branch branch. Current branch: $current_branch"
        if [ "$DRY_RUN" = false ]; then
            read -p "Switch to $dev_branch branch? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                git checkout "$dev_branch"
                git pull origin "$dev_branch"
            else
                print_error "Please switch to $dev_branch branch first"
                cd "$original_dir"
                return 1
            fi
        fi
    fi
    
    # Generate changelog if enabled
    local changelog_entries=""
    if [ "$AUTO_CHANGELOG" = true ]; then
        print_status "Auto-generating changelog..."
        
        local last_tag=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
        local commits=""
        
        if [ -n "$last_tag" ]; then
            commits=$(git log --oneline --pretty=format:"- %s" $last_tag..HEAD | grep -v "Merge\|Version bump\|Updated changelog" | head -10)
        else
            commits=$(git log --oneline --pretty=format:"- %s" -10 | grep -v "Merge\|Version bump\|Updated changelog")
        fi
        
        if [ -n "$commits" ]; then
            changelog_entries="$commits"
        else
            changelog_entries="- Few minor bug fixes & improvements"
        fi
        
        changelog_entries="$changelog_entries\n- Few minor bug fixes & improvements"
    else
        changelog_entries="- Few minor bug fixes & improvements"
    fi
    
    print_status "Changelog entries:"
    echo -e "$changelog_entries"
    
    if [ "$DRY_RUN" = true ]; then
        print_debug "DRY RUN: Would update version in $main_file"
        print_debug "DRY RUN: Would update changelog in $readme_file"
        print_debug "DRY RUN: Would commit and push changes"
        print_debug "DRY RUN: Would create pull request"
        cd "$original_dir"
        return 0
    fi
    
    # Update version in main file
    print_status "Updating version in $main_file..."
    
    # Handle different version patterns
    if grep -q "Version:" "$main_file"; then
        sed -i.bak "s/Version: $current_version/Version: $version/" "$main_file"
    fi
    
    if grep -q "const version" "$main_file"; then
        sed -i.bak "s/const version = '$current_version'/const version = '$version'/" "$main_file"
    fi
    
    # Remove backup file
    [ -f "$main_file.bak" ] && rm "$main_file.bak"
    
    # Update readme if it exists
    if [ -f "$readme_file" ]; then
        print_status "Updating $readme_file..."
        
        # Update stable tag
        sed -i.bak "s/Stable tag: $current_version/Stable tag: $version/" "$readme_file"
        
        # Add changelog entry
        local current_date=$(date +"%d/%m/%Y")
        local changelog_entry="= $version - $current_date =
$(echo -e "$changelog_entries")

"
        
        awk -v entry="$changelog_entry" '
        /^== Changelog ==/ {
            print $0
            print ""
            print entry
            next
        }
        {print}
        ' "$readme_file" > "$readme_file.tmp" && mv "$readme_file.tmp" "$readme_file"
        
        # Remove backup file
        [ -f "$readme_file.bak" ] && rm "$readme_file.bak"
    fi
    
    # Commit and push
    print_status "Committing changes..."
    git add "$main_file" ${readme_file:+"$readme_file"}
    git commit -m "Version bump to $version and updated changelog"
    
    print_status "Pushing to $dev_branch..."
    git push origin "$dev_branch"
    
    # Create PR
    print_status "Creating pull request..."
    local repo_owner=$(get_config "$project_plugin" "repo_owner")
    local repo_name=$(get_config "$project_plugin" "repo_name")
    
    local pr_title="Release v$version"
    local pr_body="## Release v$version

### Changes:
$(echo -e "$changelog_entries")

### Files Updated:
- Version bumped to $version in main plugin file
- Updated changelog in readme file

Ready for release."
    
    if gh auth status >/dev/null 2>&1; then
        local pr_url=$(gh pr create --base "$main_branch" --head "$dev_branch" --title "$pr_title" --body "$pr_body" --json url --jq .url)
        print_success "Pull request created: $pr_url"
        
        if [ "$AUTO_MERGE" = true ]; then
            print_status "Auto-merging pull request..."
            sleep 5
            gh pr merge --merge --delete-branch=false
            print_success "Pull request merged"
            
            if [ "$CREATE_TAG" = true ]; then
                print_status "Creating git tag..."
                git checkout "$main_branch"
                git pull origin "$main_branch"
                git tag -a "v$version" -m "Release version $version"
                git push origin "v$version"
                git checkout "$dev_branch"
                print_success "Tag v$version created"
            fi
        fi
    else
        print_warning "GitHub CLI not authenticated. Please run 'gh auth login'"
        cd "$original_dir"
        return 1
    fi
    
    print_success "✅ $plugin_name v$version released successfully!"
    
    # Return to original directory
    cd "$original_dir"
}

# Main execution
print_status "🌍 Global Plugin Release Manager"

# Validate version format
if ! [[ $NEW_VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    print_error "Invalid version format. Use semantic versioning (e.g., 1.3.3)"
    exit 1
fi

if [ "$DRY_RUN" = true ]; then
    print_warning "🧪 DRY RUN MODE - No changes will be made"
fi

# Check if target is a release group
config_type=$(get_config "$TARGET" "type")

if [ "$config_type" = "group" ]; then
    # Handle release group
    project_key=$(get_config "$TARGET" "project")
    plugins=$(get_config "$TARGET" "plugins")
    
    if [ -z "$plugins" ]; then
        print_error "No plugins found in release group: $TARGET"
        exit 1
    fi
    
    plugin_count=$(echo "$plugins" | wc -l | tr -d ' ')
    
    print_status "📦 Releasing $plugin_count plugins in group: $TARGET"
    echo "$plugins" | while read -r plugin; do
        if [ -n "$plugin" ]; then
            echo "  - $project_key/$plugin"
        fi
    done
    echo ""
    
    if [ "$DRY_RUN" = false ]; then
        read -p "Continue with group release? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Release cancelled"
            exit 0
        fi
    fi
    
    # Release each plugin in the group
    success_count=0
    total_count=0
    
    echo "$plugins" | while read -r plugin; do
        if [ -n "$plugin" ]; then
            total_count=$((total_count + 1))
            echo ""
            print_status "📋 Processing plugin $total_count of $plugin_count: $plugin"
            
            if release_plugin "$project_key/$plugin" "$NEW_VERSION"; then
                success_count=$((success_count + 1))
            else
                print_error "Failed to release $project_key/$plugin"
            fi
        fi
    done
    
elif [ "$config_type" = "single" ]; then
    # Handle single plugin
    if release_plugin "$TARGET" "$NEW_VERSION"; then
        print_success "🎉 Release completed successfully!"
    else
        print_error "Release failed"
        exit 1
    fi
else
    print_error "Invalid target: $TARGET"
    echo ""
    show_usage
    exit 1
fi

print_success "🎉 Global release process completed!"
print_status "Released version $NEW_VERSION for: $TARGET"
