{"plugins": {"better-payment": {"name": "Better Payment", "path": "better-payment", "main_file": "better-payment.php", "readme_file": "readme.txt", "repository": {"owner": "WPDevelopers", "name": "better-payment", "dev_branch": "dev", "main_branch": "master"}, "has_git": true, "auto_changelog": true}, "better-payment-pro": {"name": "Better Payment Pro", "path": "@better-payment-pro", "main_file": "better-payment-pro.php", "readme_file": "readme.txt", "repository": {"owner": "WPDevelopers", "name": "better-payment-pro", "dev_branch": "dev", "main_branch": "master"}, "has_git": true, "auto_changelog": true}, "essential-addons-elementor": {"name": "Essential Addons for Elementor", "path": "essential-addons-elementor", "main_file": "essential_adons_elementor.php", "readme_file": "readme.txt", "repository": {"owner": "WPDevelopers", "name": "essential-addons-elementor", "dev_branch": "dev", "main_branch": "master"}, "has_git": true, "auto_changelog": true}, "essential-addons-for-elementor-lite": {"name": "Essential Addons for Elementor Lite", "path": "essential-addons-for-elementor-lite", "main_file": "essential_adons_elementor.php", "readme_file": "readme.txt", "repository": {"owner": "WPDevelopers", "name": "essential-addons-for-elementor-lite", "dev_branch": "dev", "main_branch": "master"}, "has_git": true, "auto_changelog": true}}, "global_settings": {"default_dev_branch": "dev", "default_main_branch": "master", "auto_changelog": true, "auto_merge": false, "create_tag": false, "wordpress": {"min_version": "6.0", "tested_version": "6.8", "min_php": "7.4"}}, "release_groups": {"better-payment-suite": ["better-payment", "better-payment-pro"], "essential-addons-suite": ["essential-addons-elementor", "essential-addons-for-elementor-lite"], "all-plugins": ["better-payment", "better-payment-pro", "essential-addons-elementor", "essential-addons-for-elementor-lite"]}}