#!/bin/bash

# Universal Plugin Release Preparation Script
# Usage: ./prepare-release <plugin-slug> <version>
# Example: ./prepare-release better-payment 1.3.3
# Example: ./prepare-release essential-addons-elementor 6.4.0

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

show_usage() {
    echo "🚀 Universal Plugin Release Preparation"
    echo ""
    echo "Usage: $0 <plugin-slug> <version>"
    echo ""
    echo "Available Plugins:"
    echo "  better-payment                    - Better Payment"
    echo "  better-payment-pro                - Better Payment Pro"
    echo "  essential-addons-elementor        - Essential Addons for Elementor"
    echo "  essential-addons-for-elementor-lite - Essential Addons for Elementor Lite"
    echo ""
    echo "Release Groups:"
    echo "  better-payment-suite              - Both Better Payment plugins"
    echo "  essential-addons-suite            - Both Essential Addons plugins"
    echo "  all-plugins                       - All plugins (use with caution)"
    echo ""
    echo "Examples:"
    echo "  $0 better-payment 1.3.3"
    echo "  $0 essential-addons-elementor 6.4.0"
    echo "  $0 better-payment-suite 1.3.3     # Release both Better Payment plugins"
    echo ""
    echo "What this does:"
    echo "  ✓ Auto-generate changelog from recent commits"
    echo "  ✓ Update version numbers in plugin files"
    echo "  ✓ Commit and push changes to dev branch"
    echo "  ✓ Create pull request to master branch"
}

# Check if arguments are provided
if [ -z "$1" ] || [ -z "$2" ]; then
    print_error "Plugin slug and version are required!"
    echo ""
    show_usage
    exit 1
fi

PLUGIN_SLUG="$1"
VERSION="$2"

# Check if help is requested
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
fi

print_info "🚀 Preparing release for $PLUGIN_SLUG v$VERSION"

# Check if release manager exists
if [ ! -f "release-manager" ]; then
    print_error "release-manager script not found!"
    print_info "Please make sure you're running this from the plugins directory"
    exit 1
fi

# Show what will happen
echo ""
print_info "This will:"
echo "  ✓ Auto-generate changelog from recent commits"
echo "  ✓ Update version numbers in plugin files"
echo "  ✓ Commit and push changes to dev branch"
echo "  ✓ Create pull request to master branch"
echo ""

read -p "Continue? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Release preparation cancelled"
    exit 0
fi

# Run the release manager
print_info "Running release automation..."
./release-manager "$PLUGIN_SLUG" "$VERSION" --auto-changelog

print_success "🎉 Release preparation completed!"
print_info "Next steps:"
echo "  1. Review the pull request(s) on GitHub"
echo "  2. Merge the pull request(s) when ready"
echo "  3. Create release(s) on GitHub"
echo "  4. Deploy to WordPress.org"
