#!/bin/bash

# Global Plugin Release Preparation Script
# Works across multiple projects and environments
# Usage: ./prepare-release-global <project>/<plugin> <version>
# Example: ./prepare-release-global essential-addons-dev/better-payment 1.3.3
# Example: ./prepare-release-global nhrrob-dev/nhrrob-movies 2.1.0

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_project() { echo -e "${CYAN}[PROJECT]${NC} $1"; }

show_usage() {
    echo "🌍 Global Plugin Release Preparation"
    echo ""
    echo "Usage: $0 <project>/<plugin> <version>"
    echo "   or: $0 <release-group> <version>"
    echo ""
    echo "Available Projects & Plugins:"
    echo "  📁 essential-addons-dev:"
    echo "     - essential-addons-dev/better-payment"
    echo "     - essential-addons-dev/better-payment-pro"
    echo "     - essential-addons-dev/essential-addons-elementor"
    echo "     - essential-addons-dev/essential-addons-for-elementor-lite"
    echo ""
    echo "  📁 nhrrob-dev:"
    echo "     - nhrrob-dev/nhrrob-movies"
    echo "     - nhrrob-dev/nhrrob-core-contributions"
    echo "     - nhrrob-dev/nhrrob-options-table-manager"
    echo ""
    echo "Release Groups:"
    echo "  📦 better-payment-suite        - Both Better Payment plugins"
    echo "  📦 essential-addons-suite      - Both Essential Addons plugins"
    echo "  📦 nhrrob-suite               - All NHRROB plugins"
    echo "  📦 all-essential-addons       - All Essential Addons project plugins"
    echo "  📦 all-nhrrob                 - All NHRROB project plugins"
    echo ""
    echo "Examples:"
    echo "  $0 essential-addons-dev/better-payment 1.3.3"
    echo "  $0 nhrrob-dev/nhrrob-movies 2.1.0"
    echo "  $0 better-payment-suite 1.3.3"
    echo "  $0 nhrrob-suite 2.0.0"
    echo ""
    echo "What this does:"
    echo "  ✓ Auto-generate changelog from recent commits"
    echo "  ✓ Update version numbers in plugin files"
    echo "  ✓ Commit and push changes to dev branch"
    echo "  ✓ Create pull request to master/main branch"
}

# Check if arguments are provided
if [ -z "$1" ] || [ -z "$2" ]; then
    print_error "Target and version are required!"
    echo ""
    show_usage
    exit 1
fi

TARGET="$1"
VERSION="$2"

# Check if help is requested
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
fi

# Determine project from target
if [[ "$TARGET" == *"essential-addons-dev"* ]]; then
    PROJECT="essential-addons-dev"
elif [[ "$TARGET" == *"nhrrob-dev"* ]]; then
    PROJECT="nhrrob-dev"
elif [[ "$TARGET" == "better-payment-suite" ]] || [[ "$TARGET" == "essential-addons-suite" ]] || [[ "$TARGET" == "all-essential-addons" ]]; then
    PROJECT="essential-addons-dev"
elif [[ "$TARGET" == "nhrrob-suite" ]] || [[ "$TARGET" == "all-nhrrob" ]]; then
    PROJECT="nhrrob-dev"
else
    PROJECT="unknown"
fi

print_info "🌍 Global Plugin Release Preparation"
print_project "Target: $TARGET"
print_project "Version: $VERSION"

if [ "$PROJECT" != "unknown" ]; then
    print_project "Project: $PROJECT"
fi

# Check if global release manager exists
if [ ! -f "global-release-manager" ]; then
    print_error "global-release-manager script not found!"
    print_info "Please make sure you're running this from the correct directory"
    exit 1
fi

# Show what will happen
echo ""
print_info "This will:"
echo "  ✓ Auto-generate changelog from recent commits"
echo "  ✓ Update version numbers in plugin files"
echo "  ✓ Commit and push changes to dev branch"
echo "  ✓ Create pull request to master/main branch"

# Show which plugins will be affected
if [[ "$TARGET" == *"/"* ]]; then
    echo "  ✓ Release single plugin: $TARGET"
else
    echo "  ✓ Release group: $TARGET (multiple plugins)"
fi

echo ""

read -p "Continue? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Release preparation cancelled"
    exit 0
fi

# Run the global release manager
print_info "Running global release automation..."
./global-release-manager "$TARGET" "$VERSION" --auto-changelog

print_success "🎉 Global release preparation completed!"
print_info "Next steps:"
echo "  1. Review the pull request(s) on GitHub"
echo "  2. Merge the pull request(s) when ready"
echo "  3. Create release(s) on GitHub"
echo "  4. Deploy to WordPress.org (if applicable)"
