#!/bin/bash

# Universal WordPress Plugin Release Manager
# Usage: ./release-manager <plugin-slug> <version> [options]
# Example: ./release-manager better-payment 1.3.3
# Example: ./release-manager essential-addons-elementor 6.4.0 --auto-merge

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Default options
AUTO_CHANGELOG=true
AUTO_MERGE=false
CREATE_TAG=false
DRY_RUN=false

# Functions
print_status() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_debug() { echo -e "${PURPLE}[DEBUG]${NC} $1"; }

show_usage() {
    echo "Universal WordPress Plugin Release Manager"
    echo ""
    echo "Usage: $0 <plugin-slug> <version> [options]"
    echo ""
    echo "Plugin Slugs:"
    echo "  better-payment                    - Better Payment"
    echo "  better-payment-pro                - Better Payment Pro"
    echo "  essential-addons-elementor        - Essential Addons for Elementor"
    echo "  essential-addons-for-elementor-lite - Essential Addons for Elementor Lite"
    echo ""
    echo "Options:"
    echo "  --auto-changelog    Auto-generate changelog from commits (default: true)"
    echo "  --auto-merge        Auto-merge PR after creation"
    echo "  --create-tag        Create git tag after merge"
    echo "  --dry-run          Show what would be done without making changes"
    echo ""
    echo "Examples:"
    echo "  $0 better-payment 1.3.3"
    echo "  $0 essential-addons-elementor 6.4.0 --auto-merge"
    echo "  $0 better-payment-pro 2.1.0 --dry-run"
    echo ""
    echo "Release Groups:"
    echo "  $0 better-payment-suite 1.3.3     # Release both Better Payment plugins"
    echo "  $0 essential-addons-suite 6.4.0   # Release both Essential Addons plugins"
    echo "  $0 all-plugins 1.0.0              # Release all plugins (use with caution)"
}

# Check if config file exists
CONFIG_FILE="$(dirname "$0")/multi-release-config.json"
if [ ! -f "$CONFIG_FILE" ]; then
    print_error "Configuration file not found: $CONFIG_FILE"
    exit 1
fi

# Parse arguments
if [ -z "$1" ] || [ -z "$2" ]; then
    print_error "Plugin slug and version are required!"
    show_usage
    exit 1
fi

PLUGIN_SLUG="$1"
NEW_VERSION="$2"
shift 2

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        --auto-changelog)
            AUTO_CHANGELOG=true
            shift
            ;;
        --no-auto-changelog)
            AUTO_CHANGELOG=false
            shift
            ;;
        --auto-merge)
            AUTO_MERGE=true
            shift
            ;;
        --create-tag)
            CREATE_TAG=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Function to get plugin config
get_plugin_config() {
    local slug="$1"
    local key="$2"
    python3 -c "
import json
import sys
try:
    with open('$CONFIG_FILE', 'r') as f:
        config = json.load(f)
    
    if '$slug' in config.get('release_groups', {}):
        # Handle release groups
        plugins = config['release_groups']['$slug']
        for plugin in plugins:
            if plugin in config['plugins']:
                print(config['plugins'][plugin].get('$key', ''))
        sys.exit(0)
    
    if '$slug' in config['plugins']:
        print(config['plugins']['$slug'].get('$key', ''))
    else:
        sys.exit(1)
except Exception as e:
    sys.exit(1)
"
}

# Function to check if plugin exists
plugin_exists() {
    local slug="$1"
    python3 -c "
import json
try:
    with open('$CONFIG_FILE', 'r') as f:
        config = json.load(f)
    
    if '$slug' in config.get('plugins', {}) or '$slug' in config.get('release_groups', {}):
        exit(0)
    else:
        exit(1)
except:
    exit(1)
"
}

# Function to get plugins from group
get_plugins_from_group() {
    local slug="$1"
    python3 -c "
import json
try:
    with open('$CONFIG_FILE', 'r') as f:
        config = json.load(f)
    
    if '$slug' in config.get('release_groups', {}):
        plugins = config['release_groups']['$slug']
        for plugin in plugins:
            print(plugin)
    elif '$slug' in config.get('plugins', {}):
        print('$slug')
except:
    pass
"
}

# Function to release a single plugin
release_plugin() {
    local plugin_slug="$1"
    local version="$2"
    
    print_status "🚀 Releasing $plugin_slug v$version"
    
    # Get plugin configuration
    local plugin_path=$(get_plugin_config "$plugin_slug" "path")
    local main_file=$(get_plugin_config "$plugin_slug" "main_file")
    local readme_file=$(get_plugin_config "$plugin_slug" "readme_file")
    local plugin_name=$(get_plugin_config "$plugin_slug" "name")
    
    if [ -z "$plugin_path" ]; then
        print_error "Plugin configuration not found for: $plugin_slug"
        return 1
    fi
    
    local full_path="$(dirname "$0")/$plugin_path"
    
    if [ ! -d "$full_path" ]; then
        print_error "Plugin directory not found: $full_path"
        return 1
    fi
    
    print_status "Plugin: $plugin_name"
    print_status "Path: $plugin_path"
    print_status "Main file: $main_file"
    
    # Change to plugin directory
    cd "$full_path"
    
    # Check if it's a git repository
    if [ ! -d ".git" ]; then
        print_error "Not a git repository: $full_path"
        return 1
    fi
    
    # Get current version
    if [ ! -f "$main_file" ]; then
        print_error "Main plugin file not found: $main_file"
        return 1
    fi
    
    local current_version=$(grep -E "Version:|const version" "$main_file" | head -1 | sed 's/.*[Vv]ersion[: =]*['\''"]*//' | sed 's/['\''";].*//' | tr -d ' ')
    
    print_status "Current version: $current_version → New version: $version"
    
    # Validate version format
    if ! [[ $version =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        print_error "Invalid version format. Use semantic versioning (e.g., 1.3.3)"
        return 1
    fi
    
    # Check current branch
    local current_branch=$(git branch --show-current)
    local dev_branch=$(get_plugin_config "$plugin_slug" "repository.dev_branch" || echo "dev")
    
    if [ "$current_branch" != "$dev_branch" ]; then
        print_warning "Not on $dev_branch branch. Current branch: $current_branch"
        if [ "$DRY_RUN" = false ]; then
            read -p "Switch to $dev_branch branch? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                git checkout "$dev_branch"
                git pull origin "$dev_branch"
            else
                print_error "Please switch to $dev_branch branch first"
                return 1
            fi
        fi
    fi
    
    # Generate changelog if enabled
    local changelog_entries=""
    if [ "$AUTO_CHANGELOG" = true ]; then
        print_status "Auto-generating changelog..."
        
        local last_tag=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
        local commits=""
        
        if [ -n "$last_tag" ]; then
            commits=$(git log --oneline --pretty=format:"- %s" $last_tag..HEAD | grep -v "Merge\|Version bump\|Updated changelog" | head -10)
        else
            commits=$(git log --oneline --pretty=format:"- %s" -10 | grep -v "Merge\|Version bump\|Updated changelog")
        fi
        
        if [ -n "$commits" ]; then
            changelog_entries="$commits"
        else
            changelog_entries="- Few minor bug fixes & improvements"
        fi
        
        changelog_entries="$changelog_entries\n- Few minor bug fixes & improvements"
    else
        changelog_entries="- Few minor bug fixes & improvements"
    fi
    
    print_status "Changelog entries:"
    echo -e "$changelog_entries"
    
    if [ "$DRY_RUN" = true ]; then
        print_debug "DRY RUN: Would update version in $main_file"
        print_debug "DRY RUN: Would update changelog in $readme_file"
        print_debug "DRY RUN: Would commit and push changes"
        print_debug "DRY RUN: Would create pull request"
        return 0
    fi
    
    # Update version in main file
    print_status "Updating version in $main_file..."
    
    # Handle different version patterns
    if grep -q "Version:" "$main_file"; then
        sed -i.bak "s/Version: $current_version/Version: $version/" "$main_file"
    fi
    
    if grep -q "const version" "$main_file"; then
        sed -i.bak "s/const version = '$current_version'/const version = '$version'/" "$main_file"
    fi
    
    # Remove backup file
    [ -f "$main_file.bak" ] && rm "$main_file.bak"
    
    # Update readme if it exists
    if [ -f "$readme_file" ]; then
        print_status "Updating $readme_file..."
        
        # Update stable tag
        sed -i.bak "s/Stable tag: $current_version/Stable tag: $version/" "$readme_file"
        
        # Add changelog entry
        local current_date=$(date +"%d/%m/%Y")
        local changelog_entry="= $version - $current_date =
$(echo -e "$changelog_entries")

"
        
        awk -v entry="$changelog_entry" '
        /^== Changelog ==/ {
            print $0
            print ""
            print entry
            next
        }
        {print}
        ' "$readme_file" > "$readme_file.tmp" && mv "$readme_file.tmp" "$readme_file"
        
        # Remove backup file
        [ -f "$readme_file.bak" ] && rm "$readme_file.bak"
    fi
    
    # Commit and push
    print_status "Committing changes..."
    git add "$main_file" ${readme_file:+"$readme_file"}
    git commit -m "Version bump to $version and updated changelog"
    
    print_status "Pushing to $dev_branch..."
    git push origin "$dev_branch"
    
    # Create PR
    print_status "Creating pull request..."
    local main_branch=$(get_plugin_config "$plugin_slug" "repository.main_branch" || echo "master")
    local repo_owner=$(get_plugin_config "$plugin_slug" "repository.owner")
    local repo_name=$(get_plugin_config "$plugin_slug" "repository.name")
    
    local pr_title="Release v$version"
    local pr_body="## Release v$version

### Changes:
$(echo -e "$changelog_entries")

### Files Updated:
- Version bumped to $version in main plugin file
- Updated changelog in readme file

Ready for release."
    
    if gh auth status >/dev/null 2>&1; then
        local pr_url=$(gh pr create --base "$main_branch" --head "$dev_branch" --title "$pr_title" --body "$pr_body" --json url --jq .url)
        print_success "Pull request created: $pr_url"
        
        if [ "$AUTO_MERGE" = true ]; then
            print_status "Auto-merging pull request..."
            sleep 5
            gh pr merge --merge --delete-branch=false
            print_success "Pull request merged"
            
            if [ "$CREATE_TAG" = true ]; then
                print_status "Creating git tag..."
                git checkout "$main_branch"
                git pull origin "$main_branch"
                git tag -a "v$version" -m "Release version $version"
                git push origin "v$version"
                git checkout "$dev_branch"
                print_success "Tag v$version created"
            fi
        fi
    else
        print_warning "GitHub CLI not authenticated. Please run 'gh auth login'"
        return 1
    fi
    
    print_success "✅ $plugin_name v$version released successfully!"
    
    # Return to original directory
    cd - > /dev/null
}

# Main execution
print_status "🎯 Universal Plugin Release Manager"

# Validate version format
if ! [[ $NEW_VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    print_error "Invalid version format. Use semantic versioning (e.g., 1.3.3)"
    exit 1
fi

# Check if plugin/group exists
if ! plugin_exists "$PLUGIN_SLUG"; then
    print_error "Plugin or release group not found: $PLUGIN_SLUG"
    echo ""
    echo "Available plugins:"
    python3 -c "
import json
with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)
for plugin in config.get('plugins', {}):
    print(f'  {plugin}')
print('\nAvailable release groups:')
for group in config.get('release_groups', {}):
    print(f'  {group}')
"
    exit 1
fi

if [ "$DRY_RUN" = true ]; then
    print_warning "🧪 DRY RUN MODE - No changes will be made"
fi

# Get plugins to release
plugins_to_release=$(get_plugins_from_group "$PLUGIN_SLUG")

if [ -z "$plugins_to_release" ]; then
    print_error "No plugins found for: $PLUGIN_SLUG"
    exit 1
fi

# Count plugins
plugin_count=$(echo "$plugins_to_release" | wc -l | tr -d ' ')

if [ "$plugin_count" -gt 1 ]; then
    print_status "📦 Releasing $plugin_count plugins:"
    echo "$plugins_to_release" | while read -r plugin; do
        echo "  - $plugin"
    done
    echo ""
    
    if [ "$DRY_RUN" = false ]; then
        read -p "Continue with multi-plugin release? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Release cancelled"
            exit 0
        fi
    fi
fi

# Release each plugin
success_count=0
total_count=0

echo "$plugins_to_release" | while read -r plugin; do
    if [ -n "$plugin" ]; then
        total_count=$((total_count + 1))
        echo ""
        print_status "📋 Processing plugin $total_count of $plugin_count: $plugin"
        
        if release_plugin "$plugin" "$NEW_VERSION"; then
            success_count=$((success_count + 1))
        else
            print_error "Failed to release $plugin"
        fi
    fi
done

print_success "🎉 Release process completed!"
print_status "Released version $NEW_VERSION for: $PLUGIN_SLUG"
