!function(e){var n={};function a(t){if(n[t])return n[t].exports;var d=n[t]={i:t,l:!1,exports:{}};return e[t].call(d.exports,d,d.exports,a),d.l=!0,d.exports}a.m=e,a.c=n,a.d=function(e,n,t){a.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:t})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,n){if(1&n&&(e=a(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(a.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var d in e)a.d(t,d,function(n){return e[n]}.bind(null,d));return t},a.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(n,"a",n),n},a.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},a.p="",a(a.s=1)}([,function(e,n){var a=function(e,n){var a=n(".eael-advanced-menu-container",e).data("indicator-class"),t=(n(".eael-advanced-menu-container",e).data("hamburger-icon"),n(".eael-advanced-menu-container",e).data("dropdown-indicator-class")),d=n(".eael-advanced-menu",e).hasClass("eael-advanced-menu-horizontal"),i=n(".eael-advanced-menu-container",e).data("hamburger-breakpoints"),l=n(".eael-advanced-menu-container",e).data("hamburger-device");void 0!==l&&""!==l&&null!==l||(l="tablet");var o=d?".eael-advanced-menu-horizontal":".eael-advanced-menu-vertical",r=function(e,n){var a=0;if("none"===n||void 0===n||""===n||null===n)return a;for(var t in e)t==n&&(a=e[t]);return a=a.replace(/[^0-9]/g,"")}(i,l),c=n(".eael-advanced-menu--stretch"),s=[];function u(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(window.matchMedia("(max-width: "+a+"px)").matches){if(n(o,e).addClass("eael-advanced-menu-responsive"),n(".eael-advanced-menu-toggle-text",e).text(n(o+" .current-menu-item a",e).eq(0).text()),n(".eael-advanced-menu-container",e).closest(".elementor-widget-eael-advanced-menu").removeClass("eael-hamburger--not-responsive").addClass("eael-hamburger--responsive"),c){var t={};n(o,e).parent().hasClass("eael-nav-menu-wrapper")||n(o,e).wrap('<nav class="eael-nav-menu-wrapper"></nav>');var d=n(".eael-advanced-menu-container nav",e);m(d),c.length>0&&(t.width=parseFloat(n(".elementor").width())+"px",t.left=-parseFloat(d.offset().left)+"px",t.position="absolute"),d.css(t)}}else n(o,e).removeClass("eael-advanced-menu-responsive"),n(o+", "+o+" ul",e).css("display",""),n(".eael-advanced-menu-container nav",e).removeAttr("style"),n(".eael-advanced-menu-container",e).closest(".elementor-widget-eael-advanced-menu").removeClass("eael-hamburger--responsive").addClass("eael-hamburger--not-responsive")}function m(e){var n={width:"",left:"",position:"inherit"};e.css(n)}n(".eael-advanced-menu li a",e).each((function(){var e,a=n(this),t=a.attr("href"),d=t,i=void 0!==d?d.split("#"):[];e=(t=void 0===t?"":t).startsWith("#"),"#"!==t&&i.length>1&&localize.page_permalink===i[0]&&i[1]&&s.push(i[1]),e||localize.page_permalink!==d||a.addClass("eael-item-active")})),n(window).on("load resize scroll",(function(){s.length>0&&n.each(s,(function(a,t){n("#"+t).isInViewport()?n('a[href="'+localize.page_permalink+"#"+t+'"]',e).addClass("eael-menu-"+t+" eael-item-active"):n(".eael-menu-"+t).removeClass("eael-menu-"+t+" eael-item-active")}))})),d&&(n(".eael-advanced-menu > li.menu-item-has-children",e).each((function(){n("> a",n(this)).append("<span>".concat(a,"</span>"))})),n(".eael-advanced-menu > li ul li.menu-item-has-children",e).each((function(){n("> a",n(this)).append('<span class="eael-dropdown-indicator">'.concat(t,"</span>"))}))),n(o,e).before('<span class="eael-advanced-menu-toggle-text"></span>'),u(r),n(".eael-advanced-menu-container",e).on("click",".eael-advanced-menu-toggle",(function(e){e.preventDefault();var a=n(this).siblings("nav").children(o);"none"==a.css("display")?a.slideDown(300):a.slideUp(300)})),n(window).on("resize load",(function(){u(r)})),n(".eael-advanced-menu > li.menu-item-has-children",e).each((function(){var e=parseInt(n("a",this).css("line-height"))/2;n(this).append('<span class="eael-advanced-menu-indicator" style="top: '.concat(e,'px">').concat(a,"</span>"))})),n(".eael-advanced-menu > li ul li.menu-item-has-children",e).each((function(e){var a=parseInt(n("a",this).css("line-height"))/2;n(this).append('<span class="eael-advanced-menu-indicator eael-dropdown-indicator" style="top: '.concat(a,'px">').concat(t,"</span>"))})),n(".eael-advanced-menu-dropdown-align-left .eael-advanced-menu-vertical li.menu-item-has-children").each((function(){var e=parseInt(n("a",n(this)).css("padding-left"));n("ul li a",this).css({"padding-left":e+20+"px"})})),n(".eael-advanced-menu-dropdown-align-right .eael-advanced-menu-vertical li.menu-item-has-children").each((function(){var e=parseInt(n("a",n(this)).css("padding-right"));n("ul li a",this).css({"padding-right":e+20+"px"})})),n(".eael-advanced-menu-vertical li.menu-item-has-children.current-menu-ancestor .eael-advanced-menu-indicator").each((function(){"none"!==n(this).siblings("ul.sub-menu").css("display")&&n(this).toggleClass("eael-advanced-menu-indicator-open")})),n(".eael-advanced-menu",e).on("click",'a[href="#"]',(function(e){e.preventDefault(),n(this).siblings(".eael-advanced-menu-indicator").trigger("click")})),n(".eael-advanced-menu",e).on("click",".eael-advanced-menu-indicator",(function(e){e.preventDefault(),n(this).toggleClass("eael-advanced-menu-indicator-open"),n(this).hasClass("eael-advanced-menu-indicator-open")?n(this).siblings("ul").slideDown(300):n(this).siblings("ul").slideUp(300),n(".eael-advanced-menu-indicator-open").not(n(this).parents(".menu-item-has-children").children("span")).removeClass("eael-advanced-menu-indicator-open").siblings("ul").slideUp(300)})),n(".eael-advanced-menu-container",e).on("click",'.eael-advanced-menu-responsive li a:not([href="#"])',(function(e){n(this).parents(o).slideUp(300)})),elementorFrontend.isEditMode()&&elementor.channels.editor.on("change",(function(e){e.elementSettingsModel.changed.eael_advanced_menu_dropdown&&elementor.saver.update.apply().then((function(){elementor.reloadPreview()}))}))};jQuery(window).on("elementor/frontend/init",(function(){if(eael.elementStatusCheck("eaelAdvancedMenu"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-advanced-menu.default",a),elementorFrontend.hooks.addAction("frontend/element_ready/eael-advanced-menu.skin-one",a),elementorFrontend.hooks.addAction("frontend/element_ready/eael-advanced-menu.skin-two",a),elementorFrontend.hooks.addAction("frontend/element_ready/eael-advanced-menu.skin-three",a),elementorFrontend.hooks.addAction("frontend/element_ready/eael-advanced-menu.skin-four",a),elementorFrontend.hooks.addAction("frontend/element_ready/eael-advanced-menu.skin-five",a),elementorFrontend.hooks.addAction("frontend/element_ready/eael-advanced-menu.skin-six",a),elementorFrontend.hooks.addAction("frontend/element_ready/eael-advanced-menu.skin-seven",a)}))}]);