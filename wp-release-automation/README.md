# 🌍 Universal WordPress Plugin Release Automation

**Location:** `/Users/<USER>/Sites/wp-release-automation/`

A universal release automation system that manages WordPress plugin releases across multiple development projects in your Sites folder.

## 🚀 Quick Start

### Single Plugin Release
```bash
cd /Users/<USER>/Sites/wp-release-automation
./prepare-release essential-addons-dev/better-payment 1.3.3
./prepare-release nhrrob-dev/nhrrob-movies 2.1.0
```

### Multi-Plugin Release (Release Groups)
```bash
cd /Users/<USER>/Sites/wp-release-automation
./prepare-release better-payment-suite 1.3.3      # Both Better Payment plugins
./prepare-release nhrrob-suite 2.0.0              # All NHRROB plugins
./prepare-release essential-addons-suite 6.4.0    # Both Essential Addons plugins
```

## 📁 System Location & Structure

```
/Users/<USER>/Sites/
├── wp-release-automation/              # 🎯 THIS DIRECTORY
│   ├── prepare-release                 # Simple release script (recommended)
│   ├── global-release-manager         # Advanced release manager
│   ├── global-release-config.json     # Configuration for all projects
│   └── README.md                      # This documentation
├── essential-addons-dev/              # Your Essential Addons project
│   └── wp-content/plugins/
│       ├── better-payment/
│       ├── @better-payment-pro/
│       ├── essential-addons-elementor/
│       └── essential-addons-for-elementor-lite/
└── nhrrob-dev/                        # Your NHRROB project
    └── wp-content/plugins/
        ├── @nhrrob-movies/
        ├── nhrrob-core-contributions/
        ├── nhrrob-options-table-manager/
        ├── nhrrob-frequent-update-manager/
        ├── nhrrob-hide-admin-notice/
        └── nhrrob-trello-skin-for-fluent-boards/
```

## 🎯 Configured Projects & Plugins

### 📁 essential-addons-dev Project
| Plugin Command | Plugin Name | Repository |
|----------------|-------------|------------|
| `essential-addons-dev/better-payment` | Better Payment | WPDevelopers/better-payment |
| `essential-addons-dev/better-payment-pro` | Better Payment Pro | WPDevelopers/better-payment-pro |
| `essential-addons-dev/essential-addons-elementor` | Essential Addons for Elementor | WPDevelopers/essential-addons-elementor |
| `essential-addons-dev/essential-addons-for-elementor-lite` | Essential Addons for Elementor Lite | WPDevelopers/essential-addons-for-elementor-lite |

### 📁 nhrrob-dev Project
| Plugin Command | Plugin Name | Repository |
|----------------|-------------|------------|
| `nhrrob-dev/nhrrob-movies` | NHRROB Movies | nhrrob/nhrrob-movies |
| `nhrrob-dev/nhrrob-core-contributions` | NHRROB Core Contributions | nhrrob/nhrrob-core-contributions |
| `nhrrob-dev/nhrrob-options-table-manager` | NHRROB Options Table Manager | nhrrob/nhrrob-options-table-manager |
| `nhrrob-dev/nhrrob-frequent-update-manager` | NHRROB Frequent Update Manager | nhrrob/nhrrob-frequent-update-manager |
| `nhrrob-dev/nhrrob-hide-admin-notice` | NHRROB Hide Admin Notice | nhrrob/nhrrob-hide-admin-notice |
| `nhrrob-dev/nhrrob-trello-skin-for-fluent-boards` | NHRROB Trello Skin for Fluent Boards | nhrrob/nhrrob-trello-skin-for-fluent-boards |

## 📦 Release Groups

| Group Command | Project | Plugins Included |
|---------------|---------|------------------|
| `better-payment-suite` | essential-addons-dev | better-payment, better-payment-pro |
| `essential-addons-suite` | essential-addons-dev | essential-addons-elementor, essential-addons-for-elementor-lite |
| `nhrrob-suite` | nhrrob-dev | All NHRROB plugins |
| `all-essential-addons` | essential-addons-dev | All essential-addons-dev plugins |
| `all-nhrrob` | nhrrob-dev | All nhrrob-dev plugins |

## 🛠️ Scripts

### 1. `prepare-release` (Recommended)
**Simple one-command release for any project/plugin**

```bash
./prepare-release <project>/<plugin> <version>
./prepare-release <release-group> <version>
```

### 2. `global-release-manager` (Advanced)
**Full-featured global release manager with options**

```bash
./global-release-manager <project>/<plugin> <version> [options]
```

**Options:**
- `--auto-changelog` - Auto-generate changelog (default: true)
- `--auto-merge` - Auto-merge PR after creation
- `--create-tag` - Create git tag after merge
- `--dry-run` - Show what would be done without making changes

## 🤖 AI Assistant Integration

You can tell your AI assistant:

### Single Plugin Releases
> **"Prepare release for essential-addons-dev/better-payment 1.3.4"**
> **"Prepare release for nhrrob-dev/nhrrob-movies 2.1.0"**

### Multi-Plugin Releases
> **"Prepare release for better-payment-suite 1.3.4"**
> **"Prepare release for nhrrob-suite 2.0.0"**

The assistant will automatically run:
```bash
cd /Users/<USER>/Sites/wp-release-automation
./prepare-release <target> <version>
```

## 🔄 Example Workflows

### Single Plugin Workflow
```bash
cd /Users/<USER>/Sites/wp-release-automation
./prepare-release essential-addons-dev/better-payment 1.3.3
# Review PR on GitHub → Merge → Deploy
```

### Cross-Project Workflow
```bash
cd /Users/<USER>/Sites/wp-release-automation
./prepare-release better-payment-suite 1.3.3        # Essential Addons project
./prepare-release nhrrob-suite 2.0.0                # NHRROB project
# Review all PRs → Merge → Deploy
```

### Testing Workflow
```bash
cd /Users/<USER>/Sites/wp-release-automation
./global-release-manager nhrrob-dev/nhrrob-movies 2.1.0 --dry-run
# Review what would happen → Run actual release
./prepare-release nhrrob-dev/nhrrob-movies 2.1.0
```

## ⚙️ Configuration

Edit `global-release-config.json` to:
- Add new projects in your Sites folder
- Add new plugins to existing projects
- Create new release groups
- Modify repository settings

### Adding a New Project
```json
{
  "projects": {
    "your-new-project": {
      "name": "Your New Project",
      "path": "/Users/<USER>/Sites/your-new-project",
      "plugins_path": "wp-content/plugins",
      "plugins": {
        "your-plugin": {
          "name": "Your Plugin Name",
          "path": "your-plugin-directory",
          "main_file": "your-plugin.php",
          "readme_file": "readme.txt",
          "repository": {
            "owner": "your-github-username",
            "name": "your-plugin-repo",
            "dev_branch": "dev",
            "main_branch": "master"
          }
        }
      }
    }
  }
}
```

## 🛡️ Safety Features

- **Project validation** - Checks if project paths exist in Sites folder
- **Plugin validation** - Verifies plugin directories and files
- **Version validation** - Ensures semantic versioning format
- **Git validation** - Confirms directories are git repositories
- **Cross-project support** - Handles different repository structures
- **Confirmation prompts** - Asks before making changes
- **Dry run mode** - Test without making actual changes

## 🔧 What the System Does

### Automatic Detection
- **Project paths** - Automatically navigates to correct project directories in Sites folder
- **Plugin structures** - Adapts to different plugin file patterns
- **Repository settings** - Uses project-specific git branches and remotes
- **Version patterns** - Handles various version declaration formats

### Version Updates
- Updates version in plugin headers
- Updates version constants in plugin files
- Updates stable tag in readme.txt files
- Handles different date formats and changelog styles

### Git Operations
- Switches to correct project directories
- Commits version and changelog changes
- Pushes to appropriate dev branches
- Creates pull requests to correct main branches
- Supports different repository owners and naming conventions

## 🚨 Troubleshooting

### Access the System
```bash
cd /Users/<USER>/Sites/wp-release-automation
./prepare-release --help
```

### Project Path Not Found
Check that your project exists in `/Users/<USER>/Sites/` and update `global-release-config.json`

### Plugin Directory Not Found
Verify the plugin path in the configuration matches the actual directory name

### GitHub CLI Issues
```bash
gh auth login
```

### Permission Issues
```bash
chmod +x prepare-release global-release-manager
```

## 🎯 Benefits

1. **Centralized** - One location in Sites folder manages all projects
2. **Universal** - Works with any WordPress project in Sites folder
3. **Valet/Herd Compatible** - Perfect for local development environments
4. **Flexible** - Single plugin or multi-plugin releases
5. **Safe** - Built-in validations and confirmations
6. **Scalable** - Easy to add new projects from Sites folder

## 📋 Usage Summary

**Navigate to the system:**
```bash
cd /Users/<USER>/Sites/wp-release-automation
```

**Single plugin releases:**
```bash
./prepare-release <project>/<plugin> <version>
```

**Multi-plugin releases:**
```bash
./prepare-release <release-group> <version>
```

**Testing:**
```bash
./global-release-manager <project>/<plugin> <version> --dry-run
```

This system is perfectly positioned in your Sites folder to manage all your WordPress plugin releases across multiple development projects! 🌍🚀
